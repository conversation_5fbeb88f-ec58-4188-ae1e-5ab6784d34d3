import mongoose from 'mongoose';
import slugify from 'slugify';
import { COLLECTIONS, MODELS } from '../utils/constants.js';
import BadRequestError from '../utils/response/BadRequestError.js';

export const ImageSchema = new mongoose.Schema(
  {
    public_id: { type: String },
    url: { type: String },
  },
  {
    _id: false,
  }
);

export const OptionValue = new mongoose.Schema(
  {
    name: { type: String },
    image: ImageSchema,
  },
  {
    _id: false,
  }
);

export const Option = new mongoose.Schema(
  {
    name: { type: String },
    values: [OptionValue],
  },
  {
    _id: false,
  }
);

const productSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
      minlength: 3,
      maxlength: 100,
    },
    slug: {
      type: String,
    },
    description: {
      type: String,
      default: null,
      maxlength: 1000,
    },
    price: {
      type: Number,
    },
    quantity: {
      type: Number,
    },
    thumbnail: ImageSchema,
    images: [ImageSchema],
    categoryId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: MODELS.CATEGORY,
      required: true,
    },
    categoryPath: {
      type: String,
      required: true,
    },
    options: {
      type: [Option],
      default: null,
    },
    isHide: {
      type: Boolean,
      default: false,
    },
    isDeleted: {
      type: Boolean,
      default: false,
    },
    ratingAvg: {
      type: Number,
      default: 0,
    },
    ratingCount: {
      type: Number,
      default: 0,
    },
  },
  {
    collection: MODELS.PRODUCT,
    timestamps: true,
    toJSON: {
      transform: function (doc, ret) {
        delete ret.isDeleted;
        delete ret.__v;
        delete ret.createdAt;
        delete ret.updatedAt;
        return ret;
      },
    },
    toObject: {
      transform: function (doc, ret) {
        delete ret.isDeleted;
        delete ret.__v;
        delete ret.createdAt;
        delete ret.updatedAt;
        return ret;
      },
    },
  }
);

productSchema.index(
  {
    name: 'text',
    description: 'text',
  },
  {
    weights: {
      name: 2,
      description: 1,
    },
    default_language: 'none',
  }
);

productSchema.index({ categoryPath: 1 });
productSchema.index({ name: 1, isDeleted: 1 });

productSchema.pre('save', async function (next) {
  this.slug = slugify(this.name.replace('&', ''), {
    lower: true,
    strict: true,
    locale: 'vi',
  });
  
  // check name uniqueness, with document is not soft-deleted
  if (this.isNew || this.isModified('name')) {
    const existingName = await this.constructor.findOne({
      name: this.name,
      isDeleted: false,
      _id: { $ne: this._id }, // exclude the current document itself when updating
    });
    if (existingName) {
      return next(new BadRequestError('Tên sản phẩm đã tồn tại'));
    }
  }
  next();
});

const Product = mongoose.model(
  MODELS.PRODUCT,
  productSchema,
  COLLECTIONS.PRODUCT
);

export default Product;
