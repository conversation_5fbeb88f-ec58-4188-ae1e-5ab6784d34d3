import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import ComboApi from '../../../api/ComboApi';
import { Image, Typography, Spin, Button, message, Card, Row, Col, Tag, Divider } from 'antd';
import { formatPrice } from '../../../utils/formatPrice';
import { ShoppingCartOutlined, GiftOutlined, TagOutlined, CalendarOutlined } from '@ant-design/icons';
import { useDispatch, useSelector } from 'react-redux';
import { addToCart } from '../../../redux/slices/cartSlice';
import s from './styles.module.css';
import ProductImageSlider from '../../../components/ProductImagesSlider';
import HeartIcon from '../../../components/Wishlist/HeartIcon.jsx';
import ReviewList from '../../../components/Review/ReviewList.jsx';
import ReviewForm from '../../../components/Review/ReviewForm.jsx';
import CommentList from '../../../components/Comment/CommentList.jsx';
import CommentForm from '../../../components/Comment/CommentForm.jsx';
import OrderApi from '../../../api/OrderApi.js';
import ReviewApi from '../../../api/ReviewApi.js';

const { Title, Text, Paragraph } = Typography;

const ComboDetail = () => {
  const { id } = useParams();
  const [combo, setCombo] = useState(null);
  const [selectedImage, setSelectedImage] = useState(null);
  const [loading, setLoading] = useState(false);
  const [quantity, setQuantity] = useState(1);
  const [refreshReviews, setRefreshReviews] = useState(0);
  const [canReview, setCanReview] = useState(false);
  const dispatch = useDispatch();
  const { userInfo } = useSelector((state) => state.user);

  useEffect(() => {
    const fetchDetail = async () => {
      setLoading(true);
      try {
        const data = await ComboApi.getComboById(id);
        setCombo(data);
      } catch (error) {
        console.error('Failed to load combo detail', error);
        message.error('Không thể tải thông tin combo');
      } finally {
        setLoading(false);
      }
    };
    fetchDetail();
  }, [id]);

  // Set default selected image when combo loaded
  useEffect(() => {
    if (combo?.thumbnail && !selectedImage) {
      setSelectedImage(combo.thumbnail);
    }
  }, [combo, selectedImage]);

  // Determine review eligibility
  useEffect(() => {
    const checkEligibility = async () => {
      if (!userInfo?.userId || !combo?._id) {
        setCanReview(false);
        return;
      }
      try {
        // Check if already reviewed
        const resReview = await ReviewApi.getReviews({ comboId: combo._id, userId: userInfo.userId });
        const reviewed = (resReview.data?.data?.rows || []).length > 0;
        if (reviewed) {
          setCanReview(false);
          return;
        }

        // Check delivered orders contain combo
        const resOrders = await OrderApi.getOrderListByCustomer('DELIVERED', '', 1, 50);
        const orders = resOrders.data?.data?.rows || resOrders.data?.rows || [];
        const purchased = orders.some(order => order.items?.some(it => it.comboId === combo._id));
        setCanReview(purchased);
      } catch (err) {
        console.error(err);
        setCanReview(false);
      }
    };
    checkEligibility();
  }, [userInfo, combo, refreshReviews]);

  if (loading || !combo) return <Spin size="large" style={{ margin: 20 }} />;

  const hasDiscount = combo.discountPrice && combo.discountPrice < combo.price;
  const savings = hasDiscount ? combo.price - combo.discountPrice : 0;
  const discountPercent = hasDiscount ? Math.round((savings / combo.price) * 100) : 0;
  
  // Calculate individual products total
  const individualTotal = combo.products?.reduce((total, item) => 
    total + (item.productId?.price || 0) * item.quantity, 0
  ) || 0;
  
  const comboPrice = combo.discountPrice || combo.price;
  const totalSavings = individualTotal - comboPrice;
  const totalSavingsPercent = individualTotal > 0 ? Math.round((totalSavings / individualTotal) * 100) : 0;

  const handleAddToCart = async () => {
    if (quantity <= 0) return;
    try {
      await dispatch(addToCart({ item: { comboId: combo._id, quantity } }));
      message.success('Đã thêm combo vào giỏ hàng!');
    } catch (error) {
      console.error(error);
      message.error('Lỗi khi thêm vào giỏ hàng');
    }
  };

  return (
    <div className={s.containerFather}>
      <div className={s.container}>
        <div className={s.left}>
          <ProductImageSlider
            images={combo.images || []}
            options={[]}
            thumbnail={combo.thumbnail || {}}
            onImageSelect={setSelectedImage}
            selectedImageOnDetail={selectedImage}
          />
        </div>

        <div className={s.right}>
          <div>
            {/* Header Section */}
            <div style={{ marginBottom: 16 }}>
              <Tag color="green" icon={<GiftOutlined />} style={{ marginBottom: 8 }}>
                COMBO ĐẶC BIỆT
              </Tag>
              <div style={{ display:'flex', alignItems:'center', gap: 12 }}>
                <Title className={s.title} level={2} style={{ margin: 0, flex: 1 }}>{combo.name}</Title>
                <div className={s.heartIcon}>
                  <HeartIcon combo={combo} />
                </div>
              </div>
            </div>

            {/* Price Section */}
            <Card className={s.priceCard} size="small">
              <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <div>
                  {hasDiscount ? (
                    <>
                      <Text delete className={s.oldPrice}>{formatPrice(combo.price)}đ</Text>
                      <br />
                      <Text className={s.price} strong>{formatPrice(combo.discountPrice)}đ</Text>
                    </>
                  ) : (
                    <Text className={s.price} strong>{formatPrice(combo.price)}đ</Text>
                  )}
                </div>
                {hasDiscount && (
                  <Tag color="red" style={{ fontSize: 14, padding: '4px 8px' }}>
                    -{discountPercent}%
                  </Tag>
                )}
              </div>
              
              {totalSavings > 0 && (
                <div style={{ marginTop: 12, padding: 12, background: '#f6ffed', borderRadius: 6, border: '1px solid #b7eb8f' }}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                    <TagOutlined style={{ color: '#52c41a' }} />
                    <Text strong style={{ color: '#52c41a' }}>
                      Tiết kiệm {formatPrice(totalSavings)}đ ({totalSavingsPercent}%) so với mua lẻ!
                    </Text>
                  </div>
                  <Text type="secondary" style={{ fontSize: 12 }}>
                    Tổng giá lẻ: {formatPrice(individualTotal)}đ
                  </Text>
                </div>
              )}
            </Card>

            {/* Description */}
            <div className={s.description}>
              <Title level={5}>Mô tả combo</Title>
              <Paragraph>{combo.description}</Paragraph>
            </div>

            {/* Products Section */}
            <div className={s.productsSection}>
              <div className={s.sectionTitle}>
                <GiftOutlined /> Sản phẩm trong combo ({combo.products?.length || 0} món)
              </div>
              
              <Row gutter={[12, 12]}>
                {combo.products?.map((item, index) => (
                  <Col span={24} key={index}>
                    <Card size="small" className={s.productCard}>
                      <div className={s.productItem}>
                        <div className={s.productAvatar}>
                          <Image 
                            src={item.productId?.thumbnail?.url} 
                            width={60} 
                            height={60} 
                            style={{ borderRadius: 6 }}
                            placeholder
                          />
                        </div>
                        <div className={s.productInfo}>
                          <div className={s.productName}>{item.productId?.name}</div>
                          <div className={s.productQuantity}>
                            Số lượng: <strong>{item.quantity}</strong>
                          </div>
                          <div style={{ color: '#666', fontSize: 12 }}>
                            Đơn giá: {formatPrice(item.productId?.price)}đ
                          </div>
                        </div>
                        <div className={s.productPrice}>
                          {formatPrice((item.productId?.price || 0) * item.quantity)}đ
                        </div>
                      </div>
                    </Card>
                  </Col>
                ))}
              </Row>

              {combo.services && combo.services.length > 0 && (
                <div style={{ marginTop: 20 }}>
                  <div className={s.sectionTitle}>Dịch vụ bao gồm</div>
                  {combo.services.map((item, index) => (
                    <Card size="small" key={index} style={{ marginBottom: 8 }}>
                      <div className={s.productItem}>
                        <div className={s.productInfo}>
                          <div className={s.productName}>{item.serviceId?.name}</div>
                          <div className={s.productQuantity}>Số lượng: {item.quantity}</div>
                        </div>
                        <div className={s.productPrice}>
                          {formatPrice(item.serviceId?.price)}đ
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              )}
            </div>

            <Divider />

            {/* Combo Info */}
            <Row gutter={16} style={{ marginBottom: 20 }}>
              <Col span={12}>
                <Text type="secondary">
                  <CalendarOutlined /> Ngày tạo: {new Date(combo.createdAt).toLocaleDateString('vi-VN')}
                </Text>
              </Col>
              <Col span={12}>
                <Tag color={combo.status === 'ACTIVE' ? 'green' : 'red'}>
                  {combo.status === 'ACTIVE' ? 'Đang bán' : 'Ngừng bán'}
                </Tag>
              </Col>
            </Row>

            {/* Cart Action */}
            <div className={s.cartAction}>
              <div className={s.quantity}>
                <button 
                  onClick={() => setQuantity(q => Math.max(q - 1, 1))}
                  disabled={quantity <= 1}
                >
                  −
                </button>
                <input
                  className={s.quantityInput}
                  type="text"
                  value={quantity}
                  onChange={(e) => {
                    const val = e.target.value;
                    if (/^\d*$/.test(val)) {
                      setQuantity(val === '' ? 1 : parseInt(val, 10));
                    }
                  }}
                />
                <button onClick={() => setQuantity(q => q + 1)}>+</button>
              </div>

              <Button 
                type="primary" 
                icon={<ShoppingCartOutlined />} 
                className={s.addToCart} 
                onClick={handleAddToCart}
                disabled={combo.status !== 'ACTIVE'}
                size="large"
              >
                Thêm vào giỏ - {formatPrice(comboPrice * quantity)}đ
              </Button>
            </div>

            {combo.status !== 'ACTIVE' && (
              <Text type="secondary" style={{ fontSize: 12, fontStyle: 'italic' }}>
                * Combo này hiện không còn được bán
              </Text>
            )}
          </div>
        </div>
      </div>

      {/* Reviews Section */}
      <div className={s.reviewSection} style={{ marginTop: 40 }}>
        <Title level={4}>Đánh giá combo</Title>
        {/* Review Form - Only show if user can review */}
        {userInfo?.accountId && canReview && (
          <ReviewForm target={{ id: combo._id, type:'combo' }} onSuccess={() => setRefreshReviews(r => r + 1)} />
        )}
        {/* Review List */}
        <ReviewList target={{ id: combo._id, type:'combo' }} refreshFlag={refreshReviews} />
      </div>

      {/* Comments Section */}
      <div style={{ marginTop: 40 }}>
        <Title level={4}>Bình luận</Title>
        {userInfo?.accountId && (
          <CommentForm target={{ id: combo._id, type:'combo' }} afterSubmit={() => {}} />
        )}
        <CommentList target={{ id: combo._id, type:'combo' }} />
      </div>
    </div>
  );
};

export default ComboDetail;