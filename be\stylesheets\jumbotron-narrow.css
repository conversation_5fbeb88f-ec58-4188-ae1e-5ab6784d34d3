/* Space out content a bit */
body {
  padding-top: 20px;
  padding-bottom: 20px;
}

/* Everything but the jumbotron gets side spacing for mobile first views */
.header,
.marketing,
.footer {
  padding-right: 15px;
  padding-left: 15px;
}

/* Custom page header */
.header {
  padding-bottom: 20px;
  border-bottom: 1px solid #e5e5e5;
}
/* Make the masthead heading the same height as the navigation */
.header h3 {
  margin-top: 0;
  margin-bottom: 0;
  line-height: 40px;
}

/* Custom page footer */
.footer {
  padding-top: 19px;
  color: #777;
  border-top: 1px solid #e5e5e5;
}

/* Customize container */
@media (min-width: 768px) {
  .container {
    max-width: 970px;
  }
}
.container-narrow > hr {
  margin: 30px 0;
}

/* Main marketing message and sign up button */
.jumbotron {
  text-align: center;
  border-bottom: 1px solid #e5e5e5;
}
.jumbotron .btn {
  padding: 14px 24px;
  font-size: 21px;
}

/* Supporting marketing content */
.marketing {
  margin: 40px 0;
}
.marketing p + h4 {
  margin-top: 28px;
}

/* Responsive: Portrait tablets and up */
@media screen and (min-width: 768px) {
  /* Remove the padding we set earlier */
  .header,
  .marketing,
  .footer {
    padding-right: 0;
    padding-left: 0;
  }
  /* Space out the masthead */
  .header {
    margin-bottom: 30px;
  }
  /* Remove the bottom border on the jumbotron for visual effect */
  .jumbotron {
    border-bottom: 0;
  }
}
.pay-success{ color: blue;}
.pay-unsuccess{ color:black;}
.pay-error{ color:red;}
.footer{text-align:center}
/* Pager */
.pager
{
	margin: 8px 3px;
	padding: 3px;
}

.pager .disabled
{
	border: 1px solid #ddd;
	color: #999;
	margin-top: 4px;
	padding: 3px;
	text-align: center;
}

.pager .current
{
	background-color: #6ea9bf;
	border: 1px solid #6e99aa;
	color: #fff;
	font-weight: bold;
	margin-top: 4px;
	padding: 3px 5px;
	text-align: center;
}

.pager span, .pager a
{
	margin: 4px 3px;
}

.pager a
{
	border: 1px solid #aaa;
	padding: 3px 5px;
	text-align: center;
	text-decoration: none;
}