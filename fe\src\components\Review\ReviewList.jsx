import { useEffect, useState } from 'react';
import { List, Avatar, Spin, message } from 'antd';
import RatingStars from './RatingStars.jsx';
import ReviewApi from '../../api/ReviewApi.js';

const ReviewList = ({ productId, target = null, refreshFlag }) => {
  const [loading, setLoading] = useState(false);
  const [reviews, setReviews] = useState([]);
  const [pagination, setPagination] = useState({ page: 1, pageSize: 5, total: 0 });

  const fetchData = async (page = 1) => {
    setLoading(true);
    try {
      const params = target ? (target.type==='combo' ? { comboId: target.id } : { productId: target.id }) : { productId };
      const res = await ReviewApi.getReviews({ ...params, page, pageSize: pagination.pageSize });
      const payload = res.data?.data || {};
      setReviews(payload.rows || []);
      setPagination((prev) => ({ ...prev, page, total: payload.count || 0 }));
    } catch (err) {
      message.error('Không tải được đ<PERSON> giá');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData(1);
    // eslint-disable-next-line
  }, [productId, refreshFlag]);

  const handlePageChange = (p) => {
    fetchData(p);
  };

  if (loading) return <Spin />;

  return (
    <List
      itemLayout="vertical"
      dataSource={reviews}
      pagination={{
        current: pagination.page,
        pageSize: pagination.pageSize,
        total: pagination.total,
        onChange: handlePageChange,
        hideOnSinglePage: true,
      }}
      renderItem={(item) => (
        <List.Item key={item._id}>
          <List.Item.Meta
            avatar={<Avatar>{item.userId?.fullName?.charAt(0) || 'U'}</Avatar>}
            title={<RatingStars value={item.rating} />}
            description={new Date(item.createdAt).toLocaleDateString('vi-VN')}
          />
          <div>{item.comment}</div>
        </List.Item>
      )}
    />
  );
};

export default ReviewList; 