.pageWrapper {
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  padding-bottom: 30px;
  background-image: url('../images/login-background (1).png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  overflow-y: hidden;
  box-sizing: border-box;
}

.loginCard {
  margin-top: 50px;
  max-width: 700px;
  width: 100%;
  border-radius: 16px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  overflow: hidden;
  display: flex;
}

.formSection {
  flex: 1;
  padding: 30px;
  background-color: #ffffff;
}

.formTitle {
  color: #1f2937;
  margin-bottom: 32px;
  font-weight: 700;
  font-size: 34px;
  text-align: center;
}

.authForm .formItem {
  margin-bottom: 24px;
}

.formInput {
  height: 45px;
  border-radius: 8px;
}

.formInput:hover,
.formInput:focus {
  border-color: #4096ff;
  box-shadow: 0 0 0 2px rgba(64, 150, 255, 0.1);
}

.inputPrefixIcon {
  color: #9ca3af;
}

.submitButton {
  height: 45px;
  border-radius: 8px;
  font-weight: 500;
  font-size: 16px;
  background: linear-gradient(90deg, #4096ff 0%, #1677ff 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(64, 150, 255, 0.25);
  transition: all 0.3s ease;
}

.submitButton:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(64, 150, 255, 0.35);
}

.submitButton:active {
  transform: translateY(0);
}

.imageSection {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #e6f3ff 0%, #f0f5ff 100%);
  width: 100%;
  overflow: hidden;
}

.sectionImage {
  width: 140%;
  height: 100%;
  object-fit: cover;
}


.forgotPassword {
  text-align: right;
  margin-bottom: 8px;
}

.forgotPasswordLink {
  font-size: 14px;
  color: #1890ff;
  text-decoration: none;
}

.forgotPasswordLink:hover {
  text-decoration: underline;
}


.signupPrompt {
  text-align: center;
  margin-top: 16px;
}

.signupLink {
  font-weight: 500;
  color: #1677ff;
  text-decoration: none;
  cursor: pointer;
}

.signupLink:hover {
  text-decoration: underline;
}

.divider {
  margin: 24px 0;
}

.googleButton {
  background: #ffffff;
  color: #757575;
  border: 1px solid #ddd;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
}

.googleButton:hover {
  background: #f5f5f5;
  border-color: #ccc;
}

@media (max-width: 768px) {
  .loginCard {
    width: 95%;
    max-width: 480px;
  }

  .loginCard .ant-card-body {
    flex-direction: column;
  }

  .imageSection {
    height: 200px;
    padding: 0;
  }

  .sectionImage {
    height: 100%;
    width: auto;
  }
}

.errorMessage {
  font-size: 13px;
  margin-top: 4px;
  color: #ff4d4f;
}

.inputFocused {
  border-color: #4096ff;
  box-shadow: 0 0 0 2px rgba(64, 150, 255, 0.1);
}