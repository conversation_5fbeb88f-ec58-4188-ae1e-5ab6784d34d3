import express from "express";
import { authController } from "../controllers/index.js";
import validate from "../middlewares/validates.js";
import authValidation from "../validations/auth.validation.js";
import { verifyToken } from "../middlewares/authenticateJWT.js";

const authRouter = express.Router();

// Route không yêu cầu xác thực
authRouter.post("/login", validate(authValidation.loginValidation), authController.login);
authRouter.post("/register", validate(authValidation.registerValidation), authController.register);
authRouter.post("/google", authController.loginWithGoogle);
authRouter.post("/active", authController.activeUserByEmail);
authRouter.post("/refresh-token", authController.refreshToken);
authRouter.put("/forgot-pw", validate(authValidation.forgotPassword), authController.forgotPassword);
authRouter.put("/reset-password", validate(authValidation.resetPassword), authController.resetPassword);

authRouter.put("/logout", validate(authValidation.logout), authController.logout);
authRouter.put("/change-password", verifyToken, validate(authValidation.changePassword), authController.changePassword);
authRouter.put("/", verifyToken, validate(authValidation.editAccount), authController.editAccount);
authRouter.post("/checkTokenExprided", validate(authValidation.checkTokenExprided), authController.checkTokenExprided);



export default authRouter;
