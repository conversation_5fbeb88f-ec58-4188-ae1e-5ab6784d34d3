import { useEffect, useState } from "react";
import { <PERSON><PERSON>, Row, Col } from "antd";
import { Loading3QuartersOutlined, ShoppingCartOutlined } from "@ant-design/icons";
import HeartIcon from "../../../components/Wishlist/HeartIcon";
import s from "./styles.module.css";
import { useNavigate, useParams } from "react-router-dom";
import { viewDataDetail, viewData } from "../../../redux/slices/productSlice";
import { addToCart } from "../../../redux/slices/cartSlice";
import { useDispatch, useSelector } from "react-redux";
import ProductImageSlider from "../../../components/ProductImagesSlider";
import CustomCard from "../../../components/Card";
import { formatPrice } from "../../../utils/formatPrice";
import ReviewForm from "../../../components/Review/ReviewForm.jsx";
import ReviewList from "../../../components/Review/ReviewList.jsx";
import OrderApi from "../../../api/OrderApi.js";
import ReviewApi from "../../../api/ReviewApi.js";
import CommentSection from "../../../components/Comment/CommentSection.jsx";

const ProductDetail = () => {
    const { id } = useParams();
    const dispatch = useDispatch();
    const { productDetailData, productTableData } = useSelector((state) => state.product);
    const [selectedOptionIndexs, setSelectedOptionIndexs] = useState([]);
    const [selectedVariant, setSelectedVariant] = useState(null);
    const [selectedImage, setSelectedImage] = useState(null);
    const [products, setProducts] = useState([]);
    const [quantity, setQuantity] = useState(1);
    const { userInfo } = useSelector((state) => state.user);
    const navigate = useNavigate();
    const [refreshReviews, setRefreshReviews] = useState(0);
    const [canReview, setCanReview] = useState(false);

    const handleOptionSelect = (optionIndex, valueIndex) => {
        if (!productDetailData?.product?.options?.length || !productDetailData?.variants?.length) {
            return;
        }
        const newSelectedOptions = [...selectedOptionIndexs];
        newSelectedOptions[optionIndex] = valueIndex;
        setSelectedOptionIndexs(newSelectedOptions);
        const matchedVariant = productDetailData.variants.find(variant => {
            const variantString = JSON.stringify(variant.optionIndex);
            const selectedString = JSON.stringify(newSelectedOptions);
            return variantString === selectedString;
        });
        if (matchedVariant) {
            setSelectedVariant(matchedVariant);
            const selectedOption = productDetailData.product.options[optionIndex];
            if (selectedOption?.values[valueIndex]?.image?.url) {
                setSelectedImage(selectedOption.values[valueIndex].image);
            }
        } else {
            setSelectedVariant(null);
            setSelectedImage(null);
        }
    };

    useEffect(() => {
        dispatch(viewDataDetail(id));
    }, [id, dispatch]);

    useEffect(() => {
        if (productDetailData?.product?.categoryId?._id) {
            dispatch(viewData({ categoryIds: productDetailData.product.categoryId._id, page: 1, pageSize: 4 }));
        }

        if (productDetailData?.product?.options?.length) {
            const defaultSelectedOptions = Array(productDetailData.product.options.length).fill(0);
            setSelectedOptionIndexs(defaultSelectedOptions);

            const matchedVariant = productDetailData.variants?.find((variant) =>
                JSON.stringify(variant.optionIndex || []) === JSON.stringify(defaultSelectedOptions)
            );

            if (matchedVariant) {
                setSelectedVariant(matchedVariant);
            } else {
                setSelectedVariant(null);
            }


            if (productDetailData.product.options[0]?.values[0]?.image?.url) {
                setSelectedImage(productDetailData.product.options[0].values[0].image);
            } else if (productDetailData?.product?.thumbnail?.url) {
                setSelectedImage(productDetailData.product.thumbnail);
            }
        } else if (productDetailData?.product?.thumbnail?.url) {

            setSelectedImage(productDetailData.product.thumbnail);
            setSelectedOptionIndexs([]); // Reset options nếu không có
            setSelectedVariant(null); // Reset variant nếu không có
        }
    }, [productDetailData, dispatch]);

    useEffect(() => {
        if (productTableData?.items) {
            setProducts(productTableData.items);
        }
    }, [productTableData]);

    useEffect(() => {
        if (productDetailData?.product?.thumbnail?.url && !selectedImage) {
            setSelectedImage(productDetailData.product.thumbnail);
        }
    }, [productDetailData, selectedImage]);

    // Determine review eligibility
    useEffect(() => {
        const checkEligibility = async () => {
            if (!userInfo?.userId || !productDetailData?.product?._id) {
                setCanReview(false);
                return;
            }
            try {
                // Check already reviewed
                const resReview = await ReviewApi.getReviews({ productId: productDetailData.product._id, userId: userInfo.userId });
                const reviewed = (resReview.data?.data?.rows || []).length > 0;
                if (reviewed) {
                    setCanReview(false);
                    return;
                }

                // Check delivered orders contain product
                const resOrders = await OrderApi.getOrderListByCustomer('DELIVERED', '', 1, 50);
                const orders = resOrders.data?.data?.rows || resOrders.data?.rows || [];
                const purchased = orders.some(order => order.items?.some(it => it.productId === productDetailData.product._id));
                setCanReview(purchased);
            } catch (err) {
                console.error(err);
                setCanReview(false);
            }
        };
        checkEligibility();
    }, [userInfo, productDetailData, refreshReviews]);

    if (!productDetailData || !productDetailData.product) {
        return <div>Loading...</div>;
    }
    const handleAddToCart = () => {

        if (!userInfo?.accountId) {
            navigate("/login");
            return;
        }
        if (selectedVariant) {
            dispatch(
                addToCart({
                    item: { productId: productDetailData.product._id, variantId: selectedVariant._id, quantity },
                })
            );
        } else {
            dispatch(
                addToCart({
                    item: { productId: productDetailData.product._id, quantity },
                })
            );
        }

    };
    return productDetailData && productDetailData.product.name ? (

        <div className={s.containerFather}>
            <div className={s.container}>
                <div className={s.left}>
                    {productDetailData && productDetailData?.product?.thumbnail &&
                        <ProductImageSlider
                            images={productDetailData?.product?.images || []}
                            options={productDetailData?.product?.options || []}
                            thumbnail={productDetailData?.product?.thumbnail || {}}
                            onImageSelect={setSelectedImage}
                            selectedImageOnDetail={selectedImage}
                        />
                    }
                </div>

                <div className={s.right}>
                    <div style={{ display: "flex", alignItems: "center", gap: 12 }}>
                        <h1 className={s.title} style={{ margin: 0, flex: 1 }}>{productDetailData?.product?.name}</h1>
                        <div className={s.heartIcon}>
                            <HeartIcon product={productDetailData.product} />
                        </div>
                    </div>
                    <div>
                        <span className={s.statusQuantity}>
                            <p className={s.label}>Tình trạng:&nbsp;</p>
                            <p className={s.value}>
                                {selectedVariant
                                    ? selectedVariant.quantity > 0
                                        ? `Còn ${selectedVariant.quantity} sản phẩm`
                                        : "Hết hàng"
                                    : productDetailData.product.quantity > 0
                                        ? `Còn ${productDetailData.product.quantity} sản phẩm`
                                        : "Hết hàng"
                                }
                            </p>
                        </span>
                    </div>

                    <p className={s.price}>
                        {selectedVariant?.price ? formatPrice(selectedVariant.price) : formatPrice(productDetailData?.product?.price)}₫
                    </p>
                    {productDetailData.product?.options?.length > 0 &&
                        productDetailData.product.options?.map((option, optionIndex) => (
                            <div key={`option-${optionIndex}`} className={s.options}>
                                <h4>{option.name}:</h4>
                                {option.values?.map((value, valueIndex) => (
                                    <button
                                        key={`value-${valueIndex}`}
                                        className={`${s.optionBtn} ${selectedOptionIndexs[optionIndex] === valueIndex ? s.active : ""
                                            }`}
                                        onClick={() => handleOptionSelect(optionIndex, valueIndex)}
                                        disabled={!value || selectedOptionIndexs[optionIndex] === valueIndex}
                                    >
                                        {value?.name || "Không có giá trị"}
                                    </button>
                                ))}
                            </div>
                        ))}

                    <div className={s.cartAction}>
                        <div className={s.quantity}>
                            <button
                                onClick={() => setQuantity(q => Math.max(q - 1, 1))}
                                disabled={selectedVariant?.quantity === 0}
                            >
                                -
                            </button>
                            <input
                                className={s.quantityInput}
                                type="text"
                                value={quantity}
                                onChange={(e) => {
                                    const newValue = e.target.value;
                                    if (/^\d*$/.test(newValue)) {
                                        setQuantity(newValue);
                                    }
                                }}
                            />

                            <button
                                onClick={() => setQuantity(q => q + 1)}
                                disabled={selectedVariant?.quantity === 0}
                            >
                                +
                            </button>
                        </div>

                        <Button
                            className={s.addToCart}
                            type="primary"
                            icon={<ShoppingCartOutlined />}
                            onClick={handleAddToCart}
                            disabled={
                                productDetailData.product?.options?.length > 0
                                    ? selectedVariant?.quantity === 0 || quantity > selectedVariant?.quantity
                                    : productDetailData.product?.quantity === 0 || quantity > productDetailData.product?.quantity
                            }
                        >
                            {productDetailData.product?.options?.length > 0
                                ? quantity > selectedVariant?.quantity && selectedVariant?.quantity > 0
                                    ? "Tạm thời hết hàng"
                                    : "Thêm vào giỏ"
                                : quantity > productDetailData.product?.quantity && productDetailData.product?.quantity > 0
                                    ? "Tạm thời hết hàng"
                                    : "Thêm vào giỏ"
                            }
                        </Button>
                    </div>
                    {productDetailData.product?.description}
                </div>
            </div>
            
            {/* Reviews Section */}
            <div className={s.reviewSection} style={{ marginTop: 40 }}>
                <h2>Đánh giá sản phẩm</h2>
                {/* Review Form - Only show if user can review */}
                {userInfo?.accountId && canReview && (
                    <ReviewForm productId={productDetailData.product._id} onSuccess={() => setRefreshReviews(r => r + 1)} />
                )}
                {/* Review List */}
                <ReviewList productId={productDetailData.product._id} refreshFlag={refreshReviews} />
            </div>
            
            {/* Comments Section */}
            <div style={{ marginTop: 40 }}>
                <CommentSection productId={productDetailData.product._id} />
            </div>
            
            {/* Related Products */}
            <div className={s.relatedProducts} style={{ marginTop: 40 }}>
                <h2 className={s.titleRelatedProducts}>Sản phẩm liên quan</h2>
                <Row className={s.productList}>
                    {products.map((product) => (
                        <Col span={5} key={product._id} style={{ margin: " 0 4px" }}>
                            <CustomCard product={product} />
                        </Col>
                    ))}
                </Row>
            </div>
        </div>
    ) : <Loading3QuartersOutlined />;
};

export default ProductDetail;
