import { HttpStatusCode } from "axios";
import Account from "../models/account.model.js";
import { ErrorCodes } from "../constants/errorCode.js";
import accountRepository from "./account.repository.js";
import bcrypt from "bcryptjs";


const findUserByEmail = async (email) => {
  const account = await Account.findOne({ email: email }).populate("userId");
  return account || null;
};

const register = async (data) => {
  try {
    const { email, password, userId, activation } = data;

    const dataRegister = {
      email: email,
      password: password,
      userId: userId,
      activation
    }

    const res = await Account.create(dataRegister);
    return ({
      status: HttpStatusCode.Ok,
      message: ErrorCodes.EC015
    })

  } catch (error) {

    return ({
      status: HttpStatusCode.BadRequest,
      message: error.message
    })
  }
};



const updateTokenByAccountId = async (accountId, token) => {
  const account = await accountRepository.getByAccountId(accountId);
  account.refreshToken = token;
  account.save()
}

const updateResetPWTokenByAccountId = async (accountId, resetPWToken) => {
  const account = await accountRepository.getByAccountId(accountId);
  account.resetPWToken = resetPWToken;
  account.save()
}


const comparePassword = async (plainPassword, hashedPassword) => {
  try {
    return await bcrypt.compare(plainPassword, hashedPassword);
  } catch (error) {
    console.error("Error comparing passwords:", error);
    return false;
  }
};

const updatePassword = async (email, hashedPassword) => {
  try {
    await Account.findOneAndUpdate({ email }, { password: hashedPassword });
  } catch (error) {
    return false;
  }
};




export default { findUserByEmail, register, updateTokenByAccountId, updateResetPWTokenByAccountId, comparePassword, updatePassword };
