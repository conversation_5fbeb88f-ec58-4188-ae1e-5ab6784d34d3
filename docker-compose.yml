version: '3.8'

services:
  fe:
    image: node:20
    working_dir: /app
    volumes:
      - ./fe:/app
    ports:
      - "5173:5173"
    command: sh -c "npm install && npm start"
    networks:
      - mern-app
  be:
    image: node:20
    working_dir: /app
    volumes:
      - ./be:/app
    ports:
      - "3000:3000"
    depends_on:
      - mongodb
    networks:
      - mern-app
    environment:
      MONGO_URL: mongodb://mongodb:27017/PetHaven
    command: sh -c "npm install && npm run dev"

  mongodb:
    image: mongo:latest
    ports:
      - "27017:27017"
    volumes:
      - mongo-data:/data/db
    networks:
      - mern-app
networks:
  mern-app:
      driver: bridge
volumes:
  mongo-data:
    driver: local