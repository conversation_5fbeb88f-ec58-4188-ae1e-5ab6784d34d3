import Cart from '../models/cart.model.js';
import NotFoundError from '../utils/response/NotFoundError.js';

const findByAccountId = async (accountId) => await Cart.findOne({ accountId })
  .populate('items.productId', 'name price thumbnail options')
  .populate('items.comboId', 'name price discountPrice thumbnail')
  .populate('items.variantId', 'price optionIndex');

const findById = async (id) =>
  await Cart.findById(id)
    .populate('items.productId', 'name price thumbnail options')
    .populate('items.comboId', 'name price discountPrice thumbnail')
    .populate('items.variantId', 'price optionIndex');

const create = async (data, options) => await Cart.create(data, options);

const update = async (id, data, options) =>
  await Cart.findByIdAndUpdate(id, data, options);

const deleteCartItems = async (accountId, items, options) => {
  const cart = await Cart.findOne({ accountId });

  if (!cart) {
    return new NotFoundError('Cart not found');
  }

  const updatedItems = cart.items.filter((cartItem) => {
    return !items.some((del) => {
      // Combo case
      if (del.comboId) {
        if (!cartItem.comboId) return false;
        const cartItemId = cartItem.comboId._id ? cartItem.comboId._id.toString() : cartItem.comboId.toString();
        return cartItemId === del.comboId.toString();
      }

      // Product/variant case
      if (del.variantId) {
        return (
          cartItem.productId && 
          (cartItem.productId._id ? cartItem.productId._id.toString() : cartItem.productId.toString()) === del.productId.toString() &&
          cartItem.variantId && 
          (cartItem.variantId._id ? cartItem.variantId._id.toString() : cartItem.variantId.toString()) === del.variantId.toString()
        );
      }
      // Only productId
      return cartItem.productId && 
        (cartItem.productId._id ? cartItem.productId._id.toString() : cartItem.productId.toString()) === del.productId.toString();
    });
  });

  cart.items = updatedItems;

  return await cart.save(options);
};

export default { findById, findByAccountId, create, update, deleteCartItems };
