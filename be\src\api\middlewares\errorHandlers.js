import createError from "http-errors";

const handleBadRequest = (req, res, next) => {
  const badRequestCondition = false;
  if (badRequestCondition) {
    next(createError(400, "Bad Request"));
  } else {
    next();
  }
};

const handleNotFound = (req, res, next) => {
  next(createError(404, "Not Found this API"));
};

const handleServerErrors = (err, req, res, next) => {
  res.status(err.status || 500); // Sử dụng err.status nếu có
  res.json({
    error: {
      status: err.status || 500,
      message: err.message || "Internal server error.",
    },
  });
};

export { handleServerErrors, handleBadRequest, handleNotFound };
