import Joi from 'joi';
import mongoose from 'mongoose';

const objectId = Joi.string().custom((value, helpers) => {
  if (!mongoose.Types.ObjectId.isValid(value)) {
    return helpers.message('Invalid Object ID');
  }
  return value;
});

const productItemSchema = Joi.object({
  productId: objectId.required(),
  variantId: objectId.optional(),
  quantity: Joi.number().integer().min(1).default(1),
});

const serviceItemSchema = Joi.object({
  serviceId: objectId.required(),
  quantity: Joi.number().integer().min(1).default(1),
});

const imageSchema = Joi.object({
  url: Joi.string().uri().required(),
  public_id: Joi.string().optional(),
});

const create = {
  body: Joi.object({
    name: Joi.string().min(3).max(100).required(),
    description: Joi.string().max(1000).optional(),
    price: Joi.number().min(0).required(),
    discountPrice: Joi.number().min(0).optional(),
    products: Joi.array().items(productItemSchema).optional(),
    services: Joi.array().items(serviceItemSchema).optional(),
    thumbnail: imageSchema.optional(),
    images: Joi.array().items(imageSchema).optional(),
    status: Joi.string().valid('ACTIVE', 'HIDDEN', 'DISCONTINUED').optional(),
  }),
};

const update = {
  body: Joi.object({
    name: Joi.string().min(3).max(100).optional(),
    description: Joi.string().max(1000).optional(),
    price: Joi.number().min(0).optional(),
    discountPrice: Joi.number().min(0).optional(),
    products: Joi.array().items(productItemSchema).optional(),
    services: Joi.array().items(serviceItemSchema).optional(),
    thumbnail: imageSchema.optional(),
    images: Joi.array().items(imageSchema).optional(),
    status: Joi.string().valid('ACTIVE', 'HIDDEN', 'DISCONTINUED').optional(),
  }),
  params: Joi.object({
    id: objectId.required(),
  }),
};

const getById = {
  params: Joi.object({ id: objectId.required() }),
};

const remove = {
  params: Joi.object({ id: objectId.required() }),
};

export default { create, update, getById, remove }; 