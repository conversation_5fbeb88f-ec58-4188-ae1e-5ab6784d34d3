import nodemailer from "nodemailer";
import config from "../config/config.js";

const emailSender = async (email, subject, html) => {

    try {
        const transporter = nodemailer.createTransport({
            service: "gmail",
            host: "smtp.gmail.com",
            port: 465,
            secure: true,
            auth: {
                user: config.GMAIL.USER,
                pass: config.GMAIL.PASS
            }
        });


        const message = {
            from: config.GMAIL.USER,
            to: email,
            subject,
            html
        };

        const info = await transporter.sendMail(message);
        return info;
    } catch (error) {
        console.error("Error occurred: ", error);
        throw error;
    };
}
export default emailSender