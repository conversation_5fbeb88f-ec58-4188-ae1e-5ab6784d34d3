import {
  cartRepository,
  productRepository,
  variantRepository,
  comboRepository,
} from '../repositories/index.js';
import BadRequestError from '../utils/response/BadRequestError.js';
import InternalServerError from '../utils/response/InternalServerError.js';
import NotFoundError from '../utils/response/NotFoundError.js';
import ResponseDataSuccess from '../utils/response/ResponseDataSuccess.js';

const getCart = async (req) => {
  try {
    const { accountId } = req.user;

    const cart = await cartRepository.findByAccountId(accountId);

    // Return empty cart if not found
    if (!cart) {
      return new ResponseDataSuccess({
        accountId,
        items: [],
      });
    }

    const populatedCart = await cartRepository.findById(cart._id);

    return new ResponseDataSuccess(populatedCart);
  } catch (error) {
    return new InternalServerError(error.message);
  }
};

const addCartItem = async (req) => {
  try {
    const newItem = req.body;
    const isComboItem = !!newItem.comboId;
    const { accountId } = req.user;

    let product = null;
    let variant = null;
    let combo = null;

    if (isComboItem) {
      combo = await comboRepository.findById(newItem.comboId);
      if (!combo || combo.status !== 'ACTIVE') {
        throw new NotFoundError('Combo not found');
      }
    } else {
      // product flow
      product = await productRepository.findById(newItem.productId);
      if (!product) {
        throw new NotFoundError('Product not found');
      }
    }

    // Variant check only for product flow
    if (!isComboItem && newItem.variantId) {
      variant = await variantRepository.findById(newItem.variantId);
      if (!variant) {
        throw new NotFoundError('Variant not found');
      }
      if (variant.productId.toString() !== newItem.productId) {
        throw new NotFoundError('Variant does not belong to product');
      }
    }

    // Create new cart if not exists
    let cart = await cartRepository.findByAccountId(accountId);
    if (!cart) {
      cart = await cartRepository.create({
        accountId,
        items: [],
      });
    }

    // Check if item exists in cart
    const existingItemIndex = cart.items.findIndex((i) => {
      if (isComboItem) {
        if (!i.comboId) return false;
        const existingId = i.comboId._id ? i.comboId._id.toString() : i.comboId.toString();
        return existingId === newItem.comboId;
      }
      if (newItem.variantId) {
        return (
          i.variantId &&
          i.variantId._id.toString() === newItem.variantId &&
          i.productId._id.toString() === newItem.productId
        );
      }
      return i.productId && i.productId._id.toString() === newItem.productId;
    });

    // Update quantity if item exists
    if (existingItemIndex >= 0) {
      cart.items[existingItemIndex].quantity += newItem.quantity;

      // Check stock availability (only for products, not combos)
      if (!isComboItem) {
        const itemQuantity = cart.items[existingItemIndex].quantity;
        const stockQuantity = variant ? variant.quantity : product.quantity;
        if (itemQuantity > stockQuantity) {
          throw new BadRequestError(
            `${variant ? 'Variant' : 'Product'} out of stock`
          );
        }
      }
    } else {
      // Add new item if it doesn't exist
      if (isComboItem) {
        cart.items.push({ comboId: newItem.comboId, quantity: newItem.quantity });
      } else {
        // Check stock availability for product
        const stockQuantity = variant ? variant.quantity : product.quantity;
        if (newItem.quantity > stockQuantity) {
          throw new BadRequestError(
            `${variant ? 'Variant' : 'Product'} out of stock`
          );
        }
        cart.items.push(newItem);
      }
    }

    await cart.save();
    const populatedCart = await cart.populate([
      { path: 'items.productId', select: '-images -description' },
      { path: 'items.variantId' },
      { path: 'items.comboId', select: 'name price discountPrice thumbnail' },
    ]);

    return new ResponseDataSuccess(populatedCart);
  } catch (error) {
    if (error instanceof NotFoundError || error instanceof BadRequestError) {
      return error;
    }
    return new InternalServerError(error.message);
  }
};

const updateCartItem = async (req) => {
  try {
    const { productId, variantId, quantity, comboId } = req.body;
    const { accountId } = req.user;

    const isCombo = !!comboId;

    // Handle combo update
    if (isCombo) {
      // Validate combo
      const combo = await comboRepository.findById(comboId);
      if (!combo || combo.status !== 'ACTIVE') {
        throw new NotFoundError('Combo not found');
      }

      // Find cart
      const cart = await cartRepository.findByAccountId(accountId);
      if (!cart) throw new NotFoundError('Cart not found');

      const itemIndex = cart.items.findIndex((item) => {
        if (!item.comboId) return false;
        const idInItem = item.comboId._id ? item.comboId._id.toString() : item.comboId.toString();
        return idInItem === comboId;
      });

      if (itemIndex === -1) throw new NotFoundError('Combo item not found in cart');

      if (quantity <= 0) {
        cart.items.splice(itemIndex, 1);
      } else {
        cart.items[itemIndex].quantity = quantity;
      }

      await cart.save();
      const populatedCart = await cart.populate([
        {
          path: 'items.productId',
          select: '-images -description',
        },
        { path: 'items.variantId' },
        { path: 'items.comboId', select: 'name price discountPrice thumbnail' },
      ]);

      return new ResponseDataSuccess(populatedCart);
    }

    // Check if product exists
    const product = await productRepository.findById(productId);
    if (!product) {
      throw new NotFoundError('Product not found');
    }

    // Check if variant exists
    let variant = null;
    if (variantId) {
      variant = await variantRepository.findById(variantId);
      if (!variant) {
        throw new NotFoundError('Variant not found');
      }

      // Check if variant belongs to product
      if (variant.productId.toString() !== productId) {
        throw new NotFoundError('Variant does not belong to product');
      }
    }

    // Check if cart exists
    const cart = await cartRepository.findByAccountId(accountId);
    if (!cart) {
      throw new NotFoundError('Cart not found');
    }

    // Check stock availability
    const stockQuantity = variant ? variant.quantity : product.quantity;
    if (quantity > stockQuantity) {
      throw new BadRequestError(
        `${variant ? 'Variant' : 'Product'} out of stock`
      );
    }

    // Find the specific item in the cart
    let itemIndex = -1;
    if (variantId) {
      itemIndex = cart.items.findIndex(
        (item) => item.variantId._id.toString() === variantId
      );
    }

    if (!variantId && productId) {
      itemIndex = cart.items.findIndex(
        (item) => item.productId._id.toString() === productId
      );
    }

    if (itemIndex < 0) {
      throw new NotFoundError('Product item not found in cart');
    }

    // Update the quantity
    cart.items[itemIndex].quantity = quantity;

    // If quantity is 0, remove the item
    if (quantity <= 0) {
      cart.items.splice(itemIndex, 1);
    }

    await cart.save();
    const populatedCart = await cart.populate([
      {
        path: 'items.productId',
        select: '-images -description',
      },
      {
        path: 'items.variantId',
      },
    ]);

    return new ResponseDataSuccess(populatedCart);
  } catch (error) {
    if (error instanceof NotFoundError || error instanceof BadRequestError) {
      return error;
    }
    return new InternalServerError(error.message);
  }
};

const deleteCartItem = async (req) => {
  try {
    const { productId, variantId } = req.body;
    const { accountId } = req.user;

    const cart = await cartRepository.findByAccountId(accountId);

    if (!cart) {
      return new NotFoundError('Cart not found');
    }

    let itemIndex = -1;

    // Find and remove the specific item from the cart
    // If variantId is provided, find the item by variantId
    if (variantId) {
      itemIndex = cart.items.findIndex((item) => item.variantId === variantId);
    }

    // If variantId is not provided, find the item by productId
    if (!variantId && productId) {
      itemIndex = cart.items.findIndex((item) => item.productId === productId);
    }

    if (itemIndex === -1) {
      return new NotFoundError('Item not found in cart');
    }

    // Remove the item
    cart.items.splice(itemIndex, 1);

    await cart.save();
    const populatedCart = await cart.populate([
      {
        path: 'items.productId',
        select: '-images -description',
      },
      {
        path: 'items.variantId',
      },
    ]);
    return new ResponseDataSuccess(populatedCart);
  } catch (error) {
    return new InternalServerError(error.message);
  }
};

const updateCartItemVariant = async (req) => {
  try {
    const { accountId } = req.user;
    const { productId, oldVariantId, newVariantId } = req.body;

    // Check if product exists
    const product = await productRepository.findById(productId);
    if (!product) {
      throw new NotFoundError('Product not found');
    }

    // Check if variant exists
    let variant = null;
    if (newVariantId) {
      variant = await variantRepository.findById(newVariantId);
      if (!variant || variant.quantity <= 0) {
        throw new NotFoundError('Variant is not available');
      }

      // Check if variant belongs to product
      if (variant.productId.toString() !== productId) {
        throw new NotFoundError('Variant does not belong to product');
      }
    }

    // Check if cart exists
    const cart = await cartRepository.findByAccountId(accountId);
    if (!cart) {
      return new NotFoundError('Cart not found');
    }

    // Find the current item in the cart
    const itemIndex = cart.items.findIndex((item) => {
      if (oldVariantId) {
        return item.variantId._id.toString() === oldVariantId;
      }
      return item.productId._id.toString() === productId;
    });

    if (itemIndex === -1) {
      throw new NotFoundError('Product Item not found in cart');
    }

    // Check if the new variant already exists in the cart
    const existingNewVariantIndex = cart.items.findIndex(
      (item) =>
        item.productId._id.toString() === productId &&
        item.variantId &&
        item.variantId._id.toString() === newVariantId
    );

    if (existingNewVariantIndex >= 0 && existingNewVariantIndex !== itemIndex) {
      // If new variant already exists in another item, remove the current item
      // and increment the quantity of the existing variant item
      // Check stock availability
      const newQuantity = cart.items[existingNewVariantIndex].quantity + 1;

      if (variant.quantity < newQuantity) {
        throw new BadRequestError('Variant is out of stock');
      }
      cart.items[existingNewVariantIndex].quantity += 1;
      cart.items.splice(itemIndex, 1);
    } else {
      // Otherwise, update the current item with the new variant and set quantity to 1
      cart.items[itemIndex].variantId = newVariantId;
      cart.items[itemIndex].quantity = 1;
    }

    await cart.save();
    const populatedCart = await cart.populate([
      {
        path: 'items.productId',
        select: '-images -description',
      },
      {
        path: 'items.variantId',
      },
    ]);
    return new ResponseDataSuccess(populatedCart);
  } catch (error) {
    if (error instanceof NotFoundError || error instanceof BadRequestError) {
      return error;
    }
    return new InternalServerError(error.message);
  }
};

const deleteCartItems = async (req) => {
  try {
    const { accountId } = req.user;
    const { items } = req.body;

    const result = await cartRepository.deleteCartItems(accountId, items);
    return new ResponseDataSuccess(result);
  } catch (error) {
    return new InternalServerError(error.message);
  }
};

export default {
  getCart,
  addCartItem,
  updateCartItem,
  deleteCartItem,
  updateCartItemVariant,
  deleteCartItems,
};
