import { useEffect, useState } from 'react';
import s from './styles.module.css';
import { Layout, Row, Col, Button, Spin } from 'antd';
import { 
    ArrowRightOutlined,
    SmileOutlined,
    RocketOutlined,
    LikeOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { viewData } from '../../redux/slices/productSlice';
import CustomCard from '../../components/Card';
import ComboCard from '../../components/Combo/ComboCard';
import ProductApi from '../../api/ProductApi';
import ComboApi from '../../api/ComboApi';
import Chatbot from '../../components/Chatbot';

const Home = () => {
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const { productTableData, loading: productsLoading } = useSelector((state) => state.product);

    const [dogProducts, setDogProducts] = useState([]);
    const [catProducts, setCatProducts] = useState([]);
    const [combos, setCombos] = useState([]);
    const [dogLoading, setDogLoading] = useState(false);
    const [catLoading, setCatLoading] = useState(false);
    const [combosLoading, setCombosLoading] = useState(false);

    useEffect(() => {
        dispatch(viewData({ page: 1, pageSize: 8 }));
        fetchDogProducts();
        fetchCatProducts();
        fetchCombos();
    }, [dispatch]);

    const fetchProductsByCategory = async (categoryId, setter, loader) => {
        loader(true);
        try {
            const response = await ProductApi.searchProducts({
                page: 1,
                pageSize: 4,
                categoryIds: [categoryId],
            });
            setter(response.rows || []);
        } catch (error) {
            console.error(`Failed to fetch products for category ${categoryId}:`, error);
            setter([]);
        } finally {
            loader(false);
        }
    };

    const fetchDogProducts = () => fetchProductsByCategory("67c1f36529159fcf8af10165", setDogProducts, setDogLoading);
    const fetchCatProducts = () => fetchProductsByCategory("67c1f36b29159fcf8af10167", setCatProducts, setCatLoading);

    const fetchCombos = async () => {
        setCombosLoading(true);
        try {
            const response = await ComboApi.searchCombos({
                page: 1,
                pageSize: 4,
                status: 'ACTIVE'
            });
            setCombos(response.rows || []);
        } catch (error) {
            console.error('Failed to fetch combos:', error);
            setCombos([]);
        } finally {
            setCombosLoading(false);
        }
    };

    const handleNavigate = (path) => {
        navigate(path);
        window.scrollTo({ top: 0, behavior: 'smooth' });
    };

    const renderProductRow = (products, loading) => {
        if (loading) return <div className={s.spinnerContainer}><Spin size="large" /></div>;
        return (
            <Row gutter={[24, 24]}>
                {products.map((item) => (
                    <Col xs={24} sm={12} md={6} key={item._id}>
                        <CustomCard product={item} />
                    </Col>
                ))}
            </Row>
        );
    };

    const renderComboRow = (combos, loading) => {
        if (loading) return <div className={s.spinnerContainer}><Spin size="large" /></div>;
        return (
            <Row gutter={[24, 24]}>
                {combos.map((combo) => (
                    <Col xs={24} sm={12} md={6} key={combo._id}>
                        <ComboCard combo={combo} />
                    </Col>
                ))}
            </Row>
        );
    };

    return (
        <Layout.Content className={s.homeContainer}>
            {/* Hero Section */}
            <div className={s.heroSection}>
                <div className={s.heroContent}>
                    <h1 className={s.heroTitle}>Chăm sóc trọn vẹn, yêu thương đong đầy</h1>
                    <p className={s.heroSubtitle}>Tất cả những gì bạn cần cho những người bạn lắm lông.</p>
                    <Button type="primary" size="large" shape="round" onClick={() => handleNavigate('/search')}>
                        Mua sắm ngay <ArrowRightOutlined />
                    </Button>
                </div>
                </div>

            {/* Shop By Pet Section */}
            <div className={`${s.section} ${s.shopByPetSection}`}>
                <h2 className={s.sectionTitle}>Mua sắm theo thú cưng</h2>
                <Row gutter={[32, 32]} justify="center">
                    <Col xs={24} md={10}>
                        <div className={`${s.petCard} ${s.dogCard}`} onClick={() => handleNavigate('/collections/mua-do-cho-cho')}>
                            <div className={s.petCardContent}>
                                <h3>Dành cho Chó</h3>
                                <Button shape="round">Xem sản phẩm</Button>
                            </div>
                        </div>
                    </Col>
                    <Col xs={24} md={10}>
                        <div className={`${s.petCard} ${s.catCard}`} onClick={() => handleNavigate('/collections/mua-do-cho-meo')}>
                             <div className={s.petCardContent}>
                                <h3>Dành cho Mèo</h3>
                                <Button shape="round">Xem sản phẩm</Button>
                            </div>
                        </div>
                        </Col>
            </Row>
            </div>

            {/* Why Choose Us Section */}
            <div className={`${s.section} ${s.whyChooseUsSection}`}>
                <h2 className={s.sectionTitle}>Tại sao chọn Pet Shop?</h2>
                 <Row gutter={[32, 32]}>
                    <Col xs={24} md={8} className={s.featureBox}>
                        <LikeOutlined className={s.featureIcon} />
                        <h3>Sản phẩm chất lượng</h3>
                        <p>Chỉ cung cấp những sản phẩm tốt nhất và an toàn nhất.</p>
                    </Col>
                    <Col xs={24} md={8} className={s.featureBox}>
                        <RocketOutlined className={s.featureIcon} />
                        <h3>Giao hàng siêu tốc</h3>
                        <p>Vận chuyển nhanh chóng, nhận hàng trong ngày.</p>
                    </Col>
                    <Col xs={24} md={8} className={s.featureBox}>
                        <SmileOutlined className={s.featureIcon} />
                        <h3>Hỗ trợ tận tâm</h3>
                        <p>Đội ngũ của chúng tôi luôn sẵn sàng tư vấn cho bạn.</p>
                        </Col>
            </Row>
            </div>
            
            {/* New Arrivals Section */}
            <div className={`${s.section} ${s.newArrivalsSection}`}>
                 <h2 className={s.sectionTitle}>Hàng mới về</h2>
                 {renderProductRow(productTableData.items, productsLoading)}
                 <div className={s.viewMoreContainer}>
                    <Button type="default" shape="round" size="large" onClick={() => handleNavigate('/search')}>
                        Xem tất cả sản phẩm
                    </Button>
                </div>
            </div>

            {/* Combos Section */}
            <div className={`${s.section} ${s.combosSection}`}>
                 <h2 className={s.sectionTitle}>Combo Tiết Kiệm</h2>
                 {renderComboRow(combos, combosLoading)}
                 <div className={s.viewMoreContainer}>
                    <Button type="default" shape="round" size="large" onClick={() => handleNavigate('/combos')}>
                        Xem tất cả combo
                    </Button>
                </div>
            </div>

            {/* Dog Products Section */}
            <div className={`${s.section} ${s.dogProductsSection}`}>
                 <h2 className={s.sectionTitle}>Dành cho Chó cưng</h2>
                 {renderProductRow(dogProducts, dogLoading)}
            </div>

             {/* Cat Products Section */}
             <div className={`${s.section} ${s.catProductsSection}`}>
                 <h2 className={s.sectionTitle}>Dành cho Mèo yêu</h2>
                 {renderProductRow(catProducts, catLoading)}
            </div>

            <Chatbot />
        </Layout.Content>
    );
};

export default Home;