import pagination from "../helper/pagination.js";
import User from "../models/user.model.js";

const createUser = async (data) => {
    return await User.create(data);
}

const findById = async (userId) => {
    return await User.findById(userId);
}

const update = async (userId, data) => {
    return await User.findByIdAndUpdate(userId, data, { new: true });
}

const updateAvatar = async (userId, avatarPath) => {
    return await User.findByIdAndUpdate(
        userId,
        { $set: { avatar: avatarPath } },
        { new: true }
    );
}

export default { createUser, update, findById, updateAvatar };