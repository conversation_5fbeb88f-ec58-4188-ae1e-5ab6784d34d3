import { HttpStatusCode } from 'axios';
import { deliveryAddressService } from '../services/index.js';
import { ErrorCodes } from '../constants/errorCode.js';

const search = async (req, res) => {
    const { userId } = req.params;

    if (!userId) {
        return res.status(HttpStatusCode.BadRequest).send({
            status: HttpStatusCode.BadRequest,
            message: ErrorCodes.EC019
        });
    }
    const currentUserId = req.user.userId;
    if (userId !== currentUserId) {
        return res.status(HttpStatusCode.BadRequest).send({
            status: HttpStatusCode.BadRequest,
            message: ErrorCodes.EC017
        });
    }
    const data = await deliveryAddressService.search(userId);
    return res.status(data.status).send(data);
};

const create = async (req, res) => {
    const { userId } = req.params;
    const data = req.body;
    const currentUserId = req.user.userId;

    if (userId !== currentUserId) {
        return res.status(HttpStatusCode.BadRequest).send({
            status: HttpStatusCode.BadRequest,
            message: ErrorCodes.EC018
        });
    }

    const response = await deliveryAddressService.create(userId, data);
    return res.status(response.status).send(response);
};

const update = async (req, res) => {
    const { id } = req.params;
    const dataUpdate = req.body
    const response = await deliveryAddressService.update(id, dataUpdate);

    return res.status(response.status).send(response);
};

const deleteDeliveryAddress = async (req, res) => {
    const { id } = req.params
    const response = await deliveryAddressService.deleteDeliveryAddress(id);
    return res.status(response.status).send(response);
};

const setDefault = async (req, res) => {
    const { id } = req.params
    const response = await deliveryAddressService.setDefault(id);
    return res.status(response.status).send(response);
}

export default { search, create, update, deleteDeliveryAddress, setDefault };
