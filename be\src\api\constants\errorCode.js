export const ErrorCodes = {
    EC001: "Email was registered!",
    EC002: "Invalid email or password!",
    EC003: "Account is not active or blocked!",
    EC004: "Token is invalid or expired!",
    EC005: "Refresh token is required!",
    EC006: "Unauthorized access!",
    EC007: "Forbidden: You do not have permission!",
    EC008: "Account activation link is invalid or expired!",
    EC009: "Password reset link is invalid or expired!",
    EC010: "User not found!",
    EC011: "Account ID does not exist!",
    EC012: "Failed to update account!",
    EC013: "Your account has been activated successfully!",
    EC014: "Invalid or expired refresh token!",
    EC015: "Registration successful! Please check your email to activate your account!",
    EC017: "Failed to find user by email!",
    EC018: "You cannot create a delivery address for another user.",
    EC019: "Failed to find user!",
    EC020: "Password was resetted successfull",
    EC021: "Incorrect password",
    EC022: "Password changed successfully",
    EC023: 'Forbidden: You do not have permission to access this resource',
    EC024: "Logged out successfully",
    EC024: "Logged in successfully",
    EC025: "New password is the same as old password",
    EC026: "Incorrect old password",


};

export const CRUDCode = {
    CC001: "Data created successfully!",
    CC002: "Data updated successfully!",
    CC003: "Data deleted successfully!",
    CC004: "Data not found!",
}


export const Mail = {
    MA001: "Please check mail to reset password!"
}

