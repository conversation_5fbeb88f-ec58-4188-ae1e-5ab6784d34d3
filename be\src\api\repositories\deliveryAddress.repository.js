import { HttpStatusCode } from 'axios';
import { CRUDCode, ErrorCodes } from '../constants/errorCode.js';
import User, { deliveryAddressSchema } from '../models/user.model.js';

const search = async (userId) => {
    const res = await User.findById(userId);
    return { status: HttpStatusCode.Ok, data: res?.deliveryAddress || [] }
};

const findById = async (id) => {
    const user = await User.findOne({ "deliveryAddress._id": id });
    return user ? user.deliveryAddress.id(id) : null;
};


const create = async (userId, data) => {
    const user = await User.findById(userId);

    if (user.deliveryAddress.length === 0) {
        const dataDefault = { ...data, isDefault: true };
        user.deliveryAddress.push(dataDefault);
    } else {
        user.deliveryAddress.push(data);
    }

    const updatedUser = await user.save();

    const newDeliveryAddress = updatedUser.deliveryAddress[updatedUser.deliveryAddress.length - 1];

    return { status: HttpStatusCode.Created, message: CRUDCode.CC001, res: newDeliveryAddress };
};

const update = async (id, dataUpdate) => {
    try {
        // Tìm user chứa deliveryAddress với id tương ứng
        const user = await User.findOne({ "deliveryAddress._id": id });

        if (!user) {
            return {
                status: HttpStatusCode.NotFound,
                message: 'User or delivery address not found'
            };
        }

        // Tìm deliveryAddress cụ thể
        const deliveryAddress = user.deliveryAddress.id(id);
        if (!deliveryAddress) {
            return {
                status: HttpStatusCode.NotFound,
                message: 'Delivery address not found'
            };
        }

        // Ghi đè toàn bộ dữ liệu cũ bằng dữ liệu mới
        Object.assign(deliveryAddress, dataUpdate);

        // Lưu thay đổi
        await user.save();

        return {
            status: HttpStatusCode.OK,
            message: CRUDCode.CC002 || 'Update successful',
            data: deliveryAddress
        };
    } catch (error) {
        return {
            status: HttpStatusCode.InternalServerError,
            message: error.message
        };
    }
};

const deleteDeliveryAddress = async (id) => {
    const user = await User.findOne({ "deliveryAddress._id": id });
    user.deliveryAddress = user.deliveryAddress.filter(
        (address) => address._id.toString() !== id
    );
    await user.save();
    return { status: HttpStatusCode.Ok, message: CRUDCode.CC003 };

}


const setDefault = async (id) => {
    const user = await User.findOne({ "deliveryAddress._id": id });

    user.deliveryAddress = user.deliveryAddress.map((address) => ({
        ...address,
        isDefault: address._id.toString() === id,
    }));
    await user.save();
    return { status: HttpStatusCode.Ok, message: CRUDCode.CC002 };
}

export default { search, create, update, findById, deleteDeliveryAddress, setDefault };
