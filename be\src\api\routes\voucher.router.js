import express from 'express';
import voucherController from '../controllers/voucher.controller.js';
import voucherValidation from '../validations/voucher.validation.js';
import validate from '../middlewares/validates.js';
import { verifyToken } from '../middlewares/authenticateJWT.js';
import authorizeRoles from '../middlewares/authorizeRoles.js';
import { ROLES } from '../constants/role.js';

const router = express.Router();

// Admin CRUD
router.post(
  '/',
  verifyToken,
  authorizeRoles(ROLES.ADMIN),
  validate(voucherValidation.createVoucher),
  voucherController.createVoucher
);

router.get(
  '/',
  verifyToken,
  authorizeRoles(ROLES.ADMIN),
  voucherController.getVouchers
);

// Validate voucher for customer (no admin rights needed)
router.get('/validate', voucherController.validateVoucher);

router.get(
  '/:id',
  verifyToken,
  authorizeRoles(ROLES.ADMIN),
  voucherController.getVoucherById
);

router.patch(
  '/:id',
  verifyToken,
  authorizeRoles(ROLES.ADMIN),
  validate(voucherValidation.updateVoucher),
  voucherController.updateVoucher
);

router.delete(
  '/:id',
  verifyToken,
  authorizeRoles(ROLES.ADMIN),
  voucherController.deleteVoucher
);

export default router; 