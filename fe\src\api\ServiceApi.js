import Author<PERSON>pi from "./baseAPI/AuthorBaseApi";
import <PERSON><PERSON>or<PERSON><PERSON> from "./baseAPI/UnauthorBaseApi";
import { objectToQueryString } from "../utils/queryString";

class ServiceApi {
  constructor() {
    this.url = "/api/v1/services"; // backend path to be implemented later
  }

  listPublic = async (filters = {}) => {
    const qs = objectToQueryString(filters);
    return (await UnauthorApi.get(`${this.url}?${qs}`)).data;
  };

  listAdmin = async (filters = {}) => {
    const qs = objectToQueryString(filters);
    return (await AuthorApi.get(`${this.url}?${qs}`)).data;
  };

  getById = async (id) => {
    return (await AuthorApi.get(`${this.url}/${id}`)).data;
  };

  create = async (body) => {
    return (await AuthorApi.post(this.url, body)).data;
  };

  update = async (id, body) => {
    return (await AuthorApi.put(`${this.url}/${id}`, body)).data;
  };

  remove = async (id) => {
    return (await AuthorApi.delete(`${this.url}/${id}`)).data;
  };
}

export default new ServiceApi(); 