{"name": "be", "version": "1.0.0", "description": "", "type": "module", "main": "src/server.js", "scripts": {"lint": "eslint src/**/*.js", "dev": "nodemon src/server.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"-": "^0.0.1", "@node-red/editor-api": "^4.0.5", "@node-red/runtime": "^4.0.5", "@node-red/util": "^4.0.5", "await": "^0.2.6", "axios": "^1.7.7", "bcryptjs": "^3.0.1", "be": "file:", "body-parser": "^1.20.3", "cloudinary": "^2.5.1", "config": "^3.3.12", "cookie-parser": "^1.4.7", "cookies": "^0.9.1", "cors": "^2.8.5", "crypto": "^1.0.1", "date-fns": "^4.1.0", "dateformat": "^5.0.3", "debug": "^4.3.4", "dotenv": "^16.4.7", "ejs": "^3.1.10", "express": "^4.21.2", "express-session": "^1.18.1", "firebase-admin": "^12.6.0", "google-auth-library": "^9.15.1", "helmet": "^8.0.0", "http": "^0.0.1-security", "http-errors": "^2.0.0", "jade": "^1.11.0", "joi": "^17.13.3", "jquery": "^3.6.4", "jsonwebtoken": "^9.0.2", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "md5": "^2.3.0", "module-alias": "^2.2.3", "moment": "^2.30.1", "mongodb": "^6.12.0", "mongoose": "^8.10.0", "mongoose-beautiful-unique-validation": "^7.1.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "multiparty": "^4.2.3", "node-fetch": "^2.6.9", "nodemailer": "^6.10.0", "nodemon": "^3.1.7", "os": "^0.1.2", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "qs": "^6.13.0", "request": "^2.88.2", "save": "^2.9.0", "serve-favicon": "^2.5.0", "set-tz": "^0.2.0", "sha256": "^0.2.0", "slugify": "^1.6.6", "socket.io": "^4.8.0", "uuid": "^10.0.0", "ws": "^8.18.0"}, "devDependencies": {"@eslint/js": "^9.12.0", "eslint": "^9.12.0", "globals": "^15.10.0"}, "_moduleAliases": {"@repositories": "./repositories", "@utils": "./utils", "@middlewares": "./middlewares"}}