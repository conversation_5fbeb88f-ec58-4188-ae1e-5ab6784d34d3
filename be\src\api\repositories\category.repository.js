import Category from '../models/category.model.js';
import Product from '../models/product.model.js';
import NotFoundError from '../utils/response/NotFoundError.js';

const search = async (filters) => {
  let { query } = filters;

  query = query?.trim();

  const baseQuery = {
    isDeleted: false,
  };

  baseQuery.isHide = filters?.isHide;

  let targetCategories;

  if (!query) {
    targetCategories = await Category.find(baseQuery);
  } else {
    targetCategories = await findByNameOrSlug(query, filters.isHide);
  }

  const rootIds = [
    ...new Set(targetCategories.map((category) => category.path.split('.')[0])),
  ];

  const allCategories = await Category.find({
    $or: [
      { _id: { $in: rootIds } }, // Include all roots
      { path: { $regex: rootIds.join('|') } }, // Include all descendants of all roots
    ],
    isDeleted: false,
    isHide: filters?.isHide,
  }).sort({ level: 1 });

  const categoryMap = new Map(
    allCategories.map((category) => [
      category._id.toString(),
      {
        ...category.toObject(),
        children: [],
      },
    ])
  );

  // Create trees for each root
  const trees = rootIds.map((rootId) => {
    const tree = { root: null };

    if (query) {
      tree.targetPaths = targetCategories
        .filter((cat) => cat.path.startsWith(rootId))
        .map((cat) => cat.path.split('.'));
    }

    // Build the tree structure
    allCategories
      .filter((category) => category.path.startsWith(rootId))
      .forEach((category) => {
        const categoryData = categoryMap.get(category._id.toString());

        if (category.level === 1) {
          tree.root = categoryData;
        } else {
          const parent = categoryMap.get(category.parentId.toString());
          if (parent) {
            parent.children.push(categoryData);
          }
        }
      });

    return tree;
  });

  const filteredTrees = trees.filter((tree) => tree.root !== null);

  return filteredTrees;
};

const findById = async (id, showHidden = true) => {
  const baseQuery = { _id: id, isDeleted: false };

  if (!showHidden) {
    baseQuery.isHide = false;
  }

  const category = await Category.findOne(baseQuery);

  return category;
};

const findByNameOrSlug = async (nameOrSlug, showHidden = true) => {
  const baseQuery = {
    isDeleted: false,
    $or: [
      { name: { $regex: nameOrSlug, $options: 'i' } },
      { slug: { $regex: nameOrSlug, $options: 'i' } },
    ],
  };

  if (!showHidden) {
    baseQuery.isHide = false;
  }

  return await Category.find(baseQuery);
};

const create = async (data) => await Category.create(data);

const update = async (id, data) => {
  const category = await findById(id);
  if (!category) return new NotFoundError('Category not found');

  Object.assign(category, data);

  return await category.save();
};

const findByPath = async (path) =>
  await Category.find({ path: { $regex: path } });

const softDelete = async (ids) =>
  await Category.updateMany({ _id: { $in: ids } }, { isDeleted: true });

const hasProductsInCategoryTree = async (categoryId) => {
  const category = await Category.findOne({
    _id: categoryId,
    isDeleted: false,
  });

  if (!category) {
    return new NotFoundError('Category not found');
  }

  // Find all descendant categories using the path pattern
  const allCategories = await Category.find({
    path: { $regex: `^${category.path}` },
    isDeleted: false,
  });

  // Get all category IDs including the target and its descendants
  const categoryIds = allCategories.map((cat) => cat._id);

  // Check if any products exist for these categories
  const productsCount = await Product.countDocuments({
    categoryId: { $in: categoryIds },
    isDeleted: false,
  });

  return { hasProduct: productsCount > 0 };
};

export default {
  search,
  create,
  update,
  softDelete,
  findByPath,
  findById,
  hasProductsInCategoryTree,
};
