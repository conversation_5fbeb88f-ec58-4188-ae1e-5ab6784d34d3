import InternalServerError from '../utils/response/InternalServerError.js';
import ResponseDataSuccess from '../utils/response/ResponseDataSuccess.js';
import BadRequestError from '../utils/response/BadRequestError.js';
import { createRandomString } from '../utils/helper.js';
import { deleteImage, uploadImage } from '../helper/cloudinary.uploader.js';

const uploadSingleImage = async (req) => {
  try {
    const { file } = req;
    if (!file) {
      return new BadRequestError('No file uploaded');
    }

    const { buffer } = file;
    const base64Image = `data:image/jpeg;base64,${buffer.toString('base64')}`;
    const public_id = `${createRandomString()}`;
    const uploadResult = await uploadImage(base64Image, public_id);

    return new ResponseDataSuccess(uploadResult);
  } catch (error) {
    return new InternalServerError(error);
  }
};

const uploadMultipleImages = async (req) => {
  try {
    const { files } = req;
    if (!files || files.length === 0) {
      return new BadRequestError('No file uploaded');
    }

    const uploadResults = [];
    await Promise.all(
      files.map(async (file) => {
        const { buffer } = file;
        const base64Image = `data:image/jpeg;base64,${buffer.toString(
          'base64'
        )}`;
        const public_id = `${createRandomString()}`;
        const uploadResult = await uploadImage(base64Image, public_id);
        uploadResults.push(uploadResult);
      })
    );

    return new ResponseDataSuccess(uploadResults);
  } catch (error) {
    return new InternalServerError(error);
  }
};

const deleteSingleImage = async (public_id) => {
  try {
    if (!public_id) {
      return new BadRequestError('Not found public_id');
    }

    const uploadResult = await deleteImage(public_id);

    return new ResponseDataSuccess(uploadResult);
  } catch (error) {
    return new InternalServerError(error);
  }
};

const deleteMultipleImages = async (public_ids) => {
  try {
    if (!public_ids || public_ids.length === 0) {
      return new BadRequestError('Not found public_ids');
    }

    const uploadResults = [];
    await Promise.all(
      public_ids.map(async (public_id) => {
        const uploadResult = await deleteImage(public_id);
        uploadResults.push(uploadResult);
      })
    );

    return new ResponseDataSuccess(uploadResults);
  } catch (error) {
    return new InternalServerError(error);
  }
};

export default {
  uploadSingleImage,
  uploadMultipleImages,
  deleteSingleImage,
  deleteMultipleImages,
};
