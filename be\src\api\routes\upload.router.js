import express from 'express';
import { uploadController } from '../controllers/index.js';
import upload from '../config/multerConfig.js';
import handleUploadErrors from '../middlewares/handleMulterError.js';

const uploadRouter = express.Router();

uploadRouter.post(
  '/single',
  upload.single('file'),
  handleUploadErrors,
  uploadController.uploadSingleImage
);

uploadRouter.post(
  '/multiple',
  upload.array('files', 20),
  handleUploadErrors,
  uploadController.uploadMultipleImages
);

uploadRouter.delete('/single', uploadController.deleteSingleImage);
uploadRouter.delete('/multiple', uploadController.deleteMultipleImages);

export default uploadRouter;
