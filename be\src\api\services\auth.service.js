import { HttpStatusCode } from "axios";
import { authRepository, userRepository } from "../repositories/index.js";
import Unauthorized from "../utils/response/UnAuthorizedError.js";
import { createAccessToken, createRefreshToken } from "../middlewares/authenticateJWT.js";
import jwt from "jsonwebtoken";
import emailSender from "../helper/email.sender.js";
import NotFoundError from "../utils/response/NotFoundError.js";
import accountRepository from "../repositories/account.repository.js";
import ActivationEmail, { ActiveTitleMail } from "../constants/mail/ActiveAccount.js";
import { generateActivationToken, generateRandomPassword } from "../utils/generateRandom.js";
import Account from "../models/account.model.js";
import bcrypt from "bcryptjs";
import { CRUDCode, ErrorCodes, Mail } from "../constants/errorCode.js";
import ResetPasswordEmail, { ResetPasswordTitleMail } from "../constants/mail/ResetPasswork.js";


const login = async (email, password) => {
  const account = await authRepository.findUserByEmail(email);

  if (!account) {
    return new Unauthorized();
  }

  const isPasswordValid = await bcrypt.compare(password, account.password);
  if (!isPasswordValid) {
    return new Unauthorized();
  }


  if (account.status.isBlocked) {
    return new Unauthorized(ErrorCodes.EC003);
  }
  const bodyForToken = {
    accountId: account._id,
    userId: account.userId._id,
    email: account.email,
    fullName: account.userId.fullName,
    role: account.role,
  }

  const accessToken = createAccessToken(bodyForToken);
  const refreshToken = createRefreshToken(bodyForToken);

  account.refreshToken = refreshToken;
  await account.save();

  return {
    status: HttpStatusCode.Ok,
    message: ErrorCodes.EC024,
    accessToken,
    refreshToken,
  };
};

const loginWithGoogle = async (email, googleId, displayName) => {
  try {

    let account = await Account.findOne({ email });

    if (!account) {
      const userDataRegister = { fullName: displayName };
      const password = await bcrypt.hash(generateRandomPassword(), 10);
      const activation = { token: generateActivationToken(), expires: new Date(Date.now() + 24 * 60 * 60 * 1000 * 10) }
      const resUser = await userRepository.createUser(userDataRegister);

      const accountData = { email, password: password, userId: resUser._id, activation, googleId, status: { isBlocked: false } };
      console.log(accountData);

      const resAccount = await authRepository.register(accountData);
      console.log(accountData);
      console.log(resAccount);
      
      

      account = await Account.findOne({ email });



      const bodyForToken = {
        accountId: account._id,
        userId: account.userId._id,
        email: account.email,
        fullName: resAccount.userId?.fullName || displayName,
        role: account.role,
      }


      const accessToken = createAccessToken(bodyForToken);
      const refreshToken = createRefreshToken(bodyForToken);

      account.refreshToken = refreshToken;

      await account.save();
      return {
        status: HttpStatusCode.Ok,
        accessToken,
        refreshToken,
      };
    }

    if (account.status.isBlocked) {
      return new Unauthorized(ErrorCodes.EC003);
    }

    if (!account.googleId) {
      account.googleId = googleId;
      await account.save();
    }
    const bodyForToken = {
      accountId: account._id,
      userId: account.userId._id,
      email: account.email,
      fullName: account.userId?.fullName || displayName,
      role: account.role,
    }
    const accessToken = createAccessToken(bodyForToken);
    const refreshToken = createRefreshToken(bodyForToken);

    account.refreshToken = refreshToken;
    await account.save();

    return {
      status: HttpStatusCode.Ok,
      message: ErrorCodes.EC024,
      accessToken,
      refreshToken,
    };
  } catch (error) {
    console.error("Error in login WithGoogle service:", error);
    throw new Error("Internal Server Error");
  }
};

const register = async (data) => {

  const { email, password, fullName, address, phone } = data;

  const account = await authRepository.findUserByEmail(email);

  if (account) {
    return {
      status: HttpStatusCode.Conflict,
      message: ErrorCodes.EC001,
    };
  }
  const userDataRegister = { fullName, address, phone };

  const hashedPassword = await bcrypt.hash(password, 10);
  data.password = hashedPassword;

  const activation = { token: generateActivationToken(), expires: new Date(Date.now() + 24 * 60 * 60 * 1000 * 10) }
  const resUser = await userRepository.createUser(userDataRegister);
  const accountData = { email, password: data.password, userId: resUser._id, activation };
  const resAccount = await authRepository.register(accountData);

  await emailSender(email, ActiveTitleMail, ActivationEmail(email, activation.token));

  return { status: resAccount.status || 0, message: resAccount.message || 0 };
};

const refreshToken = async (req, res) => {
  const refreshToken = req.cookies.refreshToken;
  if (!refreshToken) {
    return ({ status: HttpStatusCode.BadRequest, message: ErrorCodes.EC005 });
  }

  try {
    const decoded = jwt.verify(refreshToken, process.env.REFRESH_TOKEN_SECRET);
    const accessToken = createAccessToken(decoded.user);
    res.cookie('accessToken', accessToken, {
      // httpOnly: true,
      // secure: process.env.NODE_ENV === 'production',
      maxAge: 1000 * 60 * 15,
      sameSite: "Strict",
    });

    return ({ status: HttpStatusCode.Ok, accessToken: accessToken });
  } catch (error) {

    return ({ status: HttpStatusCode.BadRequest, message: ErrorCodes.EC014 });
  }
}

const editAccount = async (data) => {
  const { accountId, ...resData } = data;
  const account = await accountRepository.getByAccountId(accountId);
  if (!account) {
    return new NotFoundError(ErrorCodes.EC011);
  }

  try {

    const response = await accountRepository.update(accountId, resData);
    return ({ status: HttpStatusCode.Ok, message: CRUDCode.CC002 })
  } catch (error) {
    return ({ status: HttpStatusCode.BadRequest, message: error.message });
  }
}

// const logout = async (id) => {
//   const account = await accountRepository.getByAccountId(id);
//   if (!account) {
//     return ({ status: HttpStatusCode.NotFound, message: ErrorCodes.EC010 })
//   }

//   const accountId = account._id;
//   try {
//     const refreshToken = "";
//     accountRepository.update(accountId, { refreshToken });
//     return ({ status: HttpStatusCode.Ok, message: ErrorCodes.EC019 })
//   } catch (error) {
//     return ({ status: HttpStatusCode.BadRequest, message: error.message });

//   }
// }

const forgotPassword = async (email) => {
  const account = await accountRepository.getByEmail(email);
  if (!account) {
    return ({ status: HttpStatusCode.NotFound, message: ErrorCodes.EC010 })
  }
  try {
    const resetPWToken = { token: generateActivationToken(), expires: new Date(Date.now() + 5 * 60 * 1000) }
    await authRepository.updateResetPWTokenByAccountId(account._id, resetPWToken)
    await emailSender(email, ResetPasswordTitleMail, ResetPasswordEmail(email, resetPWToken.token));
    return ({ status: HttpStatusCode.Ok, message: Mail.MA001, token: resetPWToken.token })
  } catch (error) {
    return ({ status: HttpStatusCode.BadRequest, message: error.message });

  }
}

const checkResetPWToken = async (resetPWToken) => {
  const account = await Account.findOne({ "resetPWToken.token": resetPWToken });
  console.log(account);

  if (!account) {
    return { status: HttpStatusCode.BadRequest, message: ErrorCodes.EC009 };
  }
  const tokenExpiry = new Date(account.resetPWToken.expires.getTime() - 10000);
  if (tokenExpiry < new Date()) {
    return { status: HttpStatusCode.Unauthorized, message: ErrorCodes.EC004 };
  }
  return { status: HttpStatusCode.Ok, email: account.email };
};

const checkActiveToken = async (activeToken) => {
  const account = await Account.findOne({ "activation.token": activeToken });
  if (!account) {
    return { status: HttpStatusCode.BadRequest, message: ErrorCodes.EC009 };
  }


  const tokenExpiry = new Date(account.activation.expires.getTime() - 10000);
  if (tokenExpiry < new Date()) {
    return { status: HttpStatusCode.Unauthorized, message: ErrorCodes.EC004 };
  }
  return { status: HttpStatusCode.Ok, mail: account.email };
};

const changePassword = async (password, newPassword, req) => {
  const email = req.user.email;

  // Tìm user theo email
  const account = await authRepository.findUserByEmail(email);
  if (!account) {
    return { status: HttpStatusCode.BadRequest, message: ErrorCodes.EC002 };
  }

  const isMatch = await authRepository.comparePassword(password, account.password);

  if (!isMatch) {
    return { status: HttpStatusCode.BadRequest, message: ErrorCodes.EC026 };
  }

  if (password === newPassword) {
    return { status: HttpStatusCode.BadRequest, message: ErrorCodes.EC025 };
  }

  try {
    const hashedNewPassword = await bcrypt.hash(newPassword, 10);
    await authRepository.updatePassword(email, hashedNewPassword);
    return { status: HttpStatusCode.Ok, message: ErrorCodes.EC022 };
  } catch (error) {
    return { status: HttpStatusCode.InternalServerError, message: "Đã xảy ra lỗi khi cập nhật mật khẩu" };
  }
};


export default { login, register, refreshToken, editAccount, loginWithGoogle, forgotPassword, checkResetPWToken, checkActiveToken, changePassword };
