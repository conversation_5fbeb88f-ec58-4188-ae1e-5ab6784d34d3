import { HttpStatusCode } from "axios";
import bcrypt from "bcryptjs";
import { ROLES } from "../constants/role.js";
import Account from "../models/account.model.js";
import InternalServerError from "../utils/response/InternalServerError.js";
import ResponseDataSuccess from "../utils/response/ResponseDataSuccess.js";
import NotFoundError from "../utils/response/NotFoundError.js";
import accountRepository from "../repositories/account.repository.js";
import { userRepository } from "../repositories/index.js";
import { removeVietnameseTones } from "../helper/removeVietnameseTones.js";

const search = async (filters, options) => {
  try {
    const page = parseInt(options.page) || 1;
    const pageSize = parseInt(options.pageSize) || 10;
    const offset = (page - 1) * pageSize;

    const sortCriteria = {};
    if (options.sortField && options.sortOrder) {
      const sortOrder = options.sortOrder === "asc" ? 1 : -1;
      sortCriteria[options.sortField === "fullName" ? "user.fullName" : options.sortField] = sortOrder;
    } else {
      sortCriteria["createdAt"] = 1;
    }

    // Build match stage
    let matchStage = { role: { $in: [ROLES.EMPLOYEE] } };
    if (filters.search) {
      const searchText = filters.search.trim();
      const searchPattern = removeVietnameseTones(searchText);
      matchStage.$or = [
        { "user.fullName": { $regex: searchPattern, $options: "i" } },
        { email: { $regex: searchPattern, $options: "i" } },
      ];
    }
    if (typeof filters.isBlocked !== "undefined") {
      matchStage["status.isBlocked"] = filters.isBlocked === "true" || filters.isBlocked === true;
    }

    // Aggregation pipeline
    const pipeline = [
      {
        $lookup: {
          from: "ph_users",
          localField: "userId",
          foreignField: "_id",
          as: "user",
        },
      },
      { $unwind: { path: "$user", preserveNullAndEmptyArrays: true } },
      { $match: matchStage },
      { $sort: sortCriteria },
      { $skip: offset },
      { $limit: pageSize },
      {
        $project: {
          _id: 1,
          email: 1,
          status: 1,
          role: 1,
          createdAt: 1,
          "user.fullName": 1,
          "user.phone": 1,
          "user.address": 1,
        },
      },
    ];

    const rows = await Account.aggregate(pipeline);

    // Count total
    const totalResult = await Account.aggregate([
      ...pipeline.slice(0, 3),
      { $count: "total" },
    ]);
    const total = totalResult[0]?.total || 0;

    const processedRows = rows.map((row) => ({
      _id: row._id,
      email: row.email,
      status: row.status,
      role: row.role,
      createdAt: row.createdAt,
      user: {
        fullName: row.user?.fullName || "",
        phone: row.user?.phone || "",
        address: row.user?.address || "",
      },
    }));

    return new ResponseDataSuccess({ rows: processedRows, total });
  } catch (error) {
    return new InternalServerError(error.message);
  }
};

const create = async (data) => {
  try {
    const { email, password, fullName, address, phone } = data;

    const existing = await Account.findOne({ email });
    if (existing) {
      return {
        status: HttpStatusCode.Conflict,
        message: "Email already exists",
      };
    }

    const user = await userRepository.createUser({ fullName, address, phone });
    const hashedPassword = await bcrypt.hash(password, 10);

    const accountData = {
      email,
      password: hashedPassword,
      userId: user._id,
      role: [ROLES.EMPLOYEE],
      status: { isBlocked: false, reasonBlock: "" },
    };

    const account = await Account.create(accountData);

    return new ResponseDataSuccess(account);
  } catch (error) {
    return new InternalServerError(error.message);
  }
};

const toggleStatus = async (accountId, reasonBlock = "") => {
  try {
    const account = await accountRepository.getByAccountId(accountId);
    if (!account) return new NotFoundError("Account not found");

    await accountRepository.changeStatus(accountId, account.status.isBlocked, reasonBlock);
    return new ResponseDataSuccess("Updated status");
  } catch (error) {
    return new InternalServerError(error.message);
  }
};

const updateRole = async (accountId, roles) => {
  try {
    const updated = await accountRepository.update(accountId, { role: roles });
    return new ResponseDataSuccess(updated);
  } catch (error) {
    return new InternalServerError(error.message);
  }
};

const updateInfo = async (accountId, data) => {
  try {
    const account = await accountRepository.getByAccountId(accountId);
    if (!account) return new NotFoundError("Account not found");

    // Update email if provided and different
    if (data.email && data.email !== account.email) {
      await accountRepository.update(accountId, { email: data.email });
    }

    const userId = account.userId;
    if (data.fullName || data.address || data.phone) {
      await userRepository.update(userId, {
        ...(data.fullName && { fullName: data.fullName }),
        ...(data.address !== undefined && { address: data.address }),
        ...(data.phone && { phone: data.phone }),
      });
    }

    return new ResponseDataSuccess("Updated");
  } catch (error) {
    return new InternalServerError(error.message);
  }
};

export default { search, create, toggleStatus, updateRole, updateInfo }; 