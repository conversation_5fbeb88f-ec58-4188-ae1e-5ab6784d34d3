import Joi from 'joi';
import mongoose from 'mongoose';
import { objectId } from '../helper/ObjectId.js';




// Validation cho Province
const provinceSchema = Joi.object({
    provinceId: Joi.string().required(),
    provinceName: Joi.string().required(),
});

// Validation cho District
const districtSchema = Joi.object({
    districtId: Joi.string().required(),
    districtName: Joi.string().required(),
});

// Validation cho Ward
const wardSchema = Joi.object({
    wardId: Joi.string().required(),
    wardName: Joi.string().required(),
});

// Validation cho việc tạo mới địa chỉ giao hàng
const create = {
    params: Joi.object({
        userId: objectId.required(),
    }),
    body: Joi.object({
        fullName: Joi.string().min(3).max(50).required().messages({
            'string.min': 'Full name must be at least 3 characters long.',
            'string.max': 'Full name cannot exceed 50 characters.',
        }),
        phone: Joi.string()
            .length(10)
            .pattern(/^0[0-9]{9}$/)
            .required()
            .messages({
                'string.length': 'Phone number must be exactly 10 digits.',
                'string.pattern.base': 'Phone number must start with 0 and contain only digits.',
            }),
        address: Joi.string().max(255).required(),
        province: provinceSchema.required(),
        district: districtSchema.required(),
        ward: wardSchema.required(),
        isDefault: Joi.boolean().default(false),
    }),
};

const update = {
    params: Joi.object({
        id: objectId.required(),
    }),
    body: Joi.object({
        fullName: Joi.string().min(3).max(50).required().messages({
            'string.min': 'Full name must be at least 3 characters long.',
            'string.max': 'Full name cannot exceed 50 characters.',
        }),
        phone: Joi.string()
            .length(10)
            .pattern(/^0[0-9]{9}$/)
            .required()
            .messages({
                'string.length': 'Phone number must be exactly 10 digits.',
                'string.pattern.base': 'Phone number must start with 0 and contain only digits.',
            }),
        address: Joi.string().max(255).required(),
        province: provinceSchema.required(),
        district: districtSchema.required(),
        ward: wardSchema.required(),
    }),
};

// Validation cho việc xóa địa chỉ giao hàng
const deleteValidation = {
    params: Joi.object({
        id: objectId.required()
    }),
};

export default { create, update, deleteValidation };