import { Card, Badge } from 'antd';
import s from '../Card/styles.module.css';
import { formatPrice } from '../../utils/formatPrice';
import { useNavigate } from 'react-router-dom';
import { ShoppingCartOutlined } from '@ant-design/icons';
import { message } from 'antd';
import { useDispatch } from 'react-redux';
import { addToCart } from '../../redux/slices/cartSlice';
import HeartIcon from '../Wishlist/HeartIcon.jsx';

const ComboCard = ({ combo }) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const hasDiscount = combo.discountPrice && combo.discountPrice < combo.price;

  const handleAddToCart = async () => {
    try {
      await dispatch(addToCart({ item: { comboId: combo._id, quantity: 1 } }));
      message.success('Đã thêm combo vào giỏ');
    } catch (error) {
      console.error(error);
      message.error('Lỗi thêm combo');
    }
  };

  return (
    <Badge.Ribbon text={hasDiscount ? `Giảm ${formatPrice(combo.price - combo.discountPrice)}đ` : null} color="red">
      <Card
        className={s['product-item']}
        hoverable
        cover={
          <div
            className={s['image-container']}
            onClick={() => navigate(`/combos/${combo._id}`)}
          >
            <img alt="combo" src={combo.thumbnail?.url} className={s['product-image']} />
            <img
              alt="combo-hover"
              src={combo.images && combo.images.length > 0 ? combo.images[0].url : combo.thumbnail?.url}
              className={s['product-image-hover']}
            />
            <div style={{ position:'absolute', top:8, right:8, zIndex:2 }} onClick={(e)=>e.stopPropagation()}>
              <HeartIcon combo={combo} />
            </div>
          </div>
        }
        onClick={() => navigate(`/combos/${combo._id}`)}
        actions={[<div onClick={handleAddToCart}><ShoppingCartOutlined/> Thêm giỏ</div>]}
      >
        <Card.Meta
          title={combo.name}
          description={
            hasDiscount ? (
              <>
                <span style={{ textDecoration: 'line-through', marginRight: 4 }}>{formatPrice(combo.price)}đ</span>
                <span style={{ color: 'var(--primary-color)', fontWeight: 600 }}>{formatPrice(combo.discountPrice)}đ</span>
              </>
            ) : (
              `${formatPrice(combo.price)} đ`
            )
          }
        />
      </Card>
    </Badge.Ribbon>
  );
};

export default ComboCard; 