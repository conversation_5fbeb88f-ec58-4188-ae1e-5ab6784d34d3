import React, { useState, useEffect } from "react";
import {
  Row,
  Col,
  Button,
  Divider,
  Input,
  Form,
  message,
  Upload,
} from "antd";
import { UserOutlined, LoadingOutlined, PlusOutlined } from "@ant-design/icons";
import styles from "./Profile.module.css";
import { useSelector } from "react-redux";
import UserApi from "../../../api/UserApi";
import { removeNullOrEmpty } from "../../../utils/removeNullOrEmpty";

const getBase64 = (img, callback) => {
  const reader = new FileReader();
  reader.addEventListener("load", () => callback(reader.result));
  reader.readAsDataURL(img);
};

const beforeUpload = (file) => {
  const isJpgOrPng = file.type === "image/jpeg" || file.type === "image/png";
  if (!isJpgOrPng) {
    message.error("You can only upload JPG/PNG file!");
  }
  const isLt2M = file.size / 1024 / 1024 < 2;
  if (!isLt2M) {
    message.error("Image must smaller than 2MB!");
  }
  return isJpgOrPng && isLt2M;
};

function MyProfile() {
  const { userInfo } = useSelector((state) => state.user);
  const [form] = Form.useForm();
  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [initialData, setInitialData] = useState({});
  const [imageUrl, setImageUrl] = useState();

  const accountId = userInfo?.accountId;

  const apiUrl = `http://${import.meta.env.VITE_PETHEAVEN_IP}:${import.meta.env.VITE_PETHEAVEN_PORT}`;
  const baseApiV1 = `${apiUrl}/api/v1`;

  useEffect(() => {
    if (accountId) {
      setLoading(true);
      UserApi.getUserById(accountId)
        .then((response) => {
          const data = response?.data?.data;
          const profileData = {
            email: data?.email,
            fullName: data?.userId?.fullName,
            phone: data?.userId?.phone,
            avatar: data?.userId?.avatar,
          };
          setInitialData(profileData);
          if (profileData.avatar) {
            setImageUrl(`${apiUrl}${profileData.avatar}`);
          }
          form.setFieldsValue(profileData);
        })
        .catch((error) => {
          message.error("Lỗi khi tải thông tin người dùng!");
          console.error("Error fetching user data:", error);
        })
        .finally(() => setLoading(false));
    }
  }, [accountId, form]);

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleCancel = () => {
    setIsEditing(false);
    form.resetFields();
  };

  const handleSave = async (values) => {
    setLoading(true);
    try {
      const { email, ...updateData } = values;
      const cleanedValues = removeNullOrEmpty(updateData);
      await UserApi.updateProfile(accountId, cleanedValues);
      message.success("Cập nhật thông tin thành công!");
      setIsEditing(false);
      setInitialData((prev) => ({ ...prev, ...values }));
    } catch (error) {
      message.error("Cập nhật thất bại. Vui lòng thử lại!");
      console.error("Failed to update profile:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleAvatarUpload = async ({ file, onSuccess, onError }) => {
    setUploading(true);
    const formData = new FormData();
    formData.append("avatar", file);

    try {
      const response = await UserApi.updateAvatar(accountId, formData);
      const newAvatarPath = response.data.data.data.avatar;
      
      setImageUrl(`${apiUrl}${newAvatarPath}`);
      message.success("Cập nhật ảnh đại diện thành công!");
      
      onSuccess(response.data); // Notify Ant Design's Upload component of success
    } catch (error) {
      console.error("Avatar upload failed:", error);
      message.error("Cập nhật ảnh đại diện thất bại.");
      onError(error); // Notify Ant Design's Upload component of an error
    } finally {
      setUploading(false);
    }
  };

  const uploadButton = (
    <div className={styles.uploadButton}>
      {uploading ? <LoadingOutlined /> : <PlusOutlined />}
      <div style={{ marginTop: 8 }}>Tải lên</div>
    </div>
  );

  return (
    <div className={styles.profileContainer}>
      <Row justify="space-between" align="middle">
        <Col>
          <h1 className={styles.title}>Hồ Sơ Của Tôi</h1>
          <p>Quản lý thông tin hồ sơ để bảo mật tài khoản</p>
        </Col>
        <Col>{!isEditing && <Button onClick={handleEdit}>Chỉnh sửa</Button>}</Col>
      </Row>
      <Divider />

      <Row gutter={[48, 24]}>
        <Col xs={24} md={16}>
          <Form
            form={form}
            layout="vertical"
            onFinish={handleSave}
            initialValues={initialData}
          >
            <Form.Item label="Họ và tên" name="fullName">
              <Input disabled={!isEditing} />
            </Form.Item>
            <Form.Item label="Email" name="email">
              <Input disabled />
            </Form.Item>
            <Form.Item label="Số điện thoại" name="phone">
              <Input disabled={!isEditing} />
            </Form.Item>

            {isEditing && (
              <Row gutter={16}>
                <Col>
                  <Button type="primary" htmlType="submit" loading={loading}>
                    Lưu
                  </Button>
                </Col>
                <Col>
                  <Button onClick={handleCancel} disabled={loading}>
                    Hủy
                  </Button>
                </Col>
              </Row>
            )}
          </Form>
        </Col>
        <Col xs={24} md={8}>
          <div className={styles.imageUploadSection}>
            <Upload
              name="avatar"
              listType="picture-circle"
              className={styles.avatarUploader}
              showUploadList={false}
              beforeUpload={beforeUpload}
              customRequest={handleAvatarUpload}
            >
              {imageUrl ? (
                <img
                  src={imageUrl}
                  alt="avatar"
                  style={{ width: "100%", height: "100%", objectFit: "cover" }}
                />
              ) : (
                uploadButton
              )}
            </Upload>
            <div className={styles.userNameDisplay}>
              {initialData?.fullName || userInfo?.fullName || "Tên người dùng"}
            </div>
            <div className={styles.userRoleDisplay}>
              {userInfo?.role === "admin" ? "Quản trị viên" : "Khách hàng"}
            </div>
          </div>
        </Col>
      </Row>
    </div>
  );
}

export default MyProfile;