import { wishlistRepository, productRepository } from '../repositories/index.js';
import ResponseDataSuccess from '../utils/response/ResponseDataSuccess.js';
import InternalServerError from '../utils/response/InternalServerError.js';
import NotFoundError from '../utils/response/NotFoundError.js';
import BadRequestError from '../utils/response/BadRequestError.js';

const getWishlist = async (req) => {
  try {
    const accountId = req.user.accountId || req.user.id || req.user._id;
    if (!accountId) {
      throw new BadRequestError('Account ID is required');
    }

    let wishlist = await wishlistRepository.findByAccountId(accountId);

    // Create empty wishlist for new user to keep behaviour consistent with FE expectations
    if (!wishlist) {
      wishlist = await wishlistRepository.create({ accountId, items: [] });
    }

    return new ResponseDataSuccess(wishlist);
  } catch (error) {
    if (error instanceof BadRequestError) {
      return error;
    }
    return new InternalServerError(error.message);
  }
};

const addWishlistItem = async (req) => {
  try {
    const { productId = null, comboId = null } = req.body;
    const accountId = req.user.accountId || req.user.id || req.user._id;

    if (!accountId) {
      throw new BadRequestError('Account ID is required');
    }

    if (comboId) {
      // direct add combo item
      const wishlist = await wishlistRepository.addItem(accountId, {
        comboId,
      });
      return new ResponseDataSuccess(wishlist);
    }

    if (!productId) {
      throw new BadRequestError('Product ID is required');
    }

    // Validate product existence
    const product = await productRepository.findById(productId);
    if (!product) {
      throw new NotFoundError('Product not found');
    }

    const wishlist = await wishlistRepository.addItem(accountId, {
      productId,
    });

    return new ResponseDataSuccess(wishlist);
  } catch (error) {
    if (
      error instanceof NotFoundError ||
      error instanceof BadRequestError
    ) {
      return error;
    }
    return new InternalServerError(error.message);
  }
};

const deleteWishlistItem = async (req) => {
  try {
    const { productId = null, comboId = null } = req.body;
    const accountId = req.user.accountId || req.user.id || req.user._id;

    if (!accountId) {
      throw new BadRequestError('Account ID is required');
    }

    const wishlist = await wishlistRepository.removeItem(accountId, {
      productId,
      comboId,
    });

    return wishlist instanceof NotFoundError
      ? wishlist
      : new ResponseDataSuccess(wishlist);
  } catch (error) {
    if (error instanceof NotFoundError || error instanceof BadRequestError) {
      return error;
    }
    return new InternalServerError(error.message);
  }
};

export default { getWishlist, addWishlistItem, deleteWishlistItem }; 