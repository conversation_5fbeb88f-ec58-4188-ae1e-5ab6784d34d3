import React, { useEffect, useState } from 'react';
import { Table, Tag, Button, Modal, Form, Input, Select, DatePicker, InputNumber, Space, Popconfirm, message } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import VoucherApi from '../../../api/VoucherApi';
import dayjs from 'dayjs';

const { Option } = Select;
const { RangePicker } = DatePicker;

const ManageVouchers = () => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState([]);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingVoucher, setEditingVoucher] = useState(null);
  const [form] = Form.useForm();

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    setLoading(true);
    try {
      const res = await VoucherApi.list();
      setData(res.data?.data?.rows || []);
    } catch (err) {
      console.error(err);
      message.error('Lỗi khi tải danh sách voucher');
    } finally {
      setLoading(false);
    }
  };

  const handleCreate = () => {
    setEditingVoucher(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleEdit = (record) => {
    setEditingVoucher(record);
    const formData = {
      ...record,
      dateRange: record.conditions?.startDate && record.conditions?.endDate 
        ? [dayjs(record.conditions.startDate), dayjs(record.conditions.endDate)]
        : undefined,
      minOrderValue: record.conditions?.minOrderValue,
      usageLimit: record.conditions?.usageLimit,
    };
    form.setFieldsValue(formData);
    setIsModalVisible(true);
  };

  const handleDelete = async (id) => {
    try {
      await VoucherApi.remove(id);
      message.success('Xóa voucher thành công!');
      fetchData();
    } catch (err) {
      message.error('Lỗi khi xóa voucher');
    }
  };

  const handleModalOk = async () => {
    try {
      const values = await form.validateFields();
      const payload = {
        code: values.code,
        type: values.type,
        value: values.value,
        status: values.status || 'ACTIVE',
        conditions: {
          minOrderValue: values.minOrderValue || 0,
          usageLimit: values.usageLimit || 0,
          startDate: values.dateRange?.[0]?.toISOString(),
          endDate: values.dateRange?.[1]?.toISOString(),
        }
      };

      if (editingVoucher) {
        await VoucherApi.update(editingVoucher._id, payload);
        message.success('Cập nhật voucher thành công!');
      } else {
        await VoucherApi.create(payload);
        message.success('Tạo voucher thành công!');
      }
      
      setIsModalVisible(false);
      fetchData();
    } catch (err) {
      console.error(err);
      message.error('Có lỗi xảy ra');
    }
  };

  const handleModalCancel = () => {
    setIsModalVisible(false);
    form.resetFields();
  };

  // Thêm hàm disabledDate
  const disabledDate = (current) => {
    return current && current < dayjs().startOf('day');
  };

  const columns = [
    {
      title: 'Mã Code',
      dataIndex: 'code',
      key: 'code',
      width: 120,
    },
    {
      title: 'Loại',
      dataIndex: 'type',
      key: 'type',
      width: 100,
      render: (type) => type === 'PERCENT' ? 'Phần trăm' : 'Số tiền',
    },
    {
      title: 'Giá trị',
      dataIndex: 'value',
      key: 'value',
      width: 120,
      render: (val, record) =>
        record.type === 'PERCENT' ? `${val}%` : `${val.toLocaleString()} VND`,
    },
    {
      title: 'Đơn tối thiểu',
      key: 'minOrder',
      width: 120,
      render: (_, record) => 
        record.conditions?.minOrderValue 
          ? `${record.conditions.minOrderValue.toLocaleString()} VND`
          : 'Không',
    },
    {
      title: 'Giới hạn dùng',
      key: 'usageLimit',
      width: 120,
      render: (_, record) => 
        record.conditions?.usageLimit || 'Không giới hạn',
    },
    {
      title: 'Đã dùng',
      dataIndex: 'usedCount',
      key: 'usedCount',
      width: 80,
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => (
        <Tag color={status === 'ACTIVE' ? 'green' : status === 'EXPIRED' ? 'red' : 'orange'}>
          {status === 'ACTIVE' ? 'Hoạt động' : status === 'EXPIRED' ? 'Hết hạn' : 'Ẩn'}
        </Tag>
      ),
    },
    {
      title: 'Thao tác',
      key: 'actions',
      width: 120,
      render: (_, record) => (
        <Space>
          <Button 
            type="link" 
            icon={<EditOutlined />} 
            onClick={() => handleEdit(record)}
            size="small"
          />
          <Popconfirm
            title="Bạn có chắc muốn xóa voucher này?"
            onConfirm={() => handleDelete(record._id)}
            okText="Có"
            cancelText="Không"
          >
            <Button 
              type="link" 
              danger 
              icon={<DeleteOutlined />} 
              size="small"
            />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
        <h2>Quản lý Voucher</h2>
        <Button type="primary" icon={<PlusOutlined />} onClick={handleCreate}>
          Thêm Voucher
        </Button>
      </div>
      
      <Table
        rowKey="_id"
        columns={columns}
        dataSource={data}
        loading={loading}
        pagination={{ pageSize: 10 }}
        scroll={{ x: 800 }}
      />

      <Modal
        title={editingVoucher ? 'Sửa Voucher' : 'Thêm Voucher'}
        open={isModalVisible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        width={600}
        okText={editingVoucher ? 'Cập nhật' : 'Tạo'}
        cancelText="Hủy"
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="code"
            label="Mã Code"
            rules={[{ required: true, message: 'Vui lòng nhập mã code!' }]}
          >
            <Input placeholder="VD: SALE20" style={{ textTransform: 'uppercase' }} />
          </Form.Item>

          <Form.Item
            name="type"
            label="Loại giảm giá"
            rules={[{ required: true, message: 'Vui lòng chọn loại!' }]}
          >
            <Select placeholder="Chọn loại">
              <Option value="PERCENT">Phần trăm (%)</Option>
              <Option value="AMOUNT">Số tiền cố định</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="value"
            label="Giá trị"
            rules={[{ required: true, message: 'Vui lòng nhập giá trị!' }]}
          >
            <InputNumber 
              placeholder="VD: 20 (cho 20% hoặc 20000 VND)" 
              style={{ width: '100%' }}
              min={1}
            />
          </Form.Item>

          <Form.Item
            name="minOrderValue"
            label="Giá trị đơn hàng tối thiểu (VND)"
          >
            <InputNumber 
              placeholder="VD: 100000" 
              style={{ width: '100%' }}
              min={0}
            />
          </Form.Item>

          <Form.Item
            name="usageLimit"
            label="Giới hạn số lần sử dụng"
          >
            <InputNumber 
              placeholder="Để trống = không giới hạn" 
              style={{ width: '100%' }}
              min={0}
            />
          </Form.Item>

          <Form.Item
            name="dateRange"
            label="Thời gian hiệu lực"
            rules={[
              { 
                required: true, 
                message: 'Vui lòng chọn thời gian hiệu lực!' 
              },
              {
                validator: async (_, value) => {
                  if (value && value[0] && value[1]) {
                    if (value[0].isBefore(dayjs().startOf('day'))) {
                      throw new Error('Ngày bắt đầu không thể là ngày trong quá khứ!');
                    }
                    if (value[1].isBefore(value[0])) {
                      throw new Error('Ngày kết thúc phải sau ngày bắt đầu!');
                    }
                  }
                }
              }
            ]}
          >
            <RangePicker 
              showTime
              style={{ width: '100%' }}
              placeholder={['Ngày bắt đầu', 'Ngày kết thúc']}
              disabledDate={disabledDate}
            />
          </Form.Item>

          <Form.Item
            name="status"
            label="Trạng thái"
            initialValue="ACTIVE"
          >
            <Select>
              <Option value="ACTIVE">Hoạt động</Option>
              <Option value="HIDDEN">Ẩn</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ManageVouchers; 