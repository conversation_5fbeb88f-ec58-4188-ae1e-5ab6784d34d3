import pick from 'lodash/pick.js';
import { userService } from "../services/index.js";
import { ErrorCodes } from '../constants/errorCode.js';
import { HttpStatusCode } from 'axios';
import multer from 'multer';

const findAll = async (req, res) => {
    const filters = pick(req.query, ["search", "role", "isBlocked"]);
    const options = pick(req.query, ["page", "pageSize", "sortField", "sortOrder"]);
    try {
        const response = await userService.search(filters, options);
        return res.status(200).send(response);
    } catch (error) {
        return res.status(500).send({
            success: false,
            message: error.message || "Internal server error"
        });
    }
};

const getByAccountId = async (req, res) => {
    const { accountId } = req.params;
    const currentAccount = req.user;
    try {
        if (currentAccount.accountId !== accountId && currentAccount.role.length === 1 && currentAccount.role.every(role => role === 'USER')) {
            return res.status(HttpStatusCode.Forbidden).send({ status: HttpStatusCode.Forbidden, message: ErrorCodes.EC023 });
        }
        const response = await userService.getByAccountId(accountId);
        return res.status(response.status).send(response);
    } catch (error) {
        return res.status(HttpStatusCode.Unauthorized).send({ status: HttpStatusCode.Unauthorized, message: error.message });
    }
};

const updateProfile = async (req, res) => {

}

const getDashboardNewUser = async (req, res) => {
    try {
        const response = await userService.getDashboardNewUser();
        return res.status(200).send(response);
    } catch (error) {
        return res.status(500).send({
            status: 500,
            message: error.message || "Internal server error"
        });
    }
};

const update = async (req, res) => {
    const { accountId } = req.params;
    const data = await userService.update(accountId, req.body, req.user);
    if (data.status === HttpStatusCode.Ok) {
        return res.status(data.status).send(data.data);
    }
    return res.status(data.status).send(data.error);
}

const updateAvatar = async (req, res) => {
    try {
        const { accountId } = req.params;

        if (!req.file) {
            return res.status(HttpStatusCode.BadRequest).send({
                success: false,
                message: "Không tìm thấy tệp ảnh đại diện.",
            });
        }
        
        // Construct the file path to be saved in the database
        const filePath = `/images/avatars/${req.file.filename}`;

        const result = await userService.updateAvatar(accountId, filePath);

        return res.status(result.status).send(result);
    } catch (error) {
        // Multer might throw an error (e.g., file size limit)
        if (error instanceof multer.MulterError) {
            return res.status(HttpStatusCode.BadRequest).send({
                success: false,
                message: `Lỗi tải tệp: ${error.message}`,
            });
        }
        return res.status(HttpStatusCode.InternalServerError).send({
            success: false,
            message: error.message || "Lỗi máy chủ nội bộ",
        });
    }
}

export default { findAll, getByAccountId, updateProfile, getDashboardNewUser, update, updateAvatar };
