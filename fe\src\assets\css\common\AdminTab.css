.admin-tab {
  width: 180px;
  height: 100vh;
  background-color: #2c3e50;
  color: white;
  padding: 20px;
  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.2);
  position: fixed;
  left: 0;
  top: 0;
  display: flex;
  flex-direction: column;
}

.admin-title {
  font-size: 26px;
  font-weight: bold;
  margin-bottom: 20px;
  text-align: center;
}

.dashboard-link {
  text-decoration: none;
  color: white;
  display: block;
  padding: 10px 0;
  transition: color 0.3s ease;
}

.dashboard-link:hover {
  color: #1abc9c;
  /* <PERSON><PERSON>u xanh ngọc khi hover */
}

.admin-menu {
  list-style: none;
  padding: 0;
}

.admin-item {
  font-size: 18px;
  padding: 12px 15px;
  margin-bottom: 10px;
  background-color: #34495e;
  /* <PERSON><PERSON><PERSON> nền mặc định */
  border-radius: 5px;
  transition: background 0.3s ease;
  text-align: center;
}

.admin-item a {
  text-decoration: none;
  color: white;
  display: block;
  transition: color 0.3s ease;
  /* Thêm transition cho màu chữ */
}

.admin-item:hover {
  background-color: #1abc9c;
  /* <PERSON><PERSON>u xanh ngọc khi hover */
}

/* Style cho trạng thái active */
.admin-item.active {
  background-color: #1abc9c;
  /* Màu xanh ngọc cho mục đang chọn */
  font-weight: bold;
  /* In đậm để nổi bật */
}

.admin-item.active a {
  color: white;
  /* Giữ màu chữ trắng khi active */
}