.sliderContainer {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.mainSwiper {
    width: 100%;
    overflow: hidden;
    display: flex;
    justify-content: center;
}

.slideItem {

    display: flex;
    justify-content: center;
}



.mainImage {

    max-width: 500px;
    max-height: 500px;
    width: 100%;
    height: 100%;
    /* max-height: 400px; */
    object-fit: cover;
    border-radius: 10px;
    aspect-ratio: 1 / 1;
}

.swiper-slide, .swiper-slide-active {
    display: flex;
    justify-content: center;
    padding: 4px;
}

.thumbSwiper {
    width: 100%;
    display: flex;
    box-sizing: border-box;
    padding: 8px;
}

.thumbnail {
    border: 1px solid var(--primary-color);
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 5px;
    cursor: pointer;
    transition: transform 0.2s ease-in-out;

}

.thumbnail:hover {
    transform: scale(1.1);
}

.swiper-pagination-bullet {
    background: #ccc;
    opacity: 0.5;
}

.swiper-pagination-bullet-active {
    background: #007aff;
    opacity: 1;
}

.swiper-slide-active {
    display: flex;
    justify-content: center;
}