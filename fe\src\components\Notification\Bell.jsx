import { useEffect } from 'react';
import { BellOutlined } from '@ant-design/icons';
import { Badge, Dropdown, List } from 'antd';
import { useDispatch, useSelector } from 'react-redux';
import { addNotification, fetchNotifications } from '../../redux/slices/notificationSlice';
import { io } from 'socket.io-client';
import NotificationApi from '../../api/NotificationApi';
import s from './Bell.module.css';

const Bell = () => {
  const dispatch = useDispatch();
  const { items, unreadCount } = useSelector((state) => state.notifications);
  const { userInfo } = useSelector((state) => state.user);

  useEffect(() => {
    if (!userInfo) return;
    dispatch(fetchNotifications({ page: 1, pageSize: 10 }));
    const socket = io(import.meta.env.VITE_BE_WS || 'http://localhost:3000', {
      query: { userId: userInfo.userId },
      transports: ['websocket'],
    });
    socket.on('notification', (data) => {
      dispatch(addNotification(data));
    });
    return () => socket.disconnect();
  }, [userInfo, dispatch]);

  const handleItemClick = async (item) => {
    if (!item.isRead) {
      await NotificationApi.markRead(item._id);
    }
    // navigate if needed based on item.type
  };

  const menu = (
    <List
      style={{ width: 300, maxHeight: 400, overflow: 'auto', background: '#fff', boxShadow:'0 2px 8px rgba(0,0,0,0.15)', borderRadius:4, padding:20 }}
      dataSource={items}
      renderItem={(item) => (
        <List.Item onClick={() => handleItemClick(item)} style={{ cursor: 'pointer' }}>
          <List.Item.Meta
            title={<span style={{ fontWeight: item.isRead ? 'normal' : 'bold' }}>{item.title}</span>}
            description={item.message}
          />
        </List.Item>
      )}
    />
  );

  return (
    <Dropdown overlay={menu} trigger={['click']} placement="bottomRight" className={s.bell}>
      <Badge count={unreadCount} overflowCount={99}>
        <BellOutlined style={{ fontSize: 24, color: '#fff' }} />
      </Badge>
    </Dropdown>
  );
};

export default Bell; 