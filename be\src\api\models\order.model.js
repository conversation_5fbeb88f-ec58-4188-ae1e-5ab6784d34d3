import mongoose from 'mongoose';
import { COLLECTIONS, MODELS, ORDER_STATUS } from '../utils/constants.js';
import { District, Province, Ward } from './user.model.js';

const orderItemSchema = new mongoose.Schema(
  {
    productId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: MODELS.PRODUCT,
    },
    comboId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: MODELS.COMBO,
    },
    variantId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: MODELS.VARIANT,
    },
    briefInfo: {
      quantity: {
        type: Number,
        required: true,
      },
      price: {
        type: Number,
        required: true,
      },
      name: {
        type: String,
        required: true,
      },
      variant: {
        type: String,
      },
      type: {
        type: String,
        enum: ['product', 'combo'],
        required: true,
      },
      image: {
        type: String,
      },
    },
  },
  {
    _id: false,
  }
);

const deliveryAddressSchema = new mongoose.Schema(
  {
    fullName: {
      type: String,
      required: true,
    },
    phone: {
      type: String,
      required: true,
      maxlength: 10,
    },
    address: {
      type: String,
      required: true,
      maxlength: 255,
    },
    province: {
      type: Province,
      required: true,
    },
    district: {
      type: District,
      required: true,
    },
    ward: { type: Ward, required: true },
    note: {
      type: String,
    },
  },
  {
    _id: false,
  }
);

const paymentSchema = new mongoose.Schema(
  {
    method: {
      type: String,
      required: true,
    },
    paymentId: {
      type: String,
    },
    paid: {
      type: Boolean,
      required: true,
    },
    transactionDate: {
      type: String,
    },
    refunded: {
      type: Boolean,
      required: true,
    },
  },
  {
    _id: false,
  }
);

const shippingSchema = new mongoose.Schema(
  {
    method: {
      type: String,
    },
    fee: {
      type: Number,
    },
  },
  {
    _id: false,
  }
);

const voucherSchema = new mongoose.Schema(
  {
    voucherId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: MODELS.VOUCHER,
    },
    code: String,
    discount: Number,
  },
  { _id: false }
);

const orderSchema = new mongoose.Schema(
  {
    id: {
      type: String,
      // This will automatically convert _id to string and store it in id
      default: function () {
        return this._id.toString();
      },
    },
    items: [orderItemSchema],
    orderedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: MODELS.USER,
      required: true,
    },
    deliveryAddress: deliveryAddressSchema,
    payment: paymentSchema,
    voucher: voucherSchema,
    status: {
      type: String,
      enum: [
        ORDER_STATUS.PENDING,
        ORDER_STATUS.APPROVED,
        ORDER_STATUS.DELIVERING,
        ORDER_STATUS.DELIVERED,
        ORDER_STATUS.CANCELED,
      ],
      default: 'PENDING',
    },
    shipping: shippingSchema,
    totalPrice: {
      type: Number,
      required: true,
    },
    approvedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: MODELS.USER,
    },
    approvedDate: {
      type: Date,
    },
    canceledBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: MODELS.USER,
    },
    canceledReason: {
      type: String,
    },
  },
  {
    collection: COLLECTIONS.ORDER,
    timestamps: true,
    toJSON: {
      transform: function (doc, ret) {
        delete ret.isDeleted;
        delete ret.id;
        delete ret.__v;
        return ret;
      },
    },
    toObject: {
      transform: function (doc, ret) {
        delete ret.isDeleted;
        delete ret.id;
        delete ret.__v;
        return ret;
      },
    },
  }
);

// Add text index on relevant fields
orderSchema.index({ id: 1 });
orderSchema.index({ 'deliveryAddress.phone': 1 });
orderSchema.index({ orderedBy: 1 });
orderSchema.index(
  {
    'items.briefInfo.name': 'text',
    'deliveryAddress.fullName': 'text',
  },
  {
    weights: {
      'items.briefInfo.name': 2,
      'deliveryAddress.fullName': 1,
    },
    default_language: 'none',
  }
);

const Order = mongoose.model(MODELS.ORDER, orderSchema, COLLECTIONS.ORDER);

export default Order;
export { paymentSchema };
