import mongoose from "mongoose";
import { ROLES } from "../constants/role.js";
import { type } from "os";

const statusSchema = new mongoose.Schema({
    isBlocked: {
        type: Boolean,
        default: false,
        null: false,
    },
    reasonBlock: {
        type: String,
        default: "",
    }
}, { _id: false });



const activationSchema = new mongoose.Schema({
    token: {
        type: String,
    },
    expires: {
        type: Date,
    }
}, { _id: false });

const resetPWTokenSchema = new mongoose.Schema({
    token: {
        type: String
    },
    expires: {
        type: Date,
    }
}, { _id: false })
const accountSchema = new mongoose.Schema(
    {
        email: {
            type: String,
            unique: true,
            required: true,
        },
        password: {
            type: String,
            required: true,
        },
        userId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: "User",
            required: true,
        },
        googleId: {
            type: String,
            default: null,
        },
        status: { type: statusSchema, default: { isBlocked: false, reasonBlock: "" } },
        role: {
            type: [String],
            default: ROLES.USER,
            required: true,
        },
        refreshToken: {
            type: String,
            sparse: true,
        },
        activation: activationSchema,
        resetPWToken: resetPWTokenSchema
    },
    {
        collection: "Account",
        timestamps: true,
    }
);

const Account = mongoose.model("Account", accountSchema, "ph_accounts");

export default Account;