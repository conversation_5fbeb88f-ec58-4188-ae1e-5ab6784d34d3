import mongoose from 'mongoose';
import pkg from 'lodash';
import pagination from '../helper/pagination.js';
import Product from '../models/product.model.js';
import { variantRepository } from './index.js';
import BadRequestError from '../utils/response/BadRequestError.js';
import NotFoundError from '../utils/response/NotFoundError.js';

const search = async (filters, options) => {
  const { page, pageSize } = options;
  let { sort } = options;
  const { offset, limit } = pagination(page, pageSize);

  if (!sort) sort = { updatedAt: -1 };

  const rows = await Product.find(filters)
    .select(
      '_id name categoryId quantity price images thumbnail isHide description options ratingAvg ratingCount'
    )
    .sort(sort)
    .skip(offset)
    .limit(limit)
    .populate({ path: 'categoryId', select: 'name _id', as: 'category' });
  const count = await Product.countDocuments(filters);
  return {
    count,
    rows,
  };
};

const create = async (data) => {
  try {
    const { variants, ...productData } = data;

    if (variants && variants.length > 0) {
      let defaultVariant = variants.find((v) => v.isDefault);

      if (!defaultVariant) {
        // Set the first variant as default if none is specified
        variants[0].isDefault = true;
        defaultVariant = variants[0];
      }
      productData.price = defaultVariant.price;
      productData.quantity = defaultVariant.quantity;
    }

    const createdProduct = new Product(productData);
    await createdProduct.save();

    let createdVariants = [];
    if (data.options?.length > 0 && variants?.length > 0) {
      createdVariants = await variantRepository.create(
        variants.map((v) => ({ ...v, productId: createdProduct._id }))
      );
    }

    return { product: createdProduct, variants: createdVariants };
  } catch (error) {
    throw new BadRequestError(error.message);
  }
};

const findDetailById = async (id, showHide) => {
  const product = await findById(id, showHide);
  if (!product) {
    return new NotFoundError('Product not found');
  }
  const variants = await variantRepository.findByProductId(id);
  return { product, variants };
};

// This can show some basic information of the product in the order detail although the product is deleted
const findDetailInOrderById = async (id) => {
  const product = await Product.findById(id);

  if (!product) {
    return new NotFoundError('Product not found');
  }

  const variants = product.isDeleted
    ? null
    : await variantRepository.findByProductId(id);
  return {
    product: { ...product.toObject(), isDeleted: product.isDeleted },
    variants,
  };
};

const findById = async (id, showHide = true) => {
  const filters = { _id: id, isDeleted: false };
  if (!showHide) {
    filters.isHide = false;
  }
  return await Product.findOne(filters).populate({
    path: 'categoryId',
    select: 'name _id',
    as: 'category',
  });
};

const update = async (id, data = {}) => {
  try {
    console.log(`[product.repository] Starting update for product ${id} with data:`, JSON.stringify(data, null, 2));
    const product = await Product.findById(id);
    if (!product) {
      console.error(`[product.repository] Product with id ${id} not found.`);
      throw new NotFoundError('Product not found');
    }

    const { variants: newVariants, ...productData } = data;

    // Direct assignment for product fields
    Object.assign(product, productData);
    
    // Force replace arrays
    if (data.images !== undefined) {
      product.images = data.images;
    }
    if (data.options !== undefined) {
      product.options = data.options;
    }

    if (newVariants !== undefined) {
      console.log(`[product.repository] Updating variants for product ${id}.`);
      // Delete all existing variants
      await variantRepository.deleteByProductId(id);

      // Create new variants
      const createdVariants = await variantRepository.create(
        newVariants.map(v => ({...v, productId: id}))
      );
      console.log(`[product.repository] Created ${createdVariants.length} new variants.`);

      let defaultVariant = createdVariants.find((v) => v.isDefault);
      if (!defaultVariant && createdVariants.length > 0) {
        defaultVariant = createdVariants[0];
        defaultVariant.isDefault = true;
        
        const defaultVariantToSave = await variantRepository.findById(defaultVariant._id);
        if(defaultVariantToSave) {
          defaultVariantToSave.isDefault = true;
          await defaultVariantToSave.save();
        }
      }

      if(defaultVariant) {
        console.log(`[product.repository] Setting default variant for product ${id}.`);
        product.price = defaultVariant.price;
        product.quantity = defaultVariant.quantity;
      }
    }

    await product.save();

    const updatedVariants = await variantRepository.findByProductId(id);
    return { product, variants: updatedVariants };
  } catch (error) {
    throw new BadRequestError(error.message);
  }
};

const deleteProduct = async (id) => {
  try {
    const product = await Product.findById(id);
    if (!product) {
      throw new NotFoundError('Product not found');
    }

    product.isDeleted = true;
    await product.save();

    await variantRepository.deleteByProductId(id);

    return true;
  } catch (error) {
    throw new BadRequestError(error.message);
  }
};

const updateRating = async (productId, avgRating, ratingCount) => {
  await Product.findByIdAndUpdate(productId, {
    ratingAvg: avgRating,
    ratingCount: ratingCount,
  });
};

export default {
  search,
  create,
  findDetailById,
  update,
  deleteProduct,
  findDetailInOrderById,
  findById,
  updateRating,
};
