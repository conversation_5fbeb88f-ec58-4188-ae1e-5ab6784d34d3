import config from "./api/config/config.js";
import { app } from "./app.js";
import http from "http";
import { initSocket } from './socket/index.js';

async function startServer() {
  try {
    // const localIP = getPhysicalIPv4();
    // console.log("IP: ", localIP);

    const PORT = config.port || 3000;
    const HOSTNAME = config.host || "localhost";

    // const PORT = 3000;
    // const HOSTNAME = "0.0.0.0";

    const server = http.createServer(app);

    // Initialize socket.io
    initSocket(server);

    server.listen(PORT, HOSTNAME, () => {
      console.log(`Server running at: http://${HOSTNAME}:${PORT}`);
    });
  } catch (error) {
    console.error("Failed to start server:", error);
    process.exit(1);
  }
}

startServer();
