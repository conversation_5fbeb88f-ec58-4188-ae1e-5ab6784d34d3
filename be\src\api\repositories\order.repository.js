import pagination from '../helper/pagination.js';
import Order from '../models/order.model.js';

const search = async (filters, options) => {
  const { page, pageSize } = options;
  let { sort } = options;
  const { offset, limit } = pagination(page, pageSize);

  if (!sort) {
    sort = { createdAt: -1 };
  }

  const rows = await Order.find(filters)
    .select()
    .sort(sort)
    .skip(offset)
    .limit(limit);
  const count = await Order.countDocuments(filters);
  return {
    count,
    rows,
  };
};

const findOrderByIds = async (orderIds) =>
  await Order.find({ _id: { $in: orderIds } });

const findOrderById = async (orderId) =>
  await Order.findById(orderId)
    .populate({
      path: 'orderedBy',
      select: 'fullName phone',
    })
    .populate({
      path: 'items.productId',
      select: 'name price',
    })
    .populate({
      path: 'items.variantId',
      select: 'price',
    });
const create = async (data, options) => await Order.create(data, options);

const update = async (orderId, data, options) =>
  await Order.findByIdAndUpdate(orderId, data, options);

export default {
  create,
  update,
  findOrderById,
  findOrderByIds,
  search,
  hasUserPurchasedProduct,
  hasUserPurchasedCombo,
};

async function hasUserPurchasedProduct(userId, productId) {
  const existed = await Order.exists({
    orderedBy: userId,
    status: 'DELIVERED',
    'items.productId': productId,
  });
  return !!existed;
}

async function hasUserPurchasedCombo(userId, comboId) {
  const existed = await Order.exists({
    orderedBy: userId,
    status: 'DELIVERED',
    'items.comboId': comboId,
  });
  return !!existed;
}
