import pick from 'lodash/pick.js';
import employeeService from '../services/employee.service.js';
import { HttpStatusCode } from 'axios';

// List / search employees
const search = async (req, res) => {
  const filters = pick(req.query, ['search', 'isBlocked']);
  const options = pick(req.query, ['page', 'pageSize', 'sortField', 'sortOrder']);
  const data = await employeeService.search(filters, options);
  return res.status(data.status || HttpStatusCode.Ok).send(data.data || data);
};

// Create employee
const create = async (req, res) => {
  const data = await employeeService.create(req.body);
  return res.status(data.status || HttpStatusCode.Created).send(data.data || data);
};

// Toggle block/unblock
const toggleStatus = async (req, res) => {
  const { accountId } = req.params;
  const { reasonBlock } = req.body;
  const data = await employeeService.toggleStatus(accountId, reasonBlock);
  return res.status(data.status || HttpStatusCode.Ok).send(data.data || data);
};

// Update role
const updateRole = async (req, res) => {
  const { accountId } = req.params;
  const { role } = req.body;
  const data = await employeeService.updateRole(accountId, role);
  return res.status(data.status || HttpStatusCode.Ok).send(data.data || data);
};

// Update info
const updateInfo = async (req, res) => {
  const { accountId } = req.params;
  const data = await employeeService.updateInfo(accountId, req.body);
  return res.status(data.status || HttpStatusCode.Ok).send(data.data || data);
};

export default { search, create, toggleStatus, updateRole, updateInfo };
