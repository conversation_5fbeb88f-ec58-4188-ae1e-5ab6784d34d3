import mongoose from 'mongoose';

export const Province = new mongoose.Schema(
  {
    provinceId: String,
    provinceName: String,
  },
  { _id: false }
);

export const District = new mongoose.Schema(
  { districtId: String, districtName: String },
  {
    _id: false,
  }
);

export const Ward = new mongoose.Schema(
  { wardId: String, wardName: String },
  {
    _id: false,
  }
);

const deliveryAddressSchema = new mongoose.Schema(
  {
    fullName: {
      type: String,
      required: true,
    },
    phone: {
      type: String,
      required: true,
      maxlength: 10,
    },
    address: {
      type: String,
      required: true,
      maxlength: 255,
    },
    province: {
      type: Province,
      required: true,
    },
    district: {
      type: District,
      required: true,
    },
    ward: { type: Ward, required: true },

    isDefault: {
      type: Boolean,
      default: false,
    },
  },
  {
    collection: 'DeliveryAddress',
    timestamps: true,
  }
);

const userSchema = new mongoose.Schema(
  {
    fullName: {
      type: String,
      required: true,
      maxlength: 30,
      minlength: 6,
    },
    address: {
      type: String,
    },
    phone: {
      type: String,
      match: [/^\d{10}$/, '<PERSON><PERSON> điện thoại phải có đúng 10 chữ số'],
    },
    deliveryAddress: { type: [deliveryAddressSchema], default: [] },
    avatar: {
      type: String,
    },
  },
  {
    collection: 'User',
    timestamps: true,
  }
);

const User = mongoose.model('User', userSchema, 'ph_users');

export default User;
export { deliveryAddressSchema };
