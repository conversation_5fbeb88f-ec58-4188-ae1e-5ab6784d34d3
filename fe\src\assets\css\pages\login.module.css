.login-container {
  height: 100vh;
  padding: 20px;
  box-sizing: border-box;
  display: flex;
  justify-content: center;
  align-items: center;
  /* background-color: #F8F9FE; */
  background-image: url('../images/login-background (1).png');
  /* <PERSON><PERSON>ờng dẫn ảnh nền */
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}


/* Card styles */
.login-card {
  max-height: 800px;
  height: 100%;
  width: 800px !important;
  border-radius: 16px !important;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12) !important;
  overflow: hidden;
}

.login-card .ant-card-body {
  padding: 0 !important;
  display: flex;
  min-height: 480px;
  height: 100%;
}

/* Login form container */
.login-form-container {
  flex: 1;
  padding: 30px;
  background-color: white;
}

.login-title {
  color: #1f2937 !important;
  margin-bottom: 32px !important;
  font-weight: 700 !important;
  font-size: 34px !important;
}

.login-form .ant-form-item {
  margin-bottom: 24px;
}

/* Input styles */
.login-input {
  height: 45px !important;
  border-radius: 8px !important;
}

.login-input:hover,
.login-input:focus {
  border-color: #4096ff !important;
  box-shadow: 0 0 0 2px rgba(64, 150, 255, 0.1) !important;
}

.input-icon {
  color: #9ca3af;
}

/* Button styles */
.login-button {
  height: 45px !important;
  border-radius: 8px !important;
  font-weight: 500 !important;
  font-size: 16px !important;
  background: linear-gradient(90deg, #4096ff 0%, #1677ff 100%) !important;
  border: none !important;
  box-shadow: 0 4px 12px rgba(64, 150, 255, 0.25) !important;
  transition: all 0.3s ease !important;
}

.login-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(64, 150, 255, 0.35) !important;
}

.login-button:active {
  transform: translateY(0);
}

/* Illustration container */
.login-illustration {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #e6f3ff 0%, #f0f5ff 100%);
  /* height: 100%;  */
  width: 100%;
  /* Đảm bảo chiều rộng là 100% */
  overflow: hidden;
  /* Ẩn phần dư thừa của hình ảnh */
}

.illustration-image {
  width: 140%;
  /* Chiếm toàn bộ chiều rộng */
  height: 100%;
  /* Chiếm toàn bộ chiều cao */
  object-fit: cover;
  /* Đảm bảo hình ảnh phủ kín mà không bị méo */
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .login-card {
    width: 95% !important;
    max-width: 480px;
  }

  .login-card .ant-card-body {
    flex-direction: column;
    /* Đổi hướng layout thành dọc */
  }

  .login-illustration {
    height: 200px;
    /* Chiều cao cố định cho phần hình ảnh trên mobile */
    padding: 0;
    /* Loại bỏ padding */
  }

  .illustration-image {
    height: 100%;
    /* Đảm bảo hình ảnh vẫn fit */
    width: auto;
    /* Giữ nguyên tỷ lệ hình ảnh */
  }
}

/* Form validation styles */
.ant-form-item-explain-error {
  font-size: 13px;
  margin-top: 4px;
  color: #ff4d4f;
}

/* Focus states */
.ant-input-affix-wrapper-focused {
  border-color: #4096ff !important;
  box-shadow: 0 0 0 2px rgba(64, 150, 255, 0.1) !important;
}


.login-title {
  text-align: center !important;
}

/* Style for the "Don't have an account" text */
.register-link {
  font-weight: 500;
  color: #1677ff;
  text-decoration: none;
}

.register-link:hover {
  text-decoration: underline;
}

/* Style for the Google login button */
.google-login-button {
  background: #fff !important;
  color: #757575 !important;
  border: 1px solid #ddd !important;
  font-weight: 500 !important;
  display: flex;
  align-items: center;
  justify-content: center;
}

.google-login-button:hover {
  background: #f5f5f5 !important;
  border-color: #ccc !important;
}