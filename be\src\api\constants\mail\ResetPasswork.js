import config from "../../config/config.js";

const ResetPasswordEmail = (email, resetToken) => {
    const resetPasswordLink = `http://localhost:5173/reset/confirm/${resetToken}`;

    return `
    <!DOCTYPE html>
    <html lang="vi">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Đổi Lại Mật Khẩu - PetHaven</title>
    </head>
    <body>
        <h2>Chào ${email},</h2>
        <p>Chúng tôi nhận được yêu cầu đổi lại mật khẩu cho tài khoản của bạn.</p>
        <p>Vui lòng nhấn vào nút bên dưới để đổi lại mật khẩu:</p>
        <p><strong>Lưu ý:</strong> Link này sẽ hết hạn sau ${config.resetPasswordTokenLife} phút.</p>
        <a href="${resetPasswordLink}" style="display: inline-block; padding: 12px 20px; background-color: #28a745; color: #ffffff; text-decoration: none; border-radius: 5px; border: none; cursor: pointer;">
            Đổi Lại Mật Khẩu
        </a>
        <p>Nếu bạn không yêu cầu đổi mật khẩu, vui lòng bỏ qua email này.</p>
    </body>
    </html>`;
};

export const ResetPasswordTitleMail = "Đổi Lại Mật Khẩu - PetHaven";
export default ResetPasswordEmail;