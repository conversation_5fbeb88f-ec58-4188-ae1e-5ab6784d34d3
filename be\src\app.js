import express from "express";
import bodyParser from "body-parser";
import morgan from "morgan";
import helmet from "helmet";
import dotenv from "dotenv";
import {
  handleBadRequest,
  handleNotFound,
  handleServerErrors,
} from "./api/middlewares/index.js";
import cors from "cors";

import router from "./api/routes/index.js";
import instanceMongoDb from "./api/database/connect.mongodb.js";
import passport from "passport";
import session from "express-session";
import GoogleStrategy from "passport-google-oauth20";
import config from "./api/config/config.js";
import cookieParser from "cookie-parser";
import path from "path";
import { fileURLToPath } from "url";

const app = express();
dotenv.config();

// =================================================================
// <PERSON><PERSON><PERSON> hình CORS được đưa lên ĐẦU TIÊN
// This will handle preflight requests and set CORS headers for all responses.
app.use(
  cors({
    origin: ["http://localhost:5173"],
    methods: "GET,HEAD,PUT,PATCH,POST,DELETE",
    allowedHeaders: "X-Requested-With, Content-Type, Authorization",
    credentials: true,
  })
);
// =================================================================

instanceMongoDb;

//cau hình file cho css vnpay
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
app.use(
  "/stylesheets",
  express.static(path.join(__dirname, "../stylesheets"))
);
app.set("view engine", "jade");

// Serve static files from the "public" directory
app.use(express.static(path.join(__dirname, "public")));

app.use(express.json({ limit: "5mb" }));
app.use(express.urlencoded({ limit: "5mb", extended: true }));
app.use(express.json());
app.use(cookieParser());
app.use(bodyParser.json());
app.use(morgan("dev"));
app.use(helmet());
app.set("json replacer", (key, value) => {
  if (typeof value === "object" && value !== null) {
    return JSON.parse(JSON.stringify(value));
  }
  return value;
});

app.use((req, res, next) => {
  res.setHeader("Cross-Origin-Opener-Policy", "unsafe-none");
  next();
});

app.use(
  session({
    secret: config.sessionSecret,
    resave: false,
    saveUninitialized: true,
  })
);

// Initialize Passport
app.use(passport.initialize());
app.use(passport.session());
passport.use(
  new GoogleStrategy(
    {
      clientID: config.GOOGLE.clientId,
      clientSecret: config.GOOGLE.clientSecret,
      callbackURL: `http://localhost:3000/api/v1/auth/google/callback`,
    },
    (accessToken, refreshToken, profile, done) => {
      return done(null, profile);
    }
  )
);

passport.serializeUser((user, done) => done(null, user));
passport.deserializeUser((user, done) => done(null, user));

// code .
app.use("/api/v1", router);
app.use(handleBadRequest);
app.use(handleNotFound);
app.use(handleServerErrors);

export { app };
