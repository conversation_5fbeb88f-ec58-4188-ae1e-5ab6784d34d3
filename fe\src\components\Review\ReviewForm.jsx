import { useState } from 'react';
import { Button, Form, Input, message } from 'antd';
import RatingStars from './RatingStars.jsx';
import ReviewApi from '../../api/ReviewApi.js';

const { TextArea } = Input;

const ReviewForm = ({ productId, target = null, onSuccess }) => {
  const [rating, setRating] = useState(5);
  const [comment, setComment] = useState('');
  const [loading, setLoading] = useState(false);

  const handleSubmit = async () => {
    if (!rating) {
      message.error('Vui lòng chọn số sao');
      return;
    }
    setLoading(true);
    try {
      const payload = target ? (target.type==='combo' ? { comboId: target.id } : { productId: target.id }) : { productId };
      await ReviewApi.createReview({ ...payload, rating, comment });
      message.success('Đã gửi đánh giá');
      setComment('');
      setRating(5);
      onSuccess?.();
    } catch (err) {
      message.error(err?.response?.data?.message || 'Lỗi gửi đánh giá');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Form layout="vertical" onFinish={handleSubmit}>
      <Form.Item label="Đánh giá">
        <RatingStars value={rating} onChange={setRating} allowEdit />
      </Form.Item>
      <Form.Item label="Nhận xét">
        <TextArea
          rows={4}
          value={comment}
          onChange={(e) => setComment(e.target.value)}
          maxLength={1000}
        />
      </Form.Item>
      <Button type="primary" htmlType="submit" loading={loading}>
        Gửi đánh giá
      </Button>
    </Form>
  );
};

export default ReviewForm; 