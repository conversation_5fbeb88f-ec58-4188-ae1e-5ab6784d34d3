.logo {
    cursor: pointer;
    color: var(--primary-color);
    font-size: 24px;

}

.banner {
    display: flex;
    justify-content: center;
    align-items: center;
}

.banner-image {
    width: "80%";
    height: "240px";
    object-fit: cover;
}

.best-seller {
    display: flex;
    justify-content: center;
    align-items: center;
}

.best-seller-text {
    display: flex;
    justify-content: center;
    text-align: center;
    padding: 4px 32px;
    font-size: var(--font-size-xl);
    border: 1px solid var(--primary-color);
    font-weight: 400;
    background-color: var(--primary-color);
    border-radius: 30px;
    color: white;
    max-width: 600px;
    width: 100%;
}

.menu-product {
    display: flex;
    justify-content: space-around;
}

.view-more {
    text-align: center;
}

.view-more-text {
    display: inline-block;
    border: 1px solid var(--primary-color);
    border-radius: 15px;
    padding: 4px 8px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    color: var(--primary-color);
    background-color: transparent;
    position: relative;
    overflow: hidden;
    transition: color 0.3s ease-in-out;
}

.view-more-text::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background-color: var(--primary-color);
    transition: left 0.3s ease-in-out;
    z-index: 0;
}

.view-more-text:hover::before {
    left: 0;
    color: white !important;
}

.view-more-text:hover {
    text-decoration: none;
    color: white !important;
}

.view-more-text span {

    position: relative;
    z-index: 1;
}

.homeContainer {
    background-color: #FFF;
    color: #4A4A4A;
}

.section {
    padding: 4rem 2rem;
    margin-bottom: 2rem;
}

.section:last-of-type {
    margin-bottom: 0;
}

.sectionTitle {
    font-size: 2rem;
    font-weight: 800;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

/* Hero Section */
.heroSection {
    background-color: #FFF8F0; /* A soft, warm color */
    padding: 6rem 2rem;
    text-align: center;
}

.heroContent {
    max-width: 800px;
    margin: 0 auto;
}

.heroTitle {
    font-size: 3.5rem;
    font-weight: 800;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.heroSubtitle {
    font-size: 1.25rem;
    margin-bottom: 2.5rem;
    color: #555;
}

/* Shop by Pet Section */
.petCard {
    height: 350px;
    border-radius: 20px;
    background-size: cover;
    background-position: center;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: flex-end;
    padding: 2rem;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    cursor: pointer;
}

.petCard:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
}

.petCard::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to top, rgba(0,0,0,0.6) 0%, rgba(0,0,0,0) 50%);
}

.petCardContent {
    position: relative;
    z-index: 2;
    color: white;
}

.petCardContent h3 {
    font-size: 2.5rem;
    font-weight: 700;
    color: white;
    margin-bottom: 1rem;
}

.dogCard {
    background-image: url('https://images.unsplash.com/photo-1543466835-00a7907e9de1?q=80&w=2874&auto=format&fit=crop');
}

.catCard {
     background-image: url('https://tse4.mm.bing.net/th/id/OIP.Pl36QdNiFRCY0n1cCfl5SAHaHo?rs=1&pid=ImgDetMain');
}

/* Why Choose Us Section */
.whyChooseUsSection {
    background-color: #F9F9F9;
}

.featureBox {
    text-align: center;
    padding: 2rem;
}

.featureIcon {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1.5rem;
}

.featureBox h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

/* Product Sections */
.newArrivalsSection {
    background-color: #fff;
}

.combosSection {
    background-color: #FFF8F0;
}

.dogProductsSection {
    background-color: #F9F9F9;
}

.catProductsSection {
    background-color: #FFF;
}

.spinnerContainer {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
}

.viewMoreContainer {
    text-align: center;
    margin-top: 3rem;
}

/* Responsive */
@media (max-width: 768px) {
    .section {
        padding: 3rem 1rem;
    }
    .heroTitle {
        font-size: 2.5rem;
    }
    .heroSubtitle {
        font-size: 1rem;
    }
    .sectionTitle {
        font-size: 2rem;
    }
    .petCard {
        height: 250px;
    }
    .petCardContent h3 {
        font-size: 2rem;
    }
}