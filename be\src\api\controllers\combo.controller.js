import { HttpStatusCode } from 'axios';
import { comboService } from '../services/index.js';

const listCombos = async (req, res) => {
  const data = await comboService.search(req);
  if (data.status === HttpStatusCode.Ok) {
    return res.status(data.status).send(data.data);
  }
  return res.status(data.status).send(data);
};

const createCombo = async (req, res) => {
  const data = await comboService.create(req);
  if (data.status === HttpStatusCode.Ok) {
    return res.status(data.status).send(data.data);
  }
  return res.status(data.status).send(data);
};

const getComboById = async (req, res) => {
  const data = await comboService.findById(req);
  if (data.status === HttpStatusCode.Ok) {
    return res.status(data.status).send(data.data);
  }
  return res.status(data.status).send(data);
};

const updateCombo = async (req, res) => {
  const data = await comboService.update(req);
  if (data.status === HttpStatusCode.Ok) {
    return res.status(data.status).send(data.data);
  }
  return res.status(data.status).send(data);
};

const deleteCombo = async (req, res) => {
  const data = await comboService.remove(req);
  return res.status(data.status).send(data);
};

export default { listCombos, createCombo, getComboById, updateCombo, deleteCombo }; 