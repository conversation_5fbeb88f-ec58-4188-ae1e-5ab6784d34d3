import { notificationRepository } from '../repositories/index.js';
import ResponseDataSuccess from '../utils/response/ResponseDataSuccess.js';
import InternalServerError from '../utils/response/InternalServerError.js';

const create = async (data, io) => {
  try {
    const noti = await notificationRepository.create(data);
    // if real-time needed
    if (io) {
      if (data.userId) io.to(String(data.userId)).emit('notification', noti);
      else io.emit('notification', noti); // broadcast
    }
    return new ResponseDataSuccess(noti);
  } catch (error) {
    return new InternalServerError(error);
  }
};

const list = async (userId, options) => {
  try {
    const res = await notificationRepository.listByUser(userId, options);
    return new ResponseDataSuccess(res);
  } catch (error) {
    return new InternalServerError(error);
  }
};

const markRead = async (userId, id) => {
  try {
    await notificationRepository.markRead(userId, id);
    return new ResponseDataSuccess('updated');
  } catch (error) {
    return new InternalServerError(error);
  }
};

export default { create, list, markRead }; 