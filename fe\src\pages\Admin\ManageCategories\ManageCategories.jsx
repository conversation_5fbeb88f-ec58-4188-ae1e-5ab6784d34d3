import React, { useEffect, useState } from "react";
import s from "../ManageCategories/ManageCategories.module.css";
import CategoryApi from "../../../api/CategoryApi";
import { toast } from "react-toastify"

const ManageCategories = () => {
  const [resetValue, setResetValue] = useState(1);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [subCategory, setSubCategory] = useState(null);
  const [categoriesList, setCategoriesList] = useState([]);
  const [searchKey, setSearchKey] = useState("");
  const [message, setMessage] = useState(""); // Thông báo
  const [messageType, setMessageType] = useState(""); // "success" hoặc "error"
  const [isEditModalOpen, setIsEditModalOpen] = useState(false); // Modal chỉnh sửa
  const [editCategory, setEditCategory] = useState(null); // Danh mục cần sửa
  const [newCategoryName, setNewCategoryName] = useState(""); // Tên danh mục mới
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false); // Modal thêm mới
  const [parentId, setParentId] = useState(null); // ID danh mục cha
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false); // Modal xóa
  const [categoryToDelete, setCategoryToDelete] = useState(null); // Danh mục cần xóa

  // **Chỉnh sửa danh mục**
  const handleEditCategory = (category) => {
    setEditCategory(category);
    setNewCategoryName(category.name);
    setIsEditModalOpen(true);
  };

  // **Thêm danh mục mới - mở modal**
  const handleAddCategory = (id) => {
    setParentId(id);
    setNewCategoryName(""); // Reset ô input
    setIsCreateModalOpen(true);
  };

  // **Lưu danh mục mới**
  const handleSaveNewCategory = async () => {
    if (!newCategoryName.trim()) {
      setMessage("Tên danh mục không được để trống!");
      setMessageType("error");
      setTimeout(() => {
        setMessage("");
        setMessageType("");
      }, 3000);
      return;
    }

    const payload = parentId
      ? { parentId, name: newCategoryName }
      : { name: newCategoryName };

    try {
      const response = await CategoryApi.createCategories(payload);
      setResetValue(resetValue + 1);
      setIsCreateModalOpen(false);
      setNewCategoryName("");
      setMessage("Thêm danh mục thành công!");
      setMessageType("success");
      setTimeout(() => {
        setMessage("");
        setMessageType("");
      }, 1000);
    } catch (error) {
      const errorMessage = error?.response?.data?.message || "Có lỗi xảy ra, vui lòng thử lại!";
      setMessage(errorMessage);
      setMessageType("error");
      setTimeout(() => {
        setMessage("");
        setMessageType("");
      }, 5000);
    }
  };

  // **Xóa danh mục - mở modal xác nhận**
  const handleDeleteCategory = (cate) => {
    setCategoryToDelete(cate);
    setIsDeleteModalOpen(true);
  };

  // **Xác nhận xóa danh mục**
  const confirmDelete = async () => {
    try {
      const warning = await CategoryApi.categoriesIsUsed(categoryToDelete._id);
      if (warning.data.hasProduct) {
        toast.error("Không thể xóa danh mục có sản phẩm!");
        setMessage("Không thể xóa danh mục có sản phẩm!");
        setMessageType("error");
      } else {
        await CategoryApi.deleteCategories(categoryToDelete._id);
        if (categoryToDelete._id === selectedCategory?._id) {
          setSelectedCategory(null);
          setSubCategory(null);
        }
        if (categoryToDelete._id === subCategory?._id) {
          setSubCategory(null);
        }
        setResetValue(resetValue + 1);
        setMessage("Xóa danh mục thành công!");
        setMessageType("success");
      }
    } catch (error) {
      setMessage("Có lỗi xảy ra khi xóa danh mục!");
      setMessageType("error");
    }
    setIsDeleteModalOpen(false);
    setTimeout(() => {
      setMessage("");
      setMessageType("");
    }, 3000);
  };

  // **Lưu chỉnh sửa danh mục**
  const handleSaveCategory = async () => {
    if (!newCategoryName.trim()) {
      setMessage("Tên danh mục không được để trống!");
      setMessageType("error");
      setTimeout(() => {
        setMessage("");
        setMessageType("");
      }, 3000);
      return;
    }

    const nName = { name: newCategoryName };

    try {
      await CategoryApi.updateCategories(editCategory._id, nName);
      setMessage("Cập nhật danh mục thành công!");
      setMessageType("success");
      setTimeout(() => {
        setMessage("");
        setMessageType("");
        setIsEditModalOpen(false);
      }, 1500);
      setResetValue(resetValue + 1);
    } catch (error) {
      setMessage("Tên của danh mục này đã tồn tại! Vui lòng nhập tên khác.");
      setMessageType("error");
      setTimeout(() => {
        setMessage("");
        setMessageType("");
      }, 3000);
    }
  };

  // **Lấy danh sách danh mục**
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const data = await CategoryApi.searchCategories(searchKey);
        setCategoriesList(data.data || []);

        // Xử lý selectedCategory
        if (selectedCategory) {
          const foundCategory = data.data.find(
            (cate) => cate.root?._id === selectedCategory._id
          );
          if (foundCategory) {
            setSelectedCategory(foundCategory.root);
          } else {
            setSelectedCategory(null); // Set về null nếu không tìm thấy
          }
        }

        // Xử lý subCategory
        if (subCategory) {
          const foundCategory = data.data.find((cate) =>
            cate.root?.children?.find((cr) => cr?._id === subCategory._id)
          );
          if (foundCategory) {
            const foundSubCategory = foundCategory.root?.children.find(
              (cr) => cr?._id === subCategory._id
            );
            if (foundSubCategory) {
              setSubCategory(foundSubCategory);
            } else {
              setSubCategory(null); // Set về null nếu không tìm thấy subcategory
            }
          } else {
            setSubCategory(null); // Set về null nếu không tìm thấy category chứa subcategory
          }
        }
      } catch (error) {
        console.error("Lỗi khi lấy danh mục:", error);
      }
    };
    fetchCategories();
  }, [resetValue, searchKey]);

  // **Chọn danh mục tầng 1**
  const handleSelectCategory = (category) => {
    setSelectedCategory(category);
    setSubCategory(null);
  };

  // **Chọn danh mục tầng 2**
  const handleSelectSubCategory = (sub) => {
    setSubCategory(sub);
  };

  // **Tìm kiếm danh mục**


  return (
    <div className={s["category-container"]}>
      <h1>Quản lý danh mục</h1>
      <div className={s["search-container"]}>
        <input
          type="text"
          className={s["search-input"]}
          placeholder="Tìm kiếm..."
          value={searchKey}
          onChange={(e) => setSearchKey(e.target.value)}
        />
        <button className={s["search-button"]}>
          🔍
        </button>
      </div>
      <div className={s["category-columns"]}>
        <div className={s["category-section"]}>
          <h2 style={{ height: "65px" }}>Danh mục chủ</h2>
          <ul className={s["category-form-ul"]}>
            {categoriesList?.map((category) => (
              <div
                className={s["category-item-wrapper"]}
                key={category.root?._id}
              >
                <li
                  className={`${s["category-form-li"]} ${selectedCategory?._id === category.root?._id
                    ? s["selected"]
                    : ""
                    }`}
                  onClick={() => handleSelectCategory(category.root)}
                >
                  <span>{category.root?.name}</span>
                </li>
                <div
                  className={s["icon-box"]}
                  onClick={() => handleEditCategory(category.root)}
                >
                  <img src="/images/admin/EditIcon.png" alt="icon1" />
                </div>
                <div
                  className={s["icon-box"]}
                  onClick={() => handleDeleteCategory(category.root)}
                >
                  <img src="/images/admin/deleteIconNew.png" alt="icon2" />
                </div>
              </div>
            ))}
            <li
              className={`${s["category-form-li"]} ${s["add-btn"]}`}
              onClick={() => handleAddCategory(null)}
            >
              + Thêm mới danh mục
            </li>
          </ul>
        </div>
        <div className={s["category-section"]}>
          {selectedCategory ? (
            <h2 style={{ height: "65px" }}>
              <span>{selectedCategory.name}</span>
            </h2>
          ) : (
            <h2 style={{ height: "65px" }}>
              <span>Hãy chọn danh mục chủ</span>
            </h2>
          )}
          {selectedCategory ? (
            <ul className={s["category-form-ul"]}>
              {selectedCategory.children.map((sub) => (
                <div className={s["category-item-wrapper"]} key={sub._id}>
                  <li
                    className={`${s["category-form-li"]} ${subCategory?._id === sub._id ? s["selected"] : ""
                      }`}
                    onClick={() => handleSelectSubCategory(sub)}
                  >
                    <span>{sub.name}</span>
                  </li>
                  <div
                    className={s["icon-box"]}
                    onClick={() => handleEditCategory(sub)}
                  >
                    <img src="/images/admin/EditIcon.png" alt="icon1" />
                  </div>
                  <div
                    className={s["icon-box"]}
                    onClick={() => handleDeleteCategory(sub)}
                  >
                    <img src="/images/admin/deleteIconNew.png" alt="icon2" />
                  </div>
                </div>
              ))}
              <li
                className={`${s["category-form-li"]} ${s["add-btn"]}`}
                onClick={() => handleAddCategory(selectedCategory?._id)}
              >
                + Thêm vào {selectedCategory?.name}
              </li>
            </ul>
          ) : (
            <p></p>
          )}
        </div>
        <div className={s["category-section"]}>
          {subCategory ? (
            <h2 style={{ height: "65px" }}>
              <span>{subCategory.name}</span>
            </h2>
          ) : (
            <h2 style={{ height: "65px" }}>
              <span>Hãy chọn danh mục 2</span>
            </h2>
          )}
          {subCategory ? (
            <ul className={s["category-form-ul"]}>
              {subCategory.children.map((item) => (
                <div className={s["category-item-wrapper"]} key={item._id}>
                  <li className={s["category-form-li"]}>
                    <span>{item.name}</span>
                  </li>
                  <div
                    className={s["icon-box"]}
                    onClick={() => handleEditCategory(item)}
                  >
                    <img src="/images/admin/EditIcon.png" alt="icon1" />
                  </div>
                  <div
                    className={s["icon-box"]}
                    onClick={() => handleDeleteCategory(item)}
                  >
                    <img src="/images/admin/deleteIconNew.png" alt="icon2" />
                  </div>
                </div>
              ))}
              <li
                className={`${s["category-form-li"]} ${s["add-btn"]}`}
                onClick={() => handleAddCategory(subCategory?._id)}
              >
                + Thêm vào {subCategory?.name}
              </li>
            </ul>
          ) : (
            <p></p>
          )}
        </div>
      </div>

      {/* **Modal chỉnh sửa danh mục** */}
      {isEditModalOpen && (
        <div className={s["modal"]}>
          <div className={s["modal-content"]}>
            <h2>Chỉnh sửa danh mục</h2>
            <input
              style={{ width: "90%" }}
              type="text"
              value={newCategoryName}
              onChange={(e) => setNewCategoryName(e.target.value)}
              placeholder="Nhập tên mới..."
              maxLength={35}
            />
            {message && (
              <p
                className={
                  s[
                  messageType === "error"
                    ? "error-message"
                    : "success-message"
                  ]
                }
              >
                {message}
              </p>
            )}
            <div className={s["modal-buttons"]}>
              <button onClick={handleSaveCategory}>Lưu</button>
              <button onClick={() => setIsEditModalOpen(false)}>Hủy</button>
            </div>
          </div>
        </div>
      )}

      {/* **Modal thêm danh mục mới** */}
      {isCreateModalOpen && (
        <div className={s["modal"]}>
          <div className={s["modal-content"]}>
            <h2>Thêm danh mục mới</h2>
            <input
              style={{ width: "90%" }}
              type="text"
              value={newCategoryName}
              onChange={(e) => setNewCategoryName(e.target.value)}
              placeholder="Nhập tên danh mục..."
            />
            {message && (
              <p
                className={
                  s[
                  messageType === "error"
                    ? "error-message"
                    : "success-message"
                  ]
                }
              >
                {message}
              </p>
            )}
            <div className={s["modal-buttons"]}>
              <button onClick={handleSaveNewCategory}>Lưu</button>
              <button onClick={() => setIsCreateModalOpen(false)}>Hủy</button>
            </div>
          </div>
        </div>
      )}

      {/* **Modal xác nhận xóa** */}
      {isDeleteModalOpen && (
        <div className={s["modal"]}>
          <div className={s["modal-content"]}>
            <h2>Xác nhận xóa</h2>
            <p>Bạn có chắc chắn muốn xóa danh mục này?</p>
            {message && (
              <p
                className={
                  s[
                  messageType === "error"
                    ? "error-message"
                    : "success-message"
                  ]
                }
              >
                {message}
              </p>
            )}
            <div className={s["modal-buttons"]}>
              <button onClick={confirmDelete}>Xóa</button>
              <button onClick={() => setIsDeleteModalOpen(false)}>Hủy</button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ManageCategories;