import { cloudinary } from '../config/cloudinaryConfig.js';

export async function uploadImage(
  file,
  public_id,
  options = { overwrite: true, invalidate: true }
) {
  try {
    const folder = `PetHaven`;
    const result = await cloudinary.uploader.upload(file, {
      folder,
      resource_type: 'auto',
      public_id,
      ...options,
    });
    return formatImage(result);
  } catch (error) {
    throw new Error(`Upload failed: ${error.message}`);
  }
}

export async function deleteImage(publicId) {
  try {
    return await cloudinary.uploader.destroy(publicId);
  } catch (error) {
    throw new Error(`Delete failed: ${error.message}`);
  }
}

function formatImage(result) {
  if (!result || !result.secure_url) {
    throw new Error('Invalid upload result from Cloudinary');
  }

  return {
    public_id: result.public_id,
    url: result.secure_url,
  };
}
