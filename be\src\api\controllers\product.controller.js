import { HttpStatusCode } from 'axios';
import { productService } from '../services/index.js';
import { ROLES } from '../constants/role.js';

const findAll = async (req, res) => {
  const data = await productService.search(req);
  return res.status(data.status).send(data.data);
};

const create = async (req, res) => {
  try {
    const result = await productService.create(req);

    if (result.status === HttpStatusCode.Ok) {
      return res.status(result.status).send(result.data);
    }

    return res.status(result.status).send({ message: result.message });
  } catch (error) {
    console.error('[product.controller] Unexpected error during create:', error);
    return res.status(500).send({ message: 'An unexpected error occurred.' });
  }
};

const findDetailById = async (req, res) => {
  const { id } = req.params;
  let showHide;
  // If the user is not an admin or employee, hide the products that are marked as hidden
  if (
    !req.user ||
    (!req.user.role.includes(ROLES.ADMIN) &&
      !req.user.role.includes(ROLES.EMPLOYEE))
  ) {
    showHide = false;
  }

  const data = await productService.findDetailById(id, showHide);
  if (data.status === HttpStatusCode.Ok) {
    return res.status(data.status).send(data.data);
  }
  return res.status(data.status).send(data.error);
};

const findDetailInOrderById = async (req, res) => {
  const { id } = req.params;
  const data = await productService.findDetailInOrderById(id);
  if (data.status === HttpStatusCode.Ok) {
    return res.status(data.status).send(data.data);
  }
  return res.status(data.status).send(data.error);
};

const update = async (req, res) => {
  try {
    const { id } = req.params;
    const result = await productService.update(id, req);

    if (result.status === HttpStatusCode.Ok) {
      return res.status(result.status).send(result.data);
    }

    return res.status(result.status).send({ message: result.message });
  } catch (error) {
    console.error('[product.controller] Unexpected error during update:', error);
    return res.status(500).send({ message: 'An unexpected error occurred.' });
  }
};

const deleteProduct = async (req, res) => {
  const { id } = req.params;
  const data = await productService.deleteProduct(id);
  if (data.status === HttpStatusCode.Ok) {
    return res.status(data.status).send(data.data);
  }
  return res.status(data.status).send(data.error);
};

export default {
  findAll,
  create,
  findDetailById,
  findDetailInOrderById,
  update,
  delete: deleteProduct,
};
