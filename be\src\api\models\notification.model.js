import mongoose from 'mongoose';
import { MODELS, COLLECTIONS, NOTIFICATION_TYPE } from '../utils/constants.js';

const notificationSchema = new mongoose.Schema(
  {
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: MODELS.USER,
      default: null, // null = broadcast
    },
    title: {
      type: String,
      required: true,
    },
    message: {
      type: String,
      required: true,
    },
    type: {
      type: String,
      enum: Object.values(NOTIFICATION_TYPE),
      required: true,
    },
    refId: {
      type: mongoose.Schema.Types.ObjectId,
      default: null,
    },
    isRead: {
      type: Boolean,
      default: false,
    },
  },
  {
    collection: COLLECTIONS.NOTIFICATION,
    timestamps: true,
    toJSON: {
      transform: function (doc, ret) {
        delete ret.__v;
        return ret;
      },
    },
  }
);

notificationSchema.index({ userId: 1, createdAt: -1 });

const Notification = mongoose.model(
  MODELS.NOTIFICATION,
  notificationSchema,
  COLLECTIONS.NOTIFICATION
);

export default Notification; 