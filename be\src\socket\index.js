import { Server } from 'socket.io';

let io;

export const initSocket = (server) => {
  io = new Server(server, {
    cors: {
      origin: ['http://localhost:5173'],
      credentials: true,
    },
  });

  io.on('connection', (socket) => {
    const { userId } = socket.handshake.query;
    if (userId) {
      socket.join(userId);
    }
    socket.on('disconnect', () => {});
  });

  return io;
};

export const getIO = () => io; 