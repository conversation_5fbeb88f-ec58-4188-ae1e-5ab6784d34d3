import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { nodePolyfills } from 'vite-plugin-node-polyfills';

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    react({
      // Thêm cấu hình để tránh warning compatibility
      jsxRuntime: 'automatic',
    }), 
    nodePolyfills(),
  ],
  define: {
    // Thêm global để tránh warning
    global: 'globalThis',
  },
  resolve: {
    alias: {
      // Đ<PERSON>m bảo React được resolve đúng
      'react': 'react',
      'react-dom': 'react-dom',
    },
  },
})
