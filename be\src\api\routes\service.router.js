import express from 'express';
import validate from '../middlewares/validates.js';
import { serviceController } from '../controllers/index.js';
import { verifyToken } from '../middlewares/authenticateJWT.js';
import authorizeRoles from '../middlewares/authorizeRoles.js';

const router = express.Router();

router.get('/', serviceController.listServices);
router.get('/:id', serviceController.getServiceById);

// Admin
router.post('/', verifyToken, authorizeRoles('ADMIN'), serviceController.createService);
router.put('/:id', verifyToken, authorizeRoles('ADMIN'), serviceController.updateService);
router.delete('/:id', verifyToken, authorizeRoles('ADMIN'), serviceController.deleteService);

export default router; 