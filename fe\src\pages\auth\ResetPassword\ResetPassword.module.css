.container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f0f2f5;
}

.card {
  width: 100%;
  max-width: 400px;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  background-color: #ffffff;
}

.title {
  text-align: center;
  margin-bottom: 16px;
  color: #1890ff;
}

.subText {
  display: block;
  text-align: center;
  margin-bottom: 24px;
  color: #666;
}

.formItem {
  margin-bottom: 16px;
}

.button {
  width: 100%;
  background-color: #1890ff;
  border-color: #1890ff;
}

.button:hover {
  background-color: #40a9ff;
  border-color: #40a9ff;
}