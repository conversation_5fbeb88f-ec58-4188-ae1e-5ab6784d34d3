.title {
  text-align: left;
  margin-bottom: 20px;
}

.manage-users-container {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
}

.stat-card {
  background: white;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
  width: 200px;
  text-align: left;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: transform 0.3s ease-in-out;
}

.stat-card-chart {
  background: white;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
  text-align: left;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: transform 0.3s ease-in-out;
  width: 55%;
}

.stat-card:hover {
  transform: scale(1.05);
}

.stat-title {
  font-size: 14px;
  color: #666;
  margin-top: 8px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  margin: 4px 0;
}



.search-input {
  width: 100% !important;
  max-width: 600px;
  margin: 20px 0;
}

.target-container {
  background: #fff;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
}

.deactivate {
  color: red;
}

.activate {
  color: green;
}

.stat-change {
  font-size: 14px;
  padding: 4px 8px;
  border-radius: 4px;
  display: inline-block;
}

.positive {
  background-color: #e6f4ea;
  color: #15803d;
}

.negative {
  background-color: #fce8e6;
  color: #b91c1c;
}