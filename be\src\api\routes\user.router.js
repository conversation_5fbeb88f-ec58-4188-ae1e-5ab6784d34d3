import express from "express";
import { userController } from "../controllers/index.js";
import { verifyToken } from "../middlewares/authenticateJWT.js";
import authorizeRoles from "../middlewares/authorizeRoles.js";
import { ROLES } from "../constants/role.js";
import validate from "../middlewares/validates.js";
import authValidation from "../validations/auth.validation.js";
import { uploadAvatar } from "../middlewares/upload.middleware.js";


const userRouter = express.Router();

userRouter.get("/", verifyToken, authorizeRoles(ROLES.ADMIN), userController.findAll);
userRouter.get("/dashboard-new-user", verifyToken, authorizeRoles(ROLES.ADMIN), userController.getDashboardNewUser);

userRouter.get("/:accountId", verifyToken, validate(authValidation.getProfile), userController.getByAccountId);

userRouter.patch(
  "/:accountId/avatar",
  verifyToken,
  uploadAvatar.single("avatar"),
  userController.updateAvatar
);

userRouter.patch("/:accountId", verifyToken, validate(authValidation.updateProfile), userController.update);




export default userRouter;
