.category-container {
  padding: 30px;
  max-width: 1200px;
  margin: 0 auto;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  text-align: center;
}

h1 {
  text-align: center;
  color: #1a1a1a;
  margin-bottom: 30px;
  font-size: 2rem;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.category-columns {
  display: flex;
  gap: 25px;
  justify-content: center;
}

.category-section {
  flex: 1;
  padding: 20px;
  background: #f8fafc;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  min-width: 280px;
  max-width: 350px;
  transition: transform 0.2s ease;
}

.category-section:hover {
  transform: translateY(-5px);
}

.category-section h2 {
  color: #2d3748;
  text-align: center;
  font-size: 1.5rem;
  font-weight: 500;
  margin-bottom: 15px;
  padding: 10px 0;
  height: 65px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow-wrap: break-word;
  word-break: break-word;
}

.category-section h2 span {
  display: block;
  max-width: 100%;
  overflow-wrap: break-word;
  word-break: break-word;
}

.category-form-ul {
  list-style: none;
  padding: 0;
  margin-top: 10px;
}

.category-item-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
  width: 100%;
}

.category-form-li {
  flex: 1;
  padding: 12px;
  margin: 5px 0;
  background: #ffffff;
  border-radius: 8px;
  cursor: pointer;
  transition: background 0.3s ease, transform 0.2s ease;
  text-align: center;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
  overflow-wrap: break-word;
  word-break: break-word;
  max-width: calc(100% - 90px);
  min-height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.category-form-li span {
  display: block;
  width: 100%;
}

.category-form-li:hover {
  background: #e2e8f0;
  transform: translateY(-2px);
}

.add-btn {
  background: #38a169;
  color: white;
  font-weight: 600;
  padding: 12px;
  margin: 5px 0;
  border-radius: 8px;
  cursor: pointer;
  transition: background 0.3s ease, transform 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow-wrap: break-word;
  word-break: break-word;
  max-width: 100%; /* Đảm bảo không tràn ra ngoài */
}

.add-btn:hover {
  background: #2f855a;
  transform: translateY(-2px);
}

.icon-box {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #ffffff;
  border-radius: 6px;
  margin-left: 10px;
  cursor: pointer;
  transition: background 0.3s ease;
  flex-shrink: 0;
}

.icon-box:hover {
  background-color: #edf2f7;
}

.icon-box img {
  width: 24px;
  height: 24px;
  object-fit: contain;
}

.selected {
  background: #3182ce !important;
  color: white !important;
  font-weight: 600;
  box-shadow: 0 2px 10px rgba(49, 130, 206, 0.3);
}

.search-container {
  display: flex;
  align-items: center;
  background: #ffffff;
  border-radius: 50px;
  padding: 8px 15px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  margin: 20px auto;
  max-width: 500px;
}

.search-input {
  flex: 1;
  border: none;
  background: transparent;
  outline: none;
  padding: 10px;
  font-size: 1rem;
  color: #4a5568;
}

.search-button {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1.2rem;
  color: #718096;
  transition: color 0.3s ease;
}

.search-button:hover {
  color: #2d3748;
}

.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: #ffffff;
  padding: 25px;
  border-radius: 12px;
  width: 350px;
  text-align: center;
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.2);
  animation: slideIn 0.3s ease;
}

.modal-content input {
  width: 100%;
  padding: 10px;
  margin: 15px 0;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 1rem;
  outline: none;
  transition: border-color 0.3s ease;
}

.modal-content input:focus {
  border-color: #3182ce;
}

.modal-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}

.modal-buttons button {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: background 0.3s ease, transform 0.2s ease;
}

.modal-buttons button:first-child {
  background: #3182ce;
  color: white;
}

.modal-buttons button:first-child:hover {
  background: #2b6cb0;
  transform: translateY(-2px);
}

.modal-buttons button:last-child {
  background: #e2e8f0;
  color: #4a5568;
}

.modal-buttons button:last-child:hover {
  background: #cbd5e0;
  transform: translateY(-2px);
}

.success-message {
  background-color: #48bb78;
  color: white;
  padding: 12px;
  border-radius: 6px;
  margin-top: 10px;
  font-weight: 500;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.error-message {
  background-color: #f56565;
  color: white;
  padding: 12px;
  border-radius: 6px;
  margin-top: 10px;
  font-weight: 500;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}