import Notification from '../models/notification.model.js';

const create = async (data) => await Notification.create(data);

const listByUser = async (userId, options = {}) => {
  const { page = 1, pageSize = 10 } = options;
  const skip = (page - 1) * pageSize;
  const filter = { $or: [{ userId }, { userId: null }] };
  const [items, total] = await Promise.all([
    Notification.find(filter)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(Number(pageSize)),
    Notification.countDocuments(filter),
  ]);
  return { items, total, page: Number(page), pageSize: Number(pageSize) };
};

const markRead = async (userId, id) => {
  return await Notification.updateOne({ _id: id, userId }, { isRead: true });
};

export default {
  create,
  listByUser,
  markRead,
}; 