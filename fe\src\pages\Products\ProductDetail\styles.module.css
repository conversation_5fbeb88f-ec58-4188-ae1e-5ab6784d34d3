.container {
    display: flex;
    gap: 30px;
    max-width: 1200px;
    width: 100%;
    margin: auto;
    padding: 20px;
}

.containerFather {
    margin: 20px 0;

}

.left {
    position: sticky;
    top: 100px;
    width: 50%;
}

.mainImage {
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 10px;
    overflow: hidden;
}

.mainImage img {
    width: 100%;
    max-height: 400px;
    object-fit: cover;
}

/* <PERSON>h sách ảnh nhỏ */
.thumbnailList {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 10px;
}

.thumbnail {
    max-width: 80px;
    width: 100%;
    max-height: 80px;
    height: 100%;
    object-fit: cover;
    cursor: pointer;
    border-radius: 5px;
    transition: 0.3s;
}

.thumbnail:hover, .thumbnail.active {
    border: 2px solid red;
}

/* <PERSON><PERSON>n phải - Thông tin sản phẩm */
.right {
    width: 50%;
    padding: 8px;
}

.title {
    font-size: 24px;
    font-weight: 600;
    text-align: left;
}

.heartIcon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border: 1px solid #d9d9d9;
    border-radius: 50%;
    background: white;
    transition: all 0.3s ease;
    cursor: pointer;
    flex-shrink: 0;
}

.heartIcon:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transform: translateY(-1px);
}

.price {
    margin: 0;
    font-size: 24px;
    color: red;
    font-weight: bold;
}

.oldPrice {
    text-decoration: line-through;
    color: gray;
    margin-right: 10px;
}

/* Options (Hương vị, Kích thước) */
.options {
    margin: 15px 0;
}

.optionBtn {
    margin-right: 10px;
    padding: 8px 12px;
    border: 1px solid var(--primary-color);
    cursor: pointer;
    /* background: white; */
    transition: 0.3s;
    font-size: var(--font-size-xs);
    background: var(--white-color);
    color: var(--primary-color);

}

.optionBtn:hover {
    background: var(--white-color);
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
}

.optionBtn.active {
    background: var(--primary-color);
    border: 1px solid var(--white-color);
    color: var(--white-color);
    position: relative;
}

.optionBtn.active::after {
    content: "✓";
    position: absolute;
    top: -5px;
    right: 0px;
    color: var(--white-color);
    font-size: 14px;
    font-weight: bold;
}


.cartAction {
    display: flex;
    gap: 10px;
    margin-top: 20px;
    align-items: center;
}



/* Chọn số lượng */
.quantity {
    flex: 1;
    width: 30%;
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 20px 0;
    justify-content: space-between;
    padding: 0 10px;
}

.quantity button {
    max-width: 40px;
    width: 100%;

    max-height: 40px;
    height: 100%;
    border: none;
    cursor: pointer;
    font-size: var(--font-size-lg);
    color: var(--primary-color);
    display: flex;
    justify-content: center;
    align-items: center;
    background: var(--primary-color);
    color: white;
}

.quantity button:hover {
    background: var(--primary-color);
    color: var(--white-color);
}

.quantity span {
    font-size: 18px;
    font-weight: bold;
}

/* Nút thêm vào giỏ */
.addToCart {
    position: relative;
    width: 70%;
    height: 40px;
    font-size: 18px;
    background: var(--primary-color);
    color: white;
    border: none;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.6s ease-in-out;
}

/* @media (max-width: 600px) {
    .addToCart span {
        display: none;
    }
} */

.addToCart::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.3);
    transition: left 0.6s ease-out;
}

.addToCart:hover::before {
    left: 100%;
}

.addToCart:hover {
    background-color: var(--white-color) !important;
    color: var(--primary-color) !important;
    border: 1px solid var(--primary-color);
}


.statusQuantity {
    display: flex;
}

.label {
    font-weight: 500;
    color: var(--text-color);
    margin-top: 8px;
    margin-bottom: 8px;

}

.value {
    font-weight: 600;
    color: var(--primary-color);
    margin-top: 8px;
    margin-bottom: 8px;
}

.titleRelatedProducts {
    justify-content: center;
    align-items: center;
    display: flex;
    font-size: var(--font-size-xxl);
}

.productList {
    display: flex;
    justify-content: center;
}


.quantityInput {
    max-width: 32px;
    width: 100%;
    min-width: 16px;
    text-align: center;
    border: 1px solid #ccc;
    border-radius: 4px;
    padding: 4px;
    height: 32px;
}

:global(.swiper-button-prev:after, .swiper-button-next:after) {
    color: var(--primary-color) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        flex-direction: column;
        gap: 20px;
        padding: 15px;
    }
    
    .left, .right {
        width: 100%;
        position: static;
    }
    
    .title {
        font-size: 20px;
    }
    
    .heartIcon {
        width: 36px;
        height: 36px;
    }
    
    .price {
        font-size: 20px;
    }
    
    .cartAction {
        flex-direction: column;
        gap: 15px;
    }
    
    .addToCart {
        width: 100%;
    }
}

@media (max-width: 480px) {
    .containerFather {
        margin: 10px 0;
    }
    
    .container {
        padding: 10px;
    }
    
    .title {
        font-size: 18px;
    }
    
    .heartIcon {
        width: 32px;
        height: 32px;
    }
    
    .price {
        font-size: 18px;
    }
}