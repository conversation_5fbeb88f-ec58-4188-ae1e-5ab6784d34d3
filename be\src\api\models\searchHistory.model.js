import mongoose from 'mongoose';
import { MODELS, COLLECTIONS } from '../utils/constants.js';

const searchHistorySchema = new mongoose.Schema(
  {
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: MODELS.USER,
      required: true,
    },
    keyword: {
      type: String,
      required: true,
      trim: true,
    },
  },
  {
    collection: COLLECTIONS.SEARCH_HISTORY,
    timestamps: true,
    toJSON: {
      transform: function (doc, ret) {
        delete ret.__v;
        return ret;
      },
    },
    toObject: {
      transform: function (doc, ret) {
        delete ret.__v;
        return ret;
      },
    },
  }
);

// Ensure we can query by userId quickly
searchHistorySchema.index({ userId: 1, createdAt: -1 });

// Upsert uniqueness for same keyword per user (optional, we allow duplicates but we may update timestamp)
searchHistorySchema.index(
  { userId: 1, keyword: 1 },
  {
    unique: false,
  }
);

const SearchHistory = mongoose.model(
  MODELS.SEARCH_HISTORY,
  searchHistorySchema,
  COLLECTIONS.SEARCH_HISTORY
);

export default SearchHistory; 