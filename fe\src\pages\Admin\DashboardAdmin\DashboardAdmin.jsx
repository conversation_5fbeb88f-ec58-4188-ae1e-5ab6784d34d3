import React, { useState, useEffect } from "react";
import s from "./DashboardAdmin.module.css";
import { <PERSON><PERSON>, Divide<PERSON>, <PERSON>lex, <PERSON> } from "antd";
import {
  DropboxOutlined,
  ShoppingCartOutlined,
  UsergroupAddOutlined,
} from "@ant-design/icons";
import {
  ResponsiveContainer,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
} from "recharts";
import { useNavigate } from "react-router-dom";
import DashBoardApi from "../../../api/DashBoardApi";
import { useSelector } from "react-redux";

const DashboardAdmin = () => {
  const navigate = useNavigate();
  const [date, setDate] = useState("YEAR"); // YEAR, MONTH, WEEK
  const [dataOrders, setDataOrders] = useState([]);
  const [dataMoney, setDataMoney] = useState([]);
  const [dataOverView, setDataOverView] = useState(null);
  const { userInfo } = useSelector((state) => state.user);

  // Kiểm tra vai trò admin
  const isAdmin = userInfo?.role?.includes("ADMIN") || userInfo?.role === "ADMIN";

  // Fetch dữ liệu biểu đồ (dữ liệu năm) khi component mount
  useEffect(() => {
    const fetchChartData = async () => {
      try {
        const data1 = await DashBoardApi.getChart();
        setDataOrders(data1.data.ordersByMonth);
        setDataMoney(data1.data.moneyByMonth);
      } catch (error) {
        console.error("Lỗi khi fetch dữ liệu biểu đồ:", error);
      }
    };
    fetchChartData();
  }, []);

  // Fetch dữ liệu thống kê khi date thay đổi
  useEffect(() => {
    const fetchOverviewData = async () => {
      try {
        const data2 = await DashBoardApi.getInforByDate(date);
        setDataOverView(data2.data);
      } catch (error) {
        console.error("Lỗi khi fetch dữ liệu thống kê:", error);
      }
    };
    fetchOverviewData();
  }, [date]);

  return (
    <div className={s["dashboard-container"]}>
      <h1 className={s["title-dashboard"]}>Dashboard:</h1>
      <div className={s["set-button-date"]}>
        <Radio.Group
          size="large"
          value={date}
          onChange={(e) => setDate(e.target.value)}
        >
          <Radio.Button value="YEAR">Năm</Radio.Button>
          <Radio.Button value="MONTH">Tháng</Radio.Button>
          <Radio.Button value="WEEK">Tuần</Radio.Button>
        </Radio.Group>
      </div>

      <div className={s["dashboard-stats-container"]}>
        <div
          className={s["dashboard-stat-card"]}
          onClick={() => navigate("/admin/orders")}
          style={{ cursor: "pointer" }}
        >
          <div className={s["dashboard-stat-info"]}>
            <div style={{ display: "flex" }}>
              <ShoppingCartOutlined className={s["dashboard-stat-icon"]} />
              <h2 style={{ marginLeft: "20px" }}>Income</h2>
            </div>
            <p className={s["dashboard-stat-value"]}>
              {dataOverView?.incomes?.Money?.toLocaleString() || "0"} VND
            </p>
            <p
              className={`${s["dashboard-stat-change"]} ${dataOverView?.incomes?.Change >= 0
                  ? s["dashboard-positive"]
                  : s["dashboard-negative"]
                }`}
            >
              Tình trạng:{" "}
              {dataOverView?.incomes?.Change >= 0 ? "⬆" : "⬇"}{" "}
              {Math.abs(dataOverView?.incomes?.Change) || "0"}%
            </p>
          </div>
        </div>
        <div
          className={s["dashboard-stat-card"]}
          onClick={() => navigate("/admin/orders")}
          style={{ cursor: "pointer" }}
        >
          <div className={s["dashboard-stat-info"]}>
            <div style={{ display: "flex" }}>
              <DropboxOutlined className={s["dashboard-stat-icon"]} />
              <h2 style={{ marginLeft: "20px" }}>Orders</h2>
            </div>
            <p className={s["dashboard-stat-value"]}>
              {dataOverView?.Orders?.Orders || "0"} Đơn hàng
            </p>
            <p
              className={`${s["dashboard-stat-change"]} ${dataOverView?.Orders?.Change >= 0
                  ? s["dashboard-positive"]
                  : s["dashboard-negative"]
                }`}
            >
              Tình trạng:{" "}
              {dataOverView?.Orders?.Change >= 0 ? "⬆" : "⬇"}{" "}
              {Math.abs(dataOverView?.Orders?.Change) || "0"}%
            </p>
          </div>
        </div>
        <div
          className={`${s["dashboard-stat-card"]} ${!isAdmin ? s["disabled"] : ""}`}
          onClick={() => isAdmin && navigate("/admin/users")}
          style={{ cursor: isAdmin ? "pointer" : "not-allowed" }}
        >
          <div className={s["dashboard-stat-info"]}>
            <div style={{ display: "flex" }}>
              <UsergroupAddOutlined className={s["dashboard-stat-icon"]} />
              <h2 style={{ marginLeft: "20px" }}>Users</h2>
            </div>
            <p className={s["dashboard-stat-value"]}>
              {dataOverView?.Users?.Users || "0"} User mới
            </p>
            <p
              className={`${s["dashboard-stat-change"]} ${dataOverView?.Users?.Change >= 0
                  ? s["dashboard-positive"]
                  : s["dashboard-negative"]
                }`}
            >
              Tình trạng:{" "}
              {dataOverView?.Users?.Change >= 0 ? "⬆" : "⬇"}{" "}
              {Math.abs(dataOverView?.Users?.Change) || "0"}%
            </p>
          </div>
        </div>
      </div>

      <div className={s["chart-container"]}>
        <div style={{ width: "49%" }}>
          <h3 style={{ marginLeft: "50px" }}>Bảng order năm 2025</h3>
          <div className="bar-chart">
            <ResponsiveContainer className="chart" height={300}>
              <LineChart
                width={600}
                height={300}
                data={dataOrders}
                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
              >
                <XAxis dataKey="name" />
                <YAxis />
                <CartesianGrid strokeDasharray="3 3" />
                <Tooltip />
                <Legend />
                <Line
                  type="monotone"
                  dataKey="order"
                  stroke="#8884d8"
                  activeDot={{ r: 8 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </div>

        <div style={{ width: "49%" }}>
          <h3 style={{ marginLeft: "50px" }}>Bảng thu nhập năm 2025</h3>
          <div className="bar-chart">
            <ResponsiveContainer className="chart" height={300}>
              <LineChart
                width={600}
                height={300}
                data={dataMoney}
                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
              >
                <XAxis dataKey="name" />
                <YAxis />
                <CartesianGrid strokeDasharray="3 3" />
                <Tooltip />
                <Legend />
                <Line
                  type="monotone"
                  dataKey="money"
                  stroke="#8884d8"
                  activeDot={{ r: 8 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardAdmin;