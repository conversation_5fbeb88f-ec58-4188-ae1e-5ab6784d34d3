import { useEffect, useState } from "react";
import { Card, Button, Descriptions, Tag, Space, message, Modal } from "antd";
import {
  PrinterOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  SyncOutlined,
} from "@ant-design/icons";
import { useParams } from "react-router-dom";
import OrderApi from "../../../api/OrderApi";
import s from "./DetailOrder.module.css";

const statusColors = {
  PENDING: "orange",
  APPROVED: "blue",
  DELIVERING: "cyan",
  DELIVERED: "green",
  CANCELED: "red",
};

const DetailOrder = () => {
  const { orderId } = useParams();
  const [order, setOrder] = useState(null);
  const [loading, setLoading] = useState(true);
  const [isConfirmModalVisible, setIsConfirmModalVisible] = useState(false);
  const [newStatus, setNewStatus] = useState(null); // Trạng thái mới sẽ được xác nhận

  useEffect(() => {
    const fetchOrderDetail = async () => {
      try {
        const response = await OrderApi.getOrderDetail(orderId);
        setOrder(response?.data || {});
        setLoading(false);
      } catch (error) {
        console.error("Lỗi khi lấy chi tiết đơn hàng:", error);
        message.error("Không thể lấy thông tin đơn hàng.");
        setLoading(false);
      }
    };

    fetchOrderDetail();
  }, [orderId]);

  const showConfirmModal = (status) => {
    setNewStatus(status);
    setIsConfirmModalVisible(true);
  };

  const handleConfirmUpdate = async () => {
    try {
      await OrderApi.updateStatusOrder(orderId, newStatus);
      setOrder((prev) => ({ ...prev, status: newStatus }));
      message.success(`Cập nhật trạng thái đơn hàng thành: ${newStatus}`);
      setIsConfirmModalVisible(false);
    } catch (error) {
      console.error("Lỗi cập nhật trạng thái:", error);
      message.error("Cập nhật trạng thái thất bại.");
    }
  };

  const handleCancelUpdate = () => {
    setIsConfirmModalVisible(false);
    setNewStatus(null);
  };

  const printOrder = () => {
    window.print();
  };

  if (loading) return <p>Đang tải...</p>;
  if (!order) return <p>Không tìm thấy đơn hàng.</p>;

  return (
    <div className={s["detail-order-container"]}>
      <Card title={`Chi tiết đơn hàng: ${order._id}`} bordered>
        <Descriptions column={1} bordered>
          <Descriptions.Item label="Mã đơn hàng">{order._id}</Descriptions.Item>
          <Descriptions.Item label="Khách hàng">{order.deliveryAddress.fullName}</Descriptions.Item>
          <Descriptions.Item label="Trạng thái">
            <Tag color={statusColors[order.status] || "default"}>{order.status}</Tag>
          </Descriptions.Item>
          <Descriptions.Item label="Sản phẩm">
            {order.items
              .map(
                (item) =>
                  `${item.briefInfo.name} (${item.briefInfo.variant}) x${item.briefInfo.quantity}`
              )
              .join(", ")}
          </Descriptions.Item>
          <Descriptions.Item label="Tổng tiền">{order.totalPrice.toLocaleString()} đ</Descriptions.Item>
          {order.voucher?.discount > 0 && (
            <>
              <Descriptions.Item label="Mã giảm giá">{order.voucher.code}</Descriptions.Item>
              <Descriptions.Item label="Giảm giá">-{order.voucher.discount.toLocaleString()} đ</Descriptions.Item>
            </>
          )}
          <Descriptions.Item label="Ngày đặt hàng">{new Date(order.createdAt).toLocaleString()}</Descriptions.Item>
          <Descriptions.Item label="Phương thức thanh toán">{order.payment.method}</Descriptions.Item>
          <Descriptions.Item label="Đã thanh toán">{order.payment.paid ? "Có" : "Không"}</Descriptions.Item>
          <Descriptions.Item label="Phí vận chuyển">{order.shipping.fee.toLocaleString()} đ</Descriptions.Item>
          <Descriptions.Item label="Hình thức vận chuyển">{order.shipping.method}</Descriptions.Item>
          <Descriptions.Item label="Địa chỉ giao hàng">
            {`${order.deliveryAddress.address}, ${order.deliveryAddress.ward.wardName}, ${order.deliveryAddress.district.districtName}, ${order.deliveryAddress.province.provinceName}`}
          </Descriptions.Item>
          <Descriptions.Item label="Số điện thoại">{order.deliveryAddress.phone}</Descriptions.Item>
        </Descriptions>
        <Space className={s["action-buttons"]}>
          <Button
            className={s["print-button"]}
            type="primary"
            icon={<PrinterOutlined />}
            onClick={printOrder}
          >
            In đơn hàng
          </Button>
          {order.status === "PENDING" && (
            <Button
              className={s["processing-button"]}
              type="default"
              icon={<SyncOutlined />}
              onClick={() => showConfirmModal("APPROVED")}
            >
              Chấp nhận đơn hàng
            </Button>
          )}
          {order.status === "APPROVED" && (
            <Button
              className={s["processing-button"]}
              type="default"
              icon={<SyncOutlined />}
              onClick={() => showConfirmModal("DELIVERING")}
            >
              Bắt đầu giao hàng
            </Button>
          )}
          {order.status === "DELIVERING" && (
            <Button
              className={s["complete-button"]}
              type="primary"
              icon={<CheckCircleOutlined />}
              onClick={() => showConfirmModal("DELIVERED")}
            >
              Hoàn thành
            </Button>
          )}
          {order.status !== "CANCELED" && order.status !== "DELIVERED" && order.status !== "DELIVERING" && (
            <Button
              className={s["cancel-button"]}
              type="danger"
              icon={<CloseCircleOutlined />}
              onClick={() => showConfirmModal("CANCELED")}
            >
              Hủy đơn hàng
            </Button>
          )}
        </Space>
      </Card>

      <Modal
        title="Xác nhận cập nhật trạng thái"
        visible={isConfirmModalVisible}
        onOk={handleConfirmUpdate}
        onCancel={handleCancelUpdate}
        okText="Xác nhận"
        cancelText="Hủy"
      >
        <p>
          Bạn có chắc chắn muốn cập nhật trạng thái đơn hàng thành{" "}
          <strong>{newStatus}</strong> không?
        </p>
        <p style={{ color: "red" }}>Lưu ý: Hành động này không thể hoàn tác.</p>
      </Modal>
    </div>
  );
};

export default DetailOrder;