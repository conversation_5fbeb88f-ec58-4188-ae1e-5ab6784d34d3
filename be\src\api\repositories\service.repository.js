import Service from '../models/service.model.js';
import NotFoundError from '../utils/response/NotFoundError.js';
import BadRequestError from '../utils/response/BadRequestError.js';
import pagination from '../helper/pagination.js';

const search = async (filters = {}, options = {}) => {
  const { page = 1, pageSize = 10 } = options;
  const { offset, limit } = pagination(page, pageSize);
  const sort = options.sort || { updatedAt: -1 };
  const rows = await Service.find(filters).sort(sort).skip(offset).limit(limit);
  const count = await Service.countDocuments(filters);
  return { count, rows };
};

const create = async (data) => {
  try {
    return await Service.create(data);
  } catch (error) {
    throw new BadRequestError(error.message);
  }
};

const findById = async (id) => {
  return await Service.findOne({ _id: id, isDeleted: false });
};

const update = async (id, data = {}) => {
  try {
    const service = await Service.findOne({ _id: id, isDeleted: false });
    if (!service) {
      throw new NotFoundError('Service not found');
    }
    Object.assign(service, data);
    await service.save();
    return service;
  } catch (error) {
    throw new BadRequestError(error.message);
  }
};

const softDelete = async (id) => {
  const service = await Service.findOne({ _id: id, isDeleted: false });
  if (!service) {
    throw new NotFoundError('Service not found');
  }
  service.isDeleted = true;
  await service.save();
  return true;
};

export default { search, create, findById, update, softDelete }; 