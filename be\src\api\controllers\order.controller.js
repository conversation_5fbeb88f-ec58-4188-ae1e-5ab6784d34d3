import { HttpStatusCode } from 'axios';
import orderService from '../services/order.service.js';

const getAllOrders = async (req, res) => {
  const data = await orderService.search(req);
  if (data.status === HttpStatusCode.Ok) {
    return res.status(data.status).send(data.data);
  }

  return res.status(data.status).send(data);
};

const getAllOrdersV2 = async (req, res) => {
  const data = await orderService.searchAdmin(req);
  if (data.status === HttpStatusCode.Ok) {
    return res.status(data.status).send(data.data);
  }

  return res.status(data.status).send(data);
};

const getOrderById = async (req, res) => {
  const data = await orderService.getOrderById(req);

  if (data.status === HttpStatusCode.Ok) {
    return res.status(data.status).send(data.data);
  }

  return res.status(data.status).send(data);
};

const createOrder = async (req, res) => {
  console.log('req.body', req.body);
  const data = await orderService.createOrder(req);
  if (data.status === HttpStatusCode.Ok) {
    return res.status(data.status).send(data.data);
  }

  return res.status(data.status).send(data);
};

const changeDeliveryAddress = async (req, res) => {
  const data = await orderService.changeDeliveryAddress(req);
  if (data.status === HttpStatusCode.Ok) {
    return res.status(data.status).send(data.data);
  }

  return res.status(data.status).send(data);
};

const updateOrderStatus = async (req, res) => {
  const data = await orderService.updateOrderStatus(req);
  if (data.status === HttpStatusCode.Ok) {
    return res.status(data.status).send(data.data);
  }

  return res.status(data.status).send(data);
};

const updateOrderStatusAdmin = async (req, res) => {
  const data = await orderService.updateOrderStatusAdmin(req);
  if (data.status === HttpStatusCode.Ok) {
    return res.status(data.status).send(data.data);
  }

  return res.status(data.status).send(data);
};

const getMetricsByDateRange = async (req, res) => {
  const data = await orderService.getMetricsByDateRange(req);
  if (data.status === HttpStatusCode.Ok) {
    return res.status(data.status).send(data.data);
  }

  return res.status(data.status).send(data);
};

const getOrderMetricsByMonthsInYear = async (req, res) => {
  const data = await orderService.getOrderMetricsByMonthsInYear(req);
  if (data.status === HttpStatusCode.Ok) {
    return res.status(data.status).send(data.data);
  }

  return res.status(data.status).send(data);
};

export default {
  changeDeliveryAddress,
  getAllOrders,
  createOrder,
  updateOrderStatus,
  getOrderById,
  getAllOrdersV2,
  updateOrderStatusAdmin,
  getMetricsByDateRange,
  getOrderMetricsByMonthsInYear,
};
