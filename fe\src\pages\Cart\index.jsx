import { useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import s from "./styles.module.css"
import { Col, Row, Typography } from "antd";
import { fetchCart, removeFromCart, updateCartItem } from "../../redux/slices/cartSlice";
import { useNavigate } from "react-router-dom";
const CartPage = () => {
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const { cartItems } = useSelector((state) => state.cart);
    const [showModal, setShowModal] = useState(false);
    const [selectedItem, setSelectedItem] = useState({});
    const [selectedItemsForCheckout, setSelectedItemsForCheckout] = useState([]);
    const [totalPrice, setTotalPrice] = useState(0);
    const [note] = useState("");

    useEffect(() => {
        let newTotalPrice = 0;
        if (selectedItemsForCheckout.length > 0) {
            newTotalPrice = selectedItemsForCheckout.reduce(
                (total, item) => {
                    const isCombo = !!item.comboId;
                    let price = 0;
                    if (isCombo) {
                        const target = item.comboId;
                        price = target?.discountPrice || target?.price || 0;
                    } else {
                        price = item.variantId?.price ?? item.productId?.price ?? 0;
                    }
                    return total + item.quantity * price;
                },
                0
            );
        }
        setTotalPrice(newTotalPrice);
    }, [selectedItemsForCheckout, cartItems]);


    const handleOpenDeleteModal = (cartItem) => {
        setSelectedItem(cartItem);
        setShowModal(true);
    };

    const handleConfirmDelete = () => {
        if (selectedItem) {
            const isCombo = !!selectedItem.comboId;
            let itemPayload = {};
            if (isCombo) {
                itemPayload.comboId = selectedItem.comboId._id;
            } else {
                itemPayload.productId = selectedItem.productId._id;
                if (selectedItem.variantId?._id) itemPayload.variantId = selectedItem.variantId._id;
            }

            dispatch(removeFromCart({ item: itemPayload }));

            setSelectedItemsForCheckout((prevSelectedItems) =>
                prevSelectedItems.filter((item) => {
                    if (isCombo) {
                        return item.comboId?._id !== selectedItem.comboId._id;
                    }
                    return !(
                        item.productId?._id === selectedItem.productId._id &&
                        (!item.variantId || item.variantId._id === selectedItem?.variantId?._id)
                    );
                })
            );
        }

        setShowModal(false);
    };




    const handleSelectItemForCheckout = (item) => {
        const isCombo = !!item.comboId;
        const isItemSelected = selectedItemsForCheckout.some((selectedItem) => {
            if (isCombo) {
                return selectedItem.comboId?._id === item.comboId._id;
            }
            return (
                selectedItem.productId?._id === item.productId._id &&
                (!selectedItem?.variantId || selectedItem?.variantId._id === item?.variantId?._id)
            );
        });

        if (isItemSelected) {
            setSelectedItemsForCheckout((prevSelectedItems) =>
                prevSelectedItems.filter((selectedItem) => {
                    if (isCombo) {
                        return selectedItem.comboId?._id !== item.comboId._id;
                    }
                    return !(
                        selectedItem.productId?._id === item.productId._id &&
                        (!selectedItem?.variantId || selectedItem?.variantId._id === item?.variantId?._id)
                    );
                })
            );
        } else {
            setSelectedItemsForCheckout((prevSelectedItems) => [...prevSelectedItems, item]);
        }
    };

    const handleQuantityChange = (id, variantId, quantity, isCombo = false) => {
        if (quantity === 0) {
            const payload = isCombo ? { comboId: id } : { productId: id, variantId };
            dispatch(removeFromCart({ item: payload }));
            setSelectedItemsForCheckout((prevSelectedItems) =>
                prevSelectedItems.filter(
                    (item) =>
                        isCombo
                            ? item.comboId?._id !== id
                            : !(item.productId._id === selectedItem.productId && item.variantId?._id === selectedItem.variantId)
                )
            );
            return;
        }
        const updatePayload = isCombo ? { comboId: id, quantity } : { productId: id, variantId, quantity };
        dispatch(updateCartItem({ item: updatePayload }));
        dispatch(fetchCart());

        setSelectedItemsForCheckout((prevSelectedItems) =>
            prevSelectedItems.map((item) => {
                const isMatch = isCombo
                    ? item.comboId?._id === id
                    : item.productId?._id === id && (!variantId || !item?.variantId || item?.variantId._id === variantId);
                return isMatch ? { ...item, quantity } : item;
            })
        );
    };

    const handleInputQuantityChange = (id, variantId, e, isCombo = false) => {
        const newQuantity = parseInt(e.target.value, 10);
        handleQuantityChange(id, variantId, newQuantity, isCombo);

    };

    return (
        <div className={s.cartContainer}>
            <Row md={24} >
                <Col md={16} className={s.sectionLeft}>
                    <Typography className={s.title}>Giỏ hàng của bạn</Typography>
                    <div className={s['divider']}></div>
                    <div className={s.cartItems}>
                        {cartItems && cartItems.length > 0 ? (
                            cartItems.map((item, index) => {
                                // Create unique key for each cart item
                                const itemKey = item.comboId 
                                    ? `combo-${item.comboId._id || item.comboId}-${index}`
                                    : `product-${item.productId._id || item.productId}-${item.variantId?._id || 'novariant'}-${index}`;

                                const isCombo = !!item.comboId;
                                const target = isCombo ? item.comboId : item.productId;
                                const optionIndexes = item.variantId?.optionIndex || [];
                                const productOptions = target?.options || [];
                                const optionImage = isCombo
                                  ? target?.thumbnail?.url || "https://placehold.co/100x100"
                                  : (item.variantId
                                        ? (
                                            productOptions[0]?.values?.[item.variantId.optionIndex[0]]?.image?.url ||
                                            target?.thumbnail?.url ||
                                            "https://placehold.co/100x100"
                                        )
                                        : target?.thumbnail?.url || "https://placehold.co/100x100");
                                const selectedOptionsText = isCombo ? '' : (optionIndexes.length > 0
                                  ? optionIndexes
                                      .map((optIdx, i) => {
                                        if (productOptions[i] && productOptions[i].values?.[optIdx]) {
                                          return `${productOptions[i].values[optIdx].name}`;
                                        }
                                        return null;
                                      })
                                      .filter(Boolean)
                                      .join(', ')
                                  : "");
                                const price = item?.variantId?.price || target?.discountPrice || target?.price || 0;

                                return (
                                    <div key={itemKey} className={s.cartItem}>
                                        <button
                                            className={s.deleteIcon}
                                            onClick={() => handleOpenDeleteModal(item)}
                                        >
                                            Xóa
                                        </button>
                                        <div className={s.itemImage}>
                                            <img src={optionImage} alt={target?.name} className={s.optionImage} />
                                        </div>
                                        <div className={s.itemInfo}>
                                            <div>
                                                <span className={s.itemTitle}>{target?.name}</span>
                                                <p className={s.optionValues}>{selectedOptionsText}</p>
                                                <p className={s.optionValues}>Đơn giá: {price.toLocaleString()} ₫</p>

                                            </div>
                                            <div className={s.itemPrice}>
                                                <span style={{ fontWeight: 'bold' }}>{(price * item.quantity).toLocaleString()}₫</span>
                                                <div className={s.quantityControl}>
                                                    <button
                                                        onClick={() =>
                                                            handleQuantityChange(
                                                                isCombo ? item.comboId?._id : item.productId?._id,
                                                                item?.variantId?._id,
                                                                item.quantity - 1,
                                                                isCombo
                                                            )
                                                        }
                                                    >
                                                        -
                                                    </button>
                                                    <input
                                                        className={s.quantityInput}
                                                        type="number"
                                                        value={item.quantity}
                                                        min="1"
                                                        onChange={(e) =>
                                                            handleInputQuantityChange(
                                                                isCombo ? item.comboId?._id : item.productId?._id,
                                                                item?.variantId?._id,
                                                                e,
                                                                isCombo
                                                            )
                                                        }
                                                    />

                                                    {/* Nút tăng số lượng */}
                                                    <button
                                                        onClick={() =>
                                                            handleQuantityChange(
                                                                isCombo ? item.comboId?._id : item.productId?._id,
                                                                item?.variantId?._id,
                                                                item.quantity + 1,
                                                                isCombo
                                                            )
                                                        }
                                                    >
                                                        +
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        <input
                                            type="checkbox"
                                            checked={selectedItemsForCheckout.includes(item)}
                                            onChange={() => handleSelectItemForCheckout(item)}
                                        />
                                    </div>
                                );
                            })
                        ) : (
                            <div className={s.cartTextEmpty}>Giỏ hàng của bạn đang trống</div>
                        )}
                        {/* {cartItems && cartItems.length > 0 && (
                            <div className={s.noteSection}>
                                <label htmlFor="orderNote">Ghi chú đơn hàng:</label>
                                <textarea
                                    id="orderNote"
                                    className={s.noteInput}
                                    placeholder="Nhập ghi chú của bạn (ví dụ: Giao hàng sau 18h)"
                                    value={note}
                                    onChange={(e) => setNote(e.target.value)}
                                />
                            </div>
                        )} */}
                    </div>
                </Col>

                <Col md={8} className={s.sectionRight}>
                    <Typography className={s.title}>Thông tin dịch vụ đơn hàng</Typography>
                    <div className={s['divider']}></div>

                    <div className={s.orderSummary}>
                        <div className={s.total}>
                            <span className={s.totalText}>Tổng tiền</span>
                            <span className={s.totalPrice}>{totalPrice.toLocaleString()}₫</span>
                        </div>
                        <div className={s['divider']}></div>
                        <ul>
                            <li className={s.infoText}>Phí vận chuyển mặc định là 30.000VND cho mọi đơn hàng.</li>
                            <li className={s.infoText}>Giao hàng hỏa tốc trong vòng 4 giờ, áp dụng tại khu vực nội thành Hà Nội</li>
                        </ul>

                        <button
                            className={s.checkoutButton}
                            onClick={() => {
                                navigate("/checkout", {
                                    state: {
                                        cartItems: selectedItemsForCheckout.length > 0 ? selectedItemsForCheckout : cartItems, note, totalPrice
                                    },
                                });
                            }}
                            disabled={totalPrice <= 0}
                        >
                            Thanh toán
                        </button>
                    </div>
                </Col>
            </Row>
            {showModal && (
                <div className={s.modal}>
                    <div className={s.modalContent}>
                        <p>Bạn có chắc chắn muốn xóa sản phẩm này?</p>
                        <button onClick={() => setShowModal(false)}>Hủy</button>
                        <button onClick={handleConfirmDelete} className={s.confirmDelete}>Xác nhận</button>
                    </div>
                </div>
            )}
        </div>
    );
};

export default CartPage;
