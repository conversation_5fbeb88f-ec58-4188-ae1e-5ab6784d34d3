import { useEffect, useState } from 'react';
import { List, Spin, message } from 'antd';
import CommentApi from '../../api/CommentApi.js';
import CommentItem from './CommentItem.jsx';

const CommentList = ({ productId, target = null, reloadFlag }) => {
  const [loading, setLoading] = useState(false);
  const [comments, setComments] = useState([]);
  const [pagination, setPagination] = useState({ page: 1, pageSize: 5, total: 0 });

  const fetchComments = async (page = 1) => {
    setLoading(true);
    try {
      const params = target ? (target.type==='combo' ? { comboId: target.id } : { productId: target.id }) : { productId };
      const res = await CommentApi.listComments({ ...params, page, pageSize: pagination.pageSize });
      const data = res.data?.data || {};
      setComments(data.rows || []);
      setPagination((prev) => ({ ...prev, page, total: data.count || 0 }));
    } catch {
      message.error('Không tải được bình luận');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchComments(1);
    // eslint-disable-next-line
  }, [productId, target?.id, reloadFlag]);

  const handlePageChange = (p) => fetchComments(p);

  if (loading) return <Spin />;

  return (
    <List
      dataSource={comments}
      itemLayout="vertical"
      pagination={{
        current: pagination.page,
        pageSize: pagination.pageSize,
        total: pagination.total,
        onChange: handlePageChange,
        hideOnSinglePage: true,
      }}
      renderItem={(item) => (
        <CommentItem
          key={item._id}
          item={item}
          onDeleteSuccess={() => fetchComments(pagination.page)}
        />
      )}
    />
  );
};

export default CommentList; 