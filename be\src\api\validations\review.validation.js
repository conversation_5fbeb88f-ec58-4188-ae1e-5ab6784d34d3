import Joi from 'joi';
import mongoose from 'mongoose';

const objectId = Joi.string().custom((value, helpers) => {
  if (!mongoose.Types.ObjectId.isValid(value)) return helpers.message('Invalid Object ID');
  return value;
});

const createReview = {
  body: Joi.object({
    productId: objectId.optional(),
    comboId: objectId.optional(),
    rating: Joi.number().min(1).max(5).required(),
    comment: Joi.string().max(1000).allow(null, ''),
  }).xor('productId', 'comboId'),
};

const updateReview = {
  body: Joi.object({
    rating: Joi.number().min(1).max(5),
    comment: Joi.string().max(1000).allow(null, ''),
  }),
};

export default { createReview, updateReview }; 