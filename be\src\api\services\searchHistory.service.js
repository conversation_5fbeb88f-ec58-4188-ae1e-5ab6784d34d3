import { searchHistoryRepository } from '../repositories/index.js';
import ResponseDataSuccess from '../utils/response/ResponseDataSuccess.js';
import InternalServerError from '../utils/response/InternalServerError.js';
import NotFoundError from '../utils/response/NotFoundError.js';

const addKeyword = async (userId, keyword) => {
  try {
    const record = await searchHistoryRepository.addKeyword(userId, keyword);
    return new ResponseDataSuccess(record);
  } catch (error) {
    return new InternalServerError(error);
  }
};

const list = async (userId, options) => {
  try {
    const res = await searchHistoryRepository.listByUser(userId, options);
    return new ResponseDataSuccess(res);
  } catch (error) {
    return new InternalServerError(error);
  }
};

const deleteOne = async (userId, id) => {
  try {
    const result = await searchHistoryRepository.deleteOne(userId, id);
    if (result.deletedCount === 0) return new NotFoundError('History not found');
    return new ResponseDataSuccess('Deleted');
  } catch (error) {
    return new InternalServerError(error);
  }
};

const deleteAll = async (userId) => {
  try {
    await searchHistoryRepository.deleteAll(userId);
    return new ResponseDataSuccess('Deleted all');
  } catch (error) {
    return new InternalServerError(error);
  }
};

export default {
  addKeyword,
  list,
  deleteOne,
  deleteAll,
}; 