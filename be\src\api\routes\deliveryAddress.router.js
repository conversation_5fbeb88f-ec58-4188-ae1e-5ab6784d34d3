import express from 'express';
import { deliveryAddressController } from '../controllers/index.js';
import deliveryAddressValidation from '../validations/deliveryAddress.validation.js';
import validate from '../middlewares/validates.js';
import { verifyToken } from '../middlewares/authenticateJWT.js';


const deliveryAddressRouter = express.Router();

deliveryAddressRouter.get('/user/:userId', verifyToken, deliveryAddressController.search);
deliveryAddressRouter.post(
    '/user/:userId', verifyToken, validate(deliveryAddressValidation.create),
    deliveryAddressController.create
);
deliveryAddressRouter.put(
    '/:id',
    verifyToken, validate(deliveryAddressValidation.update),
    deliveryAddressController.update
);

deliveryAddressRouter.put(
    '/:id/default',
    verifyToken, validate(deliveryAddressValidation.deleteValidation),
    deliveryAddressController.setDefault
);
deliveryAddressRouter.delete(
    '/:id',
    verifyToken, validate(deliveryAddressValidation.deleteValidation),
    deliveryAddressController.deleteDeliveryAddress
);

export default deliveryAddressRouter;
