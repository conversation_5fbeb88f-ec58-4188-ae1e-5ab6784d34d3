import reviewService from '../services/review.service.js';

const createReview = async (req, res) => {
  const result = await reviewService.createReview(req);
  res.status(result.status).json(result);
};

const getReviews = async (req, res) => {
  const result = await reviewService.getReviews(req);
  res.status(result.status).json(result);
};

const getReviewById = async (req, res) => {
  const result = await reviewService.getReviewById(req);
  res.status(result.status).json(result);
};

const updateReview = async (req, res) => {
  const result = await reviewService.updateReview(req);
  res.status(result.status).json(result);
};

const deleteReview = async (req, res) => {
  const result = await reviewService.deleteReview(req);
  res.status(result.status).json(result);
};

export default {
  createReview,
  getReviews,
  getReviewById,
  updateReview,
  deleteReview,
}; 