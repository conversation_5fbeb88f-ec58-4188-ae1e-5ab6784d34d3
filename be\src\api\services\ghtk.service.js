import axios from "axios";



const ghtkClient = axios.create({
    baseURL: GHTK_API_URL,
    headers: {
        'Content-Type': 'application/json',
        'Token': GHTK_API_KEY
    }
});

const createOrder = async (orderData) => {
    try {
        const response = await ghtkClient.post('/shipment/order', orderData);
        return response.data;
    } catch (error) {
        console.error('Error creating order:', error.response ? error.response.data : error.message);
        throw error;
    }
}

const getOrderStatus = async (orderId) => {
    try {
        const response = await ghtkClient.get(`/shipment/v2/${orderId}`);
        return response.data;
    } catch (error) {
        console.error('Error getting order status:', error.response ? error.response.data : error.message);
        throw error;
    }
}

export {
    createOrder,
    getOrderStatus
};