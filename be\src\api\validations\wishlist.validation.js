import Joi from 'joi';
import mongoose from 'mongoose';

const objectId = Joi.string().custom((value, helpers) => {
  if (!mongoose.Types.ObjectId.isValid(value)) {
    return helpers.message('Invalid Object ID');
  }
  return value;
});

const modifyItem = {
  body: Joi.object({
    productId: objectId.optional(),
    comboId: objectId.optional(),
    variantId: objectId.optional(),
  }).xor('productId', 'comboId'),
};

export default { modifyItem }; 