import { HttpStatusCode } from "axios";
import jwt from "jsonwebtoken";
import Account from "../models/account.model.js";
import config from "../config/config.js";
import { handleServerErrors } from "./errorHandlers.js";
import { ErrorCodes } from "../constants/errorCode.js";

const verifyToken = async (req, res, next) => {
  let token = req.cookies.accessToken;
  try {
    const decoded = await jwt.verify(token, process.env.ACCESS_TOKEN_SECRET);
    req.user = decoded.user;
    next();
  } catch (error) {
    return res.status(HttpStatusCode.Unauthorized).send({
      status: HttpStatusCode.Unauthorized,
      message: ErrorCodes.EC004,
    });
  }
};

const refreshAccessToken = async (req, res) => {
  const { refreshToken } = req.body;

  if (!refreshToken) {
    return res.status(HttpStatusCode.Unauthorized).json({ message: ErrorCodes.EC005 });
  }

  try {
    const user = await Account.findOne({ refreshToken });
    if (!user) {
      return res.status(HttpStatusCode.Forbidden).json({ message: ErrorCodes.EC014 });
    }

    // Xác thực refreshToken
    const decoded = await jwt.verify(refreshToken, config.refreshTokenSecret);

    // Tạo mới accessToken
    const accessToken = createAccessToken({ userId: decoded.userId });
    res.json({ accessToken });
  } catch (error) {
    return handleServerErrors(error.message);
  }
};

// Tạo accessToken
const createAccessToken = (bodyForToken) => {
  return jwt.sign({ user: bodyForToken }, config.accessTokenSecret, {
    expiresIn: process.env.ACCESS_TOKEN_LIFE,
  });
};

// Tạo refreshToken
const createRefreshToken = (bodyForToken) => {
  return jwt.sign({ user: bodyForToken }, config.refreshTokenSecret, {
    expiresIn: process.env.REFRESH_TOKEN_LIFE,
  });
};

const createActivationLink = (userId) => {
  const localIP = "localhost"

  const expiresIn = config.activationSecret;
  const token = jwt.sign({ userId }, config.activationSecret, { expiresIn });

  return `${localIP}/auth/activate?token=${token}`;
};

const verifyActivation = (req, res) => {
  const { token } = req.query;
  if (!token) return res.status(400).json({ status: 400, message: ErrorCodes.EC004 });

  try {
    const decoded = jwt.verify(token, config.activationSecret);
    res.json({ status: 200, message: ErrorCodes.EC013, userId: decoded.userId });
  } catch (error) {
    res.status(400).json({ status: 400, message: ErrorCodes.EC008 });
  }
};

export { verifyToken, refreshAccessToken, createAccessToken, createRefreshToken, createActivationLink, verifyActivation };