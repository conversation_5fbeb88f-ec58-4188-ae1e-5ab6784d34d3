import Joi from 'joi';
import mongoose from 'mongoose';

const objectId = Joi.string().custom((value, helpers) => {
  if (!mongoose.Types.ObjectId.isValid(value)) {
    return helpers.message('Invalid id');
  }
  return value;
});

const search = {
  query: Joi.object({
    search: Joi.string().optional(),
    isBlocked: Joi.boolean().optional(),
    page: Joi.number().integer().min(1).optional(),
    pageSize: Joi.number().integer().min(1).max(100).optional(),
    sortField: Joi.string().optional(),
    sortOrder: Joi.string().valid('asc', 'desc').optional(),
  }),
};

const create = {
  body: Joi.object({
    email: Joi.string().email().required(),
    password: Joi.string().min(6).required(),
    fullName: Joi.string().min(6).required(),
    address: Joi.string().optional().allow('', null),
    phone: Joi.string()
      .pattern(/^0\d{8,10}$/)
      .optional()
      .allow('', null),
  }),
};

const updateStatus = {
  params: Joi.object({
    accountId: objectId.required(),
  }),
  body: Joi.object({
    reasonBlock: Joi.string().optional().allow('', null),
  }),
};

const updateRole = {
  params: Joi.object({
    accountId: objectId.required(),
  }),
  body: Joi.object({
    role: Joi.array().items(Joi.string().valid('USER', 'EMPLOYEE', 'ADMIN')).required(),
  }),
};

const updateInfo = {
  params: Joi.object({
    accountId: objectId.required(),
  }),
  body: Joi.object({
    fullName: Joi.string().min(6).optional(),
    address: Joi.string().optional().allow('', null),
    phone: Joi.string()
      .pattern(/^0\d{8,10}$/)
      .optional()
      .allow('', null),
    email: Joi.string().email().optional(),
  }),
};

export default { search, create, updateStatus, updateRole, updateInfo }; 