import { HttpStatusCode } from 'axios';
import { serviceService } from '../services/index.js';

const listServices = async (req, res) => {
  const data = await serviceService.list(req);
  if (data.status === HttpStatusCode.Ok) return res.status(data.status).send(data.data);
  return res.status(data.status).send(data);
};

const createService = async (req, res) => {
  const data = await serviceService.create(req);
  if (data.status === HttpStatusCode.Ok) return res.status(data.status).send(data.data);
  return res.status(data.status).send(data);
};

const getServiceById = async (req, res) => {
  const data = await serviceService.getById(req);
  if (data.status === HttpStatusCode.Ok) return res.status(data.status).send(data.data);
  return res.status(data.status).send(data);
};

const updateService = async (req, res) => {
  const data = await serviceService.update(req);
  if (data.status === HttpStatusCode.Ok) return res.status(data.status).send(data.data);
  return res.status(data.status).send(data);
};

const deleteService = async (req, res) => {
  const data = await serviceService.remove(req);
  return res.status(data.status).send(data);
};

export default { listServices, createService, getServiceById, updateService, deleteService }; 