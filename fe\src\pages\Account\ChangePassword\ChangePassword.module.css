.changePasswordContainer {
    min-width: 100vh;
    padding: 24px;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    background-color: #ffffff;
}

.title {
    font-size: 28px;
    font-weight: 700;
    color: #1a1a1a;
    text-align: center;
    margin-bottom: 8px;
}

.subtitle {
    font-size: 14px;
    color: #666666;
    text-align: center;
    margin-bottom: 32px;
}

.passwordForm {
    /* Không cần định kiểu quá nhiều vì Ant Design đã xử lý layout */
}

.passwordInput {
    border-radius: 8px;
    height: 40px;
    /* Đặt chiều cao cố định cho input */
}

.confirmButton {
    width: 100%;
    height: 40px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    background-color: #1890ff;
    border-color: #1890ff;
    transition: background-color 0.3s ease, transform 0.2s ease;
}

.confirmButton:hover {
    background-color: #40a9ff;
    border-color: #40a9ff;
    transform: translateY(-1px);
}

.confirmButton:active {
    background-color: #096dd9;
    border-color: #096dd9;
    transform: translateY(0);
}