{"name": "fe", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@ant-design/compatible": "^5.1.4", "@ant-design/icons": "^5.6.1", "@fontsource/quicksand": "^5.1.1", "@google/generative-ai": "^0.24.1", "@mui/x-tree-view": "^7.26.0", "@react-oauth/google": "^0.12.1", "@reduxjs/toolkit": "^2.5.1", "antd": "^5.24.0", "axios": "^1.7.9", "crypto-browserify": "^3.12.1", "dayjs": "^1.11.13", "dotenv": "^16.4.7", "fe": "file:", "js-cookie": "^3.0.5", "jwt-decode": "^4.0.0", "lucide-react": "^0.476.0", "os": "^0.1.2", "prop-types": "^15.8.1", "qrcode": "^1.5.4", "react": "^18.3.1", "react-dom": "^18.3.1", "react-google-charts": "^5.2.1", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "react-router": "^7.2.0", "react-router-dom": "^7.1.5", "react-toastify": "^11.0.3", "recharts": "^2.15.1", "socket.io-client": "^4.8.1", "swiper": "^11.2.4", "vite-plugin-node-polyfills": "^0.23.0", "yet-another-react-lightbox": "^3.21.7"}, "devDependencies": {"@eslint/js": "^9.19.0", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.19.0", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "vite": "^6.1.0"}}