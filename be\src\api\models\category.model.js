import mongoose from 'mongoose';
import slugify from 'slugify';
import NotFoundError from '../utils/response/NotFoundError.js';
import BadRequestError from '../utils/response/BadRequestError.js';
import { COLLECTIONS, MODELS } from '../utils/constants.js';

const categorySchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
      minlength: 3,
      maxlength: 40,
    },
    slug: {
      type: String,
    },
    parentId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: MODELS.CATEGORY,
      default: null,
      immutable: true,
    },
    path: {
      type: String,
    },
    level: {
      type: Number,
      min: 1,
      max: 3,
    },
    isHide: {
      type: Boolean,
      default: false,
    },
    isDeleted: {
      type: Boolean,
      default: false,
    },
  },
  {
    collection: COLLECTIONS.CATEGORY,
    timestamps: false,
    toJSON: {
      transform: function (doc, ret) {
        delete ret.isDeleted;
        delete ret.__v;
        return ret;
      },
    },
    toObject: {
      transform: function (doc, ret) {
        delete ret.isDeleted;
        delete ret.__v;
        return ret;
      },
    },
  }
);

categorySchema.index({ name: 1, isDeleted: 1 });

categorySchema.pre('save', async function (next) {
  this.slug = slugify(this.name.replace('&', ''), {
    lower: true,
    strict: true,
    locale: 'vi',
  });

  // check name uniqueness, with document is not soft-deleted
  if (this.isNew || this.isModified('name')) {
    const existingName = await this.constructor.findOne({
      name: this.name,
      isDeleted: false,
      _id: { $ne: this._id }, // exclude the current document itself when updating
    });
    if (existingName) {
      return next(new BadRequestError('Category name already exists'));
    }
  }

  // handle path and level hierarchy
  if (this.isNew) {
    if (!this.parentId) {
      this.path = this._id.toString();
      this.level = 1;
    } else {
      const parent = await this.constructor.findById(this.parentId);

      if (!parent) {
        return next(new NotFoundError('Parent category not found'));
      }

      if (parent.level === 3) {
        return next(new BadRequestError('Maximum category depth (3) exceeded'));
      }

      this.path = `${parent.path}.${this._id}`;
      this.level = parent.level + 1;
    }
  }
  next();
});

const Category = mongoose.model(
  MODELS.CATEGORY,
  categorySchema,
  COLLECTIONS.CATEGORY
);

export default Category;
