import jwt from "jsonwebtoken";

const getTokenInfoFromRequest = (req) => {
  const authHeader = req.headers["authorization"];
  if (authHeader && authHeader.startsWith("Bearer ")) {
    const token = authHeader.split(" ")[1];
    try {
      const decoded = jwt.verify(token, process.env.ACCESS_TOKEN_SECRET);
      return decoded; // Trả về nội dung đã giải mã
    } catch (err) {
      console.error("Token không hợp lệ:", err);
      return null;
    }
  }
  return null;
};

const authorizeRoles = (...roles) => {
  return (req, res, next) => {
    const user = req.user;
    if (!user) {
      return res
        .status(401)
        .json({ message: "Unauthorized: Không có token hợp lệ" });
    }
    if (!user.role.some(r => roles.includes(r))) {
      return res
        .status(403)
        .json({ message: "Forbidden: <PERSON><PERSON><PERSON> <PERSON>hông có quyền truy cập" });
    }
    next();
  };
};

export default authorizeRoles;
