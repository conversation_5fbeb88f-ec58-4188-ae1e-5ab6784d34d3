import { HttpStatusCode } from "axios";
import Account from "../models/account.model.js";
import { authService } from "../services/index.js";
import { ErrorCodes } from "../constants/errorCode.js";
import { verifyTokenGoogle } from "../utils/verifyTokenGoogle.js";
import bcrypt from "bcryptjs";
import config from "../config/config.js";

const loginWithGoogle = async (req, res) => {
  try {
    const { token } = req.body;
    const payload = await verifyTokenGoogle(token);

    const googleId = payload.sub;
    const email = payload.email;
    const displayName = payload.name;
    const response = await authService.loginWithGoogle(email, googleId, displayName);
    if (response.status === 200) {
      res.cookie('accessToken', response.accessToken, {
        // httpOnly: true,
        secure: false,
        // secure: config.accessTokenSecret,
        maxAge: 1000 * 60 * 1500,
        sameSite: "Strict",
        domain: 'localhost',
      });

      res.cookie('refreshToken', response.refreshToken, {
        // httpOnly: true,
        // secure: config.refreshTokenSecret,
        maxAge: 1000 * 60 * 60 * 24 * 7,
        sameSite: "Strict",
        secure: false,
      });
    }
    return res.status(response.status).json({ status: response.status, message: response.message, accessToken: response.accessToken });
  } catch (error) {
    console.error("Error in loginWithGoogle:", error);
    res.status(500).json({ message: "Internal Server Error" });
  }
};

const login = async (req, res) => {
  const { email, password } = req.body;
  const response = await authService.login(email, password);
  if (response.status === 200) {
    res.cookie('accessToken', response.accessToken, {
      // httpOnly: true,
      secure: false,
      // secure: config.accessTokenSecret,
      maxAge: 1000 * 60 * 1500,
      sameSite: "Strict",
      domain: 'localhost',
    });

    res.cookie('refreshToken', response.refreshToken, {
      // httpOnly: true,
      // secure: config.refreshTokenSecret,
      maxAge: 1000 * 60 * 60 * 24 * 7,
      sameSite: "Strict",
      domain: 'localhost',
      secure: false,
    });
  }

  return res.status(response.status).json({ status: response.status, message: response.message, accessToken: response.accessToken });
};

const register = async (req, res) => {
  const data = req.body;
  const response = await authService.register(data);
  return res.status(response.status).json(response);
};


const refreshToken = async (req, res) => {
  const response = await authService.refreshToken(req, res);
  return res.status(response.status).json(response);
};

const activeUserByEmail = async (req, res) => {

  const { token } = req.body;


  const account = await Account.findOne({ "activation.token": token, "activation.expires": { $gt: new Date() } })
  if (!account) {
    return res.status(HttpStatusCode.BadRequest).send({ errorCode: ErrorCodes.EC004 });
  }

  try {
    account.status.isBlocked = false;
    account.status.reasonBlock = "";
    account.activation.token = null;
    account.activation.expires = null;
    await account.save();

    // return res.status(HttpStatusCode.Ok).json({ status: HttpStatusCode.Ok, message: ErrorCodes.EC013 });
    return res.redirect("http://localhost:5173/active-user");
  } catch (error) {
    return res.status(HttpStatusCode.InternalServerError).json({ status: HttpStatusCode.InternalServerError, message: error.message });

  }
}

const changeStatusAccount = async (req, res) => {

}

const editAccount = async (req, res) => {
  const data = req.body;
  const response = await authService.editAccount(data);
  return res.status(response.status).json(response);

}

const logout = async (req, res) => {
  try {
    res.cookie('accessToken', '', {
      // httpOnly: true,
      secure: config.accessTokenSecret,
      maxAge: 0
    });

    res.cookie('refreshToken', '', {
      // httpOnly: true,
      secure: config.refreshTokenSecret,
      maxAge: 0
    });
    return res.status(200).json({
      status: 200,
      message: 'Logout successful'
    });
  } catch (error) {
    return res.status(500).json({
      status: 500,
      message: 'Logout failed',
      error: error.message
    });
  }
}


const forgotPassword = async (req, res) => {
  const { email } = req.body;
  const response = await authService.forgotPassword(email);
  return res.status(response.status).json(response);
}

const resetPassword = async (req, res) => {
  const { token, newPassword } = req.body;
  const account = await Account.findOne({ "resetPWToken.token": token, "resetPWToken.expires": { $gt: new Date() } })
  if (!account) {
    return res.status(HttpStatusCode.BadRequest).send({ status: HttpStatusCode.BadRequest, message: ErrorCodes.EC009 });
  }
  account.resetPWToken.token = null;
  account.resetPWToken.expires = null;
  const hashedPassword = await bcrypt.hash(newPassword, 10);
  account.password = hashedPassword;
  await account.save();
  return res.status(HttpStatusCode.Created).json({ status: HttpStatusCode.Created, message: ErrorCodes.EC020 });
}

const checkTokenExprided = async (req, res) => {

  const { resetPWToken, activeToken } = req.body;
  if (resetPWToken) {
    const response = await authService.checkResetPWToken(resetPWToken);
    return res.status(response.status).json(response);
  }
  const response = await authService.checkActiveToken(activeToken);
  return res.status(response.status).json(response);
}

const changePassword = async (req, res) => {
  const { password, newPassword } = req.body;
  const response = await authService.changePassword(password, newPassword, req);
  return res.status(response.status).json(response);
}

const checkAuth = (req, res) => {
  const accessToken = req.cookies.accessToken;
  if (!accessToken) {
    return res.status(401).json({ message: 'Unauthorized' });
  }
  const decoded = jwt.verify(accessToken, config.accessTokenSecret);
  res.json({ accessToken, user: decoded.user });
};

export default { login, register, refreshToken, activeUserByEmail, editAccount, loginWithGoogle, logout, forgotPassword, resetPassword, checkTokenExprided, changePassword, checkAuth };
