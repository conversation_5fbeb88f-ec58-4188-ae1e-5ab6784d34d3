import { commentRepository } from '../repositories/index.js';
import BadRequestError from '../utils/response/BadRequestError.js';
import NotFoundError from '../utils/response/NotFoundError.js';
import ResponseDataSuccess from '../utils/response/ResponseDataSuccess.js';
import InternalServerError from '../utils/response/InternalServerError.js';

const createComment = async (req) => {
  try {
    const { productId = null, comboId = null, content, parentId = null } = req.body;
    const userId = req.user?.userId;

    // Optional: validate parent comment belongs to same product
    if (parentId) {
      const parent = await commentRepository.findById(parentId);
      if (!parent || String(parent.productId) !== String(productId)) {
        return new BadRequestError('Parent comment invalid');
      }
    }

    const comment = await commentRepository.create({ productId, comboId, content, parentId, userId });
    return new ResponseDataSuccess(comment);
  } catch (err) {
    return new InternalServerError(err.message);
  }
};

const getComments = async (req) => {
  try {
    const { productId = null, comboId = null, page = 1, pageSize = 10 } = req.query;
    const res = await commentRepository.searchRoots(productId, comboId, page, pageSize);

    // add reply count
    const rowsWithCount = await Promise.all(
      res.rows.map(async (c) => {
        const cnt = await commentRepository.countReplies(c._id);
        return { ...c.toObject(), replyCount: cnt };
      })
    );
    return new ResponseDataSuccess({ rows: rowsWithCount, count: res.count });
  } catch (err) {
    return new InternalServerError(err.message);
  }
};

const getReplies = async (req) => {
  try {
    const { parentId, page = 1, pageSize = 10 } = req.query;
    const res = await commentRepository.searchReplies(parentId, page, pageSize);
    return new ResponseDataSuccess(res);
  } catch (err) {
    return new InternalServerError(err.message);
  }
};

const updateComment = async (req) => {
  try {
    const { id } = req.params;
    const comment = await commentRepository.findById(id);
    if (!comment || comment.isDeleted) return new NotFoundError('Comment not found');
    if (String(comment.userId) !== String(req.user?.userId)) return new BadRequestError('No permission');

    const updated = await commentRepository.updateById(id, { content: req.body.content });
    return new ResponseDataSuccess(updated);
  } catch (err) {
    return new InternalServerError(err.message);
  }
};

const deleteComment = async (req) => {
  try {
    const { id } = req.params;
    const comment = await commentRepository.findById(id);
    if (!comment || comment.isDeleted) return new NotFoundError('Comment not found');
    if (String(comment.userId) !== String(req.user?.userId)) return new BadRequestError('No permission');

    await commentRepository.softDelete(id);
    return new ResponseDataSuccess({ message: 'Deleted' });
  } catch (err) {
    return new InternalServerError(err.message);
  }
};

const likeComment = async (req) => {
  try {
    const { id } = req.params;
    const updated = await commentRepository.toggleLike(id, req.user?.userId);
    return new ResponseDataSuccess(updated);
  } catch (err) {
    return new InternalServerError(err.message);
  }
};

export default {
  createComment,
  getComments,
  getReplies,
  updateComment,
  deleteComment,
  likeComment,
}; 