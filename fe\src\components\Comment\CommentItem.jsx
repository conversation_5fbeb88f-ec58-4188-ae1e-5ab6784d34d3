/* eslint-disable react/prop-types */
import { useState } from 'react';
import { Avatar, Tooltip, message } from 'antd';
import { Comment } from '@ant-design/compatible';
import { LikeOutlined, LikeFilled, DeleteOutlined } from '@ant-design/icons';
import CommentForm from './CommentForm.jsx';
import CommentApi from '../../api/CommentApi.js';
import { useSelector } from 'react-redux';

const CommentItem = ({ item, onDeleteSuccess }) => {
  const { userInfo } = useSelector((state) => state.user);
  const [showReplyForm, setShowReplyForm] = useState(false);
  const [liked, setLiked] = useState(item.likedBy?.some((id)=>id===userInfo?.userId));
  const [likes, setLikes] = useState(item.likedBy?.length || 0);
  const [childReplies, setChildReplies] = useState([]);
  const [showReplies, setShowReplies] = useState(false);

  const isRoot = !item.parentId;

  const handleLike = async () => {
    try {
      await CommentApi.toggleLike(item._id);
      setLiked(!liked);
      setLikes((l) => l + (liked ? -1 : 1));
    } catch {
      message.error('Lỗi like');
    }
  };

  const handleDelete = async () => {
    try {
      await CommentApi.remove(item._id);
      message.success('Đã xoá');
      onDeleteSuccess?.();
    } catch (e) { console.error(e); }
  };

  const loadReplies = async () => {
    try {
      const res = await CommentApi.listReplies({ parentId: item._id, page: 1, pageSize: 10 });
      setChildReplies(res.data?.data?.rows || []);
    } catch (e) { console.error(e); }
  };

  return (
    <Comment
      author={item.userId?.fullName || 'User'}
      avatar={<Avatar>{item.userId?.fullName?.charAt(0) || 'U'}</Avatar>}
      content={<p>{item.content}</p>}
      datetime={<Tooltip title={new Date(item.createdAt).toLocaleString()}>{new Date(item.createdAt).toLocaleDateString('vi-VN')}</Tooltip>}
      actions={[
        <span key="like" onClick={handleLike} style={{ userSelect: 'none' }}>
          {liked ? <LikeFilled /> : <LikeOutlined />} {likes}
        </span>,
        isRoot && (
          <span key="reply" onClick={() => setShowReplyForm((v) => !v)}>
            Trả lời
          </span>
        ),
        isRoot && item.replyCount > 0 && (
          <span key="view" onClick={async () => {
            if (!showReplies) { setShowReplies(true); }
            await loadReplies();
          }}>
            {showReplies ? 'Ẩn' : `Xem ${item.replyCount} phản hồi`}
          </span>
        ),
        item.userId?._id === userInfo?.userId && (
          <span key="delete" onClick={handleDelete}>
            <DeleteOutlined /> Xoá
          </span>
        ),
      ]}
    >
      {showReplyForm && (
        <CommentForm
          productId={item.productId}
          parentId={item._id}
          afterSubmit={async () => {
            setShowReplyForm(false);
            if (!showReplies) setShowReplies(true);
            await loadReplies();
          }}
        />
      )}

      {showReplies && childReplies.map((rep) => (
        <CommentItem key={rep._id} item={rep} onDeleteSuccess={loadReplies} />
      ))}
    </Comment>
  );
};

export default CommentItem; 