import React, { useEffect, useState } from "react";
import s from "./ManageProducts.module.css";
import { Table, Input, Select, Button, Space, Tag, Image, Modal } from "antd";
import { useNavigate } from "react-router-dom";
import {
  EditOutlined,
  DeleteOutlined,
  EyeInvisibleOutlined,
  EyeOutlined,
} from "@ant-design/icons";
import ProductApi from "../../../api/ProductApi";
import { toast } from "react-toastify";
import { removeUndefinedFields } from "../../../utils/removeUndefinedFields";

const { Search } = Input;
const { Option } = Select;

const ManageProducts = () => {
  const [searchText, setSearchText] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("");
  const [selectedStatus, setSelectedStatus] = useState("");
  const [productList, setProductList] = useState([]);
  const [selectedProduct, setSelectedProduct] = useState({});
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isDeleteModalVisible, setIsDeleteModalVisible] = useState(false);

  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalItems, setTotalItems] = useState(0);

  const navigate = useNavigate();

  // Hàm lấy danh sách sản phẩm từ API
  const fetchProducts = async (filters = {}) => {
    try {
      const paginationFilters = {
        ...filters,
        page: currentPage,
        pageSize: pageSize,
      };
      const data = await ProductApi.getAllProductsAdmin(paginationFilters);
      setProductList(data.rows || []);
      setTotalItems(data.count || 0);
    } catch (error) {
      console.error("Lỗi khi lấy danh sách sản phẩm:", error);
      toast.error("Lấy danh sách sản phẩm thất bại");
    }
  };

  // useEffect để gọi fetchProducts khi searchText, currentPage hoặc pageSize thay đổi
  useEffect(() => {
    fetchProducts({ query: searchText });
  }, [searchText, currentPage, pageSize]);

  // Cấu hình các cột của bảng
  const columns = [
    {
      title: "Tên sản phẩm",
      dataIndex: "name",
      key: "name",
      sorter: (a, b) => a.name.localeCompare(b.name),
      render: (text) => <span>{text}</span>,
      width: "25%",
      align: "center",
    },
    {
      title: "Ảnh",
      dataIndex: "thumbnail",
      key: "thumbnail",
      render: (thumbnail) => <Image width={60} src={thumbnail?.url} />,
      width: "10%",
      align: "center",
    },
    {
      title: "Giá",
      dataIndex: "price",
      key: "price",
      sorter: (a, b) => a.price - b.price,
      render: (price) => `${price.toLocaleString()}đ`,
      width: "15%",
      align: "center",
    },
    {
      title: "Danh mục",
      dataIndex: "categoryId",
      key: "categoryId",
      render: (categoryId) =>
        categoryId ? categoryId.name : "Không có danh mục",
      width: "20%",
      align: "center",
    },
    {
      title: "Trạng thái",
      dataIndex: "isHide",
      key: "isHide",
      filters: [
        { text: "Đang hoạt động", value: "Đang hoạt động" },
        { text: "Đang ẩn", value: "Đang ẩn" },
      ],
      onFilter: (value, record) =>
        (!record.isHide ? "Đang hoạt động" : "Đang ẩn") === value,
      render: (isHide) => (
        <Tag color={!isHide ? "green" : "red"}>
          {!isHide ? "Đang hoạt động" : "Đang ẩn"}
        </Tag>
      ),
      width: "15%",
      align: "center",
    },
    {
      title: "Hành động",
      key: "action",
      render: (_, record) => (
        <Space size="middle">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => navigate("/admin/update-product/" + record._id)}
            title="Sửa"
            style={{ color: "#fadb14" }}
          />
          <Button
            type="link"
            icon={record.isHide ? <EyeOutlined /> : <EyeInvisibleOutlined />}
            onClick={() => showConfirmModal(record)}
            title={record.isHide ? "Hiển thị" : "Ẩn"}
          />
          <Button
            type="link"
            icon={<DeleteOutlined size={24} />}
            onClick={() => showDeleteModal(record)}
            title="Xóa"
            danger
          />
        </Space>
      ),
      width: "15%",
      align: "center",
      fixed: "right",
    },
  ];

  // Xử lý tìm kiếm
  const handleSearch = (value) => {
    setSearchText(value.toLowerCase());
    setCurrentPage(1); // Reset về trang 1 khi tìm kiếm
  };

  // Xử lý thay đổi phân trang
  const handleTableChange = (pagination) => {
    setCurrentPage(pagination.current);
    setPageSize(pagination.pageSize);
  };

  // Hiển thị modal xác nhận thay đổi trạng thái
  const showConfirmModal = (product) => {
    setSelectedProduct(product);
    setIsModalVisible(true);
  };

  // Xử lý khi nhấn OK trong modal thay đổi trạng thái
  const handleOk = async () => {
    if (selectedProduct) {
      try {
        await ProductApi.updateProducts(selectedProduct._id, {
          isHide: !selectedProduct.isHide,
        });
        toast.success("Cập nhật trạng thái thành công");
        fetchProducts({ query: searchText });
      } catch (error) {
        console.error("Lỗi khi cập nhật trạng thái:", error);
        toast.error("Cập nhật trạng thái thất bại");
      }
    }
    setIsModalVisible(false);
  };

  // Hủy modal thay đổi trạng thái
  const handleCancel = () => {
    setIsModalVisible(false);
  };

  // Hiển thị modal xác nhận xóa
  const showDeleteModal = (product) => {
    setSelectedProduct(product);
    setIsDeleteModalVisible(true);
  };

  // Xử lý khi nhấn OK trong modal xóa
  const handleDeleteOk = async () => {
    if (selectedProduct) {
      try {
        await ProductApi.deleteProduct(selectedProduct._id);
        toast.success("Xóa sản phẩm thành công");
        fetchProducts({ query: searchText });
      } catch (error) {
        console.error("Lỗi khi xóa sản phẩm:", error);
        toast.error("Xóa sản phẩm thất bại");
      }
    }
    setIsDeleteModalVisible(false);
  };

  // Hủy modal xóa
  const handleDeleteCancel = () => {
    setIsDeleteModalVisible(false);
  };

  return (
    <div>
      <h1 className={s["title"]}>Quản lý sản phẩm</h1>

      <div className={s["filter-container"]}>
        <Search
          placeholder="Tìm kiếm theo tên..."
          allowClear
          enterButton="Tìm kiếm"
          onSearch={handleSearch}
          className={s["search-input"]}
        />
        <Button type="primary" onClick={() => navigate("/admin/add-product")}>
          Tạo sản phẩm
        </Button>
      </div>
      <Table
        columns={columns}
        dataSource={productList}
        pagination={{
          current: currentPage,
          pageSize: pageSize,
          total: totalItems,
          showSizeChanger: true,
          pageSizeOptions: ["10", "20", "50"],
        }}
        onChange={handleTableChange}
      />
      <Modal
        title="Xác nhận"
        visible={isModalVisible}
        onOk={handleOk}
        onCancel={handleCancel}
        okText="Xác nhận"
        cancelText="Hủy"
        okButtonProps={{ danger: true }}
      >
        <p>
          Bạn có chắc chắn muốn{" "}
          {selectedProduct?.isHide ? "hiển thị" : "ẩn"} sản phẩm này không?
        </p>
      </Modal>

      <Modal
        title="Xác nhận xóa"
        visible={isDeleteModalVisible}
        onOk={handleDeleteOk}
        onCancel={handleDeleteCancel}
        okText="Xác nhận"
        cancelText="Hủy"
        okButtonProps={{ danger: true }}
      >
        <p>
          Bạn có chắc chắn muốn xóa sản phẩm "{selectedProduct?.name}" không?
          Hành động này không thể hoàn tác.
        </p>
      </Modal>
    </div>
  );
};

export default ManageProducts;