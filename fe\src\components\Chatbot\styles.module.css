.chatbotContainer {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
}

.chatToggleButton {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    border: none;
    font-size: 24px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    transition: transform 0.2s ease-in-out;
}

.chatToggleButton:hover {
    transform: scale(1.1);
}

.chatWindow {
    width: 350px;
    height: 500px;
    background-color: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.chatHeader {
    padding: 15px;
    background-color: var(--primary-color);
    color: white;
    font-weight: bold;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.chatHeader p {
    margin: 0;
    font-size: 16px;
}
.closeButton {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    font-size: 18px;
}

.chatBody {
    flex-grow: 1;
    padding: 15px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.message {
    padding: 10px 15px;
    border-radius: 20px;
    max-width: 80%;
    word-wrap: break-word;
}

.message.user {
    background-color: #e6f7ff;
    color: #333;
    align-self: flex-end;
    border-bottom-right-radius: 5px;

}

.message.bot {
    background-color: #f0f0f0;
    color: #333;
    align-self: flex-start;
    border-bottom-left-radius: 5px;
}

.message.bot.thinking span {
    animation: blink 1.4s infinite both;
    display: inline-block;
}

.message.bot.thinking span:nth-child(2) {
    animation-delay: 0.2s;
}

.message.bot.thinking span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes blink {
    0% {
        opacity: 0.2;
    }
    20% {
        opacity: 1;
    }
    100% {
        opacity: 0.2;
    }
}

.chatFooter {
    padding: 10px;
    border-top: 1px solid #f0f0f0;
    display: flex;
    gap: 10px;
}

.chatFooter input {
    flex-grow: 1;
    border: 1px solid #ddd;
    border-radius: 20px;
    padding: 10px 15px;
    font-size: 14px;
}
.chatFooter input:focus {
    outline: none;
    border-color: var(--primary-color);
}

.chatFooter button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    cursor: pointer;
    font-size: 18px;
    flex-shrink: 0;
}
.chatFooter button:hover {
    opacity: 0.9;
}