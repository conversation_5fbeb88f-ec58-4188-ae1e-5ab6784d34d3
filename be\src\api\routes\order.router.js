import express from 'express';
import vnpayController from '../controllers/vnpay.controller.js';
import orderValidation from '../validations/order.validation.js';
import orderController from '../controllers/order.controller.js';
import validate from '../middlewares/validates.js';
import { verifyToken } from '../middlewares/authenticateJWT.js';
import authorizeRoles from '../middlewares/authorizeRoles.js';
import { ROLES } from '../constants/role.js';

const orderRouter = express.Router();

orderRouter.get('/', function (req, res, next) {
  res.render('orderlist', { title: 'Danh sách đơn hàng' });
});

// Get payment View
orderRouter.get('/create_payment_url', function (req, res, next) {
  res.render('order', { title: 'Tạo mới đơn hàng', amount: 10000 });
});

// Truy vấn kết quả thanh toán View
orderRouter.get('/querydr', function (req, res, next) {
  res.render('querydr', { title: 'Truy vấn kết quả thanh toán' });
});

// Hoàn tiền giao dịch thanh toán View
orderRouter.get('/refund', function (req, res, next) {
  res.render('refund', { title: 'Hoàn tiền giao dịch thanh toán' });
});

// Create payment URL Link
orderRouter.post(
  '/create_payment_url',
  verifyToken,
  authorizeRoles(ROLES.USER),
  vnpayController.createPayment
);

orderRouter.get('/vnpay_return', vnpayController.handleReturn);

orderRouter.get('/vnpay_ipn', vnpayController.vnpayIPN);

// Truy vấn kết quả giao dịch (queryDr)
orderRouter.post(
  '/querydr',
  verifyToken,
  authorizeRoles(ROLES.USER),
  vnpayController.queryDR
);

// Hoàn tiền GD
orderRouter.post(
  '/refund',
  verifyToken,
  authorizeRoles(ROLES.ADMIN),
  vnpayController.refund
);

orderRouter.post(
  '/create-order',
  verifyToken,
  authorizeRoles(ROLES.USER, ROLES.EMPLOYEE, ROLES.ADMIN),
  validate(orderValidation.createOrder),
  orderController.createOrder
);

orderRouter.get(
  '/list',
  verifyToken,
  // authorizeRoles(ROLES.USER),
  validate(orderValidation.search),
  orderController.getAllOrders
);

orderRouter.get(
  '/admin/list',
  verifyToken,
  authorizeRoles(ROLES.EMPLOYEE, ROLES.ADMIN),
  orderController.getAllOrdersV2
);

orderRouter.get(
  '/:orderId',
  verifyToken,
  authorizeRoles(ROLES.USER, ROLES.ADMIN, ROLES.EMPLOYEE),
  validate(orderValidation.getOrderById),
  orderController.getOrderById
);

orderRouter.patch(
  '/:orderId/change-delivery-address',
  verifyToken,
  authorizeRoles(ROLES.USER),
  validate(orderValidation.changeOrderDeliveryAddress),
  orderController.changeDeliveryAddress
);

orderRouter.patch(
  '/:orderId/update-status',
  verifyToken,
  authorizeRoles(ROLES.USER, ROLES.EMPLOYEE, ROLES.ADMIN),
  validate(orderValidation.updateOrderStatus),
  orderController.updateOrderStatus
);

orderRouter.patch(
  '/admin/update-order-status',
  verifyToken,
  authorizeRoles(ROLES.EMPLOYEE, ROLES.ADMIN),
  validate(orderValidation.updateOrderStatusAdmin),
  orderController.updateOrderStatusAdmin
);

orderRouter.get(
  '/admin/metrics/date-range',
  verifyToken,
  authorizeRoles(ROLES.EMPLOYEE, ROLES.ADMIN),
  validate(orderValidation.getMetricsByDateRange),
  orderController.getMetricsByDateRange
);

orderRouter.get(
  '/admin/metrics/months-in-year',
  verifyToken,
  authorizeRoles(ROLES.EMPLOYEE, ROLES.ADMIN),
  orderController.getOrderMetricsByMonthsInYear
);

export default orderRouter;
