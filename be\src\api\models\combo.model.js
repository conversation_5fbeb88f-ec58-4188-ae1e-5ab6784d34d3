import mongoose from 'mongoose';
import { MODELS, COLLECTIONS, COMBO_STATUS } from '../utils/constants.js';
import { ImageSchema } from './product.model.js';

const comboProductItemSchema = new mongoose.Schema(
  {
    productId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: MODELS.PRODUCT,
      required: true,
    },
    variantId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: MODELS.VARIANT,
    },
    quantity: {
      type: Number,
      default: 1,
      min: 1,
    },
  },
  { _id: false }
);

const comboServiceItemSchema = new mongoose.Schema(
  {
    serviceId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: MODELS.SERVICE,
      required: true,
    },
    quantity: {
      type: Number,
      default: 1,
      min: 1,
    },
  },
  { _id: false }
);

const comboSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
      unique: true,
      trim: true,
      minlength: 3,
      maxlength: 100,
    },
    description: {
      type: String,
      maxlength: 1000,
      default: null,
    },
    price: {
      type: Number,
      required: true,
      min: 0,
    },
    discountPrice: {
      type: Number,
      min: 0,
    },
    products: [comboProductItemSchema],
    services: [comboServiceItemSchema],
    thumbnail: ImageSchema,
    images: [ImageSchema],
    ratingAvg: {
      type: Number,
      default: 0,
    },
    ratingCount: {
      type: Number,
      default: 0,
    },
    status: {
      type: String,
      enum: Object.values(COMBO_STATUS),
      default: COMBO_STATUS.ACTIVE,
    },
    isDeleted: {
      type: Boolean,
      default: false,
    },
  },
  {
    collection: COLLECTIONS.COMBO,
    timestamps: true,
    toJSON: {
      transform: function (doc, ret) {
        delete ret.__v;
        return ret;
      },
    },
    toObject: {
      transform: function (doc, ret) {
        delete ret.__v;
        return ret;
      },
    },
  }
);

// Unique name for non-deleted combos
comboSchema.index({ name: 1 }, { unique: true, partialFilterExpression: { isDeleted: false } });

const Combo = mongoose.model(MODELS.COMBO, comboSchema, COLLECTIONS.COMBO);

export default Combo; 