import Joi from 'joi';
import mongoose from 'mongoose';

const objectId = Joi.string().custom((value, helpers) => {
  if (!mongoose.Types.ObjectId.isValid(value)) {
    return helpers.message('Category not found');
  }
  return value;
});

const create = {
  body: Joi.object({
    name: Joi.string().min(3).max(40).required().messages({
      'string.min': 'Name must be at least 3 characters long.',
      'string.max': 'Name cannot exceed 40 characters.',
      'any.required': 'Please enter a name.',
    }),
    parentId: Joi.string().optional(),
    isHide: Joi.boolean().optional(),
  }),
};

const update = {
  params: Joi.object({
    id: objectId.required(),
  }),
  body: Joi.object({
    name: Joi.string().min(3).max(40).required().messages({
      'string.min': 'Name must be at least 3 characters long.',
      'string.max': 'Name cannot exceed 40 characters.',
      'any.required': 'Please enter a name.',
    }),
    isHide: Joi.boolean().optional(),
  }),
};

const deleteValidation = {
  params: Joi.object({
    id: objectId.required(),
  }),
};

export default { create, update, deleteValidation };
