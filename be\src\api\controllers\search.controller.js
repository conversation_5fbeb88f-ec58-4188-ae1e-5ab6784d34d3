import { HttpStatusCode } from 'axios';
import { searchHistoryService } from '../services/index.js';
import pick from '../utils/pick.js';

const list = async (req, res) => {
  const { user } = req;
  if (!user) return res.status(HttpStatusCode.Unauthorized).send({ message: 'Unauthorized' });
  const options = pick(req.query, ['page', 'pageSize']);
  const data = await searchHistoryService.list(user.userId || user.id || user._id, options);
  return res.status(data.status).send(data.data || data);
};

const deleteOne = async (req, res) => {
  const { user } = req;
  if (!user) return res.status(HttpStatusCode.Unauthorized).send({ message: 'Unauthorized' });
  const { id } = req.params;
  const data = await searchHistoryService.deleteOne(user.userId || user.id || user._id, id);
  return res.status(data.status).send(data.data || data);
};

const deleteAll = async (req, res) => {
  const { user } = req;
  if (!user) return res.status(HttpStatusCode.Unauthorized).send({ message: 'Unauthorized' });
  const data = await searchHistoryService.deleteAll(user.userId || user.id || user._id);
  return res.status(data.status).send(data.data || data);
};

export default {
  list,
  deleteOne,
  deleteAll,
}; 