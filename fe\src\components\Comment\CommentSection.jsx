import { useState } from 'react';
import { useSelector } from 'react-redux';
import CommentForm from './CommentForm.jsx';
import CommentList from './CommentList.jsx';

const CommentSection = ({ productId }) => {
  const { userInfo } = useSelector((state) => state.user);
  const [refresh, setRefresh] = useState(0);

  return (
    <div style={{ marginTop: 40 }}>
      <h2>Bình luận</h2>
      {userInfo?.accountId && (
        <CommentForm
          productId={productId}
          afterSubmit={() => setRefresh((r) => r + 1)}
        />
      )}
      <CommentList productId={productId} reloadFlag={refresh} />
    </div>
  );
};

export default CommentSection; 