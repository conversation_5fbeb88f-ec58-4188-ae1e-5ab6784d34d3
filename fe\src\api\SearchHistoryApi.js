import axiosClient from './baseAPI/AuthorBaseApi';

const SearchHistoryApi = {
  getList: ({ page = 1, pageSize = 10 } = {}) =>
    axiosClient.get('/api/v1/search-histories', {
      params: { page, pageSize },
    }).then((res) => res.data),

  deleteOne: (id) => axiosClient.delete(`/api/v1/search-histories/${id}`),
  deleteAll: () => axiosClient.delete('/api/v1/search-histories'),
};

export default SearchHistoryApi; 