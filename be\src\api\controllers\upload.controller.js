import { HttpStatusCode } from 'axios';
import { uploadService } from '../services/index.js';

const uploadSingleImage = async (req, res) => {
  const data = await uploadService.uploadSingleImage(req);
  if (data.status === HttpStatusCode.Ok) {
    return res.status(data.status).send(data.data);
  }
  return res.status(data.status).send(data);
};

const uploadMultipleImages = async (req, res) => {
  const data = await uploadService.uploadMultipleImages(req);
  if (data.status === HttpStatusCode.Ok) {
    return res.status(data.status).send(data.data);
  }
  return res.status(data.status).send(data);
};

const deleteSingleImage = async (req, res) => {
  const data = await uploadService.deleteSingleImage(
    req.query.public_id.trim()
  );
  if (data.status === HttpStatusCode.Ok) {
    return res.status(data.status).send(data.data);
  }
  return res.status(data.status).send(data);
};

const deleteMultipleImages = async (req, res) => {
  const data = await uploadService.deleteMultipleImages(req.body.public_ids);
  if (data.status === HttpStatusCode.Ok) {
    return res.status(data.status).send(data.data);
  }
  return res.status(data.status).send(data);
};

export default {
  uploadSingleImage,
  uploadMultipleImages,
  deleteSingleImage,
  deleteMultipleImages,
};
