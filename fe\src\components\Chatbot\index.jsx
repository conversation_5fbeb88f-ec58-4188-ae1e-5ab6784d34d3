import React, { useState, useRef, useEffect } from 'react';
import { CommentOutlined, CloseOutlined, SendOutlined } from '@ant-design/icons';
import { GoogleGenerativeAI } from '@google/generative-ai';
import s from './styles.module.css';

// !!! QUAN TRỌNG: Thay thế bằng API Key của bạn
// Để bảo mật, trong môi trường production, bạn nên giấu key này và gọi API thông qua backend.
const API_KEY = 'AIzaSyCzrLnhe1SITdOiJArNKVKMzxtKm3tJR2g';

const Chatbot = () => {
    const [isOpen, setIsOpen] = useState(false);
    const [messages, setMessages] = useState([]);
    const [inputValue, setInputValue] = useState('');
    const [isThinking, setIsThinking] = useState(false);
    const chatBodyRef = useRef(null);

    // Automatically scroll to the bottom when messages change
    useEffect(() => {
        if (chatBodyRef.current) {
            chatBodyRef.current.scrollTop = chatBodyRef.current.scrollHeight;
        }
    }, [messages, isThinking]);

    const toggleChat = () => {
        setIsOpen(!isOpen);
        if (!isOpen && messages.length === 0) {
            setMessages([
                { from: 'bot', text: 'Chào bạn, tôi là trợ lý ảo từ PetHaven. Tôi có thể giúp gì cho bạn về các sản phẩm thú cưng?' }
            ]);
        }
    };

    const handleSendMessage = async () => {
        if (inputValue.trim() === '' || isThinking) return;

        const userMessage = { from: 'user', text: inputValue };
        const newMessages = [...messages, userMessage];
        
        setMessages(newMessages);
        setInputValue('');
        setIsThinking(true);

        try {
            if (API_KEY === 'YOUR_GEMINI_API_KEY') {
                throw new Error("API Key not provided.");
            }
            const genAI = new GoogleGenerativeAI(API_KEY);
            const model = genAI.getGenerativeModel({ model: "gemini-2.0-flash-lite" });
            
            // Lọc bỏ tin nhắn chào mừng của bot khỏi lịch sử gửi đến API
            const historyForApi = newMessages.length > 1 && newMessages[0].from === 'bot' 
                ? newMessages.slice(1, newMessages.length - 1)
                : newMessages.slice(0, newMessages.length - 1);

            const chat = model.startChat({
                history: historyForApi.map(msg => ({
                    role: msg.from === 'user' ? 'user' : 'model',
                    parts: [{ text: msg.text }],
                })),
                generationConfig: {
                    maxOutputTokens: 200,
                },
            });

            const result = await chat.sendMessage(inputValue);
            const response = await result.response;
            const text = response.text();

            setMessages(prevMessages => [...prevMessages, { from: 'bot', text }]);

        } catch (error) {
            console.error("Gemini API error:", error);
            const errorMessage = error.message.includes("API Key") 
                ? "Vui lòng cung cấp API Key của Gemini để tiếp tục."
                : "Xin lỗi, tôi đang gặp sự cố. Vui lòng thử lại sau.";
            setMessages(prevMessages => [...prevMessages, { from: 'bot', text: errorMessage }]);
        } finally {
            setIsThinking(false);
        }
    };

    return (
        <div className={s.chatbotContainer}>
            {isOpen && (
                <div className={s.chatWindow}>
                    <div className={s.chatHeader}>
                        <p>Pet Shop AI Assistant</p>
                        <button onClick={toggleChat} className={s.closeButton}><CloseOutlined /></button>
                    </div>
                    <div className={s.chatBody} ref={chatBodyRef}>
                        {messages.map((msg, index) => (
                            <div key={index} className={`${s.message} ${s[msg.from]}`}>
                                {msg.text}
                            </div>
                        ))}
                        {isThinking && (
                             <div className={`${s.message} ${s.bot} ${s.thinking}`}>
                                <span>.</span><span>.</span><span>.</span>
                            </div>
                        )}
                    </div>
                    <div className={s.chatFooter}>
                        <input
                            type="text"
                            value={inputValue}
                            onChange={(e) => setInputValue(e.target.value)}
                            onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                            placeholder="Hỏi về sản phẩm..."
                            disabled={isThinking}
                        />
                        <button onClick={handleSendMessage} disabled={isThinking}><SendOutlined /></button>
                    </div>
                </div>
            )}
            <button onClick={toggleChat} className={s.chatToggleButton}>
                <CommentOutlined />
            </button>
        </div>
    );
};

export default Chatbot; 