import express from 'express';
import { productController } from '../controllers/index.js';
import validate from '../middlewares/validates.js';
import productValidation from '../validations/product.validation.js';
import { verifyToken } from '../middlewares/authenticateJWT.js';
import authorizeRoles from '../middlewares/authorizeRoles.js';
import { ROLES } from '../constants/role.js';

const productRouter = express.Router();

// get by employee or admin
productRouter.get(
  '/admin',
  verifyToken,
  authorizeRoles(ROLES.EMPLOYEE, ROLES.ADMIN),
  validate(productValidation.search),
  productController.findAll
);



productRouter.get(
  '/admin/:id',
  verifyToken,
  authorizeRoles(ROLES.EMPLOYEE, ROLES.ADMIN),
  validate(productValidation.get),
  productController.findDetailById
);
productRouter.get(
  '/in-order/:id',
  verifyToken,
  validate(productValidation.get),
  productController.findDetailInOrderById
);

productRouter.get(
  '/',
  validate(productValidation.search),
  productController.findAll
);
productRouter.get('/:id', productController.findDetailById);

productRouter.post(
  '/',
  verifyToken,
  authorizeRoles(ROLES.EMPLOYEE, ROLES.ADMIN),
  validate(productValidation.create),
  productController.create
);
productRouter.patch(
  '/:id',
  verifyToken,
  authorizeRoles(ROLES.EMPLOYEE, ROLES.ADMIN),
  validate(productValidation.update),
  productController.update
);
productRouter.delete(
  '/:id',
  verifyToken,
  authorizeRoles(ROLES.ADMIN),
  validate(productValidation.deleteValidation),
  productController.delete
);

export default productRouter;
