import config from "../../config/config.js";

const ActivationEmail = (email, activationToken) => {
    const activationLink = `http://localhost:${config.port}/api/v1/auth/active`

    return `
    <!DOCTYPE html>
    <html lang="vi">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Kích <PERSON>t Tà<PERSON>ản - PetHaven</title>
    </head>
    <body>
        <h2>Chào ${email},</h2>
        <p>Vui lòng nhấn vào nút bên dưới để kích hoạt tài khoản của bạn.</p>
        <p><strong>Lưu ý:</strong> Link này sẽ hết hạn sau ${config.activattionTokenLife} ngày.</p>
        <form action="${activationLink}" method="POST">
            <input type="hidden" name="token" value="${activationToken}">
            <button type="submit" style="display: inline-block; padding: 12px 20px; background-color: #28a745; color: #ffffff; text-decoration: none; border-radius: 5px; border: none; cursor: pointer;">
            Kích Hoạt Tài Khoản
        </button>
</form>        
    </body>
    </html>`;
};

export const ActiveTitleMail = "Kích Hoạt Tài Khoản - PetHaven";
export default ActivationEmail;