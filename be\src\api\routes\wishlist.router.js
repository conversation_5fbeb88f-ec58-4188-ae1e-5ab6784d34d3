import express from 'express';
import validate from '../middlewares/validates.js';
import { wishlistController } from '../controllers/index.js';
import wishlistValidation from '../validations/wishlist.validation.js';
import { verifyToken } from '../middlewares/authenticateJWT.js';

const wishlistRouter = express.Router();

wishlistRouter.get('/', verifyToken, wishlistController.getWishlist);
wishlistRouter.post('/', verifyToken, validate(wishlistValidation.modifyItem), wishlistController.addWishlistItem);
wishlistRouter.delete('/', verifyToken, validate(wishlistValidation.modifyItem), wishlistController.deleteWishlistItem);

export default wishlistRouter; 