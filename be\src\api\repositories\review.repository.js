import Review from '../models/review.model.js';

const create = async (data) => await Review.create(data);

const findById = async (id) => await Review.findById(id);

const findByUserAndProduct = async (userId, productId, comboId = null) =>
  await Review.findOne({
    userId,
    isDeleted: false,
    ...(productId ? { productId } : {}),
    ...(comboId ? { comboId } : {}),
  });

const updateById = async (id, data) =>
  await Review.findByIdAndUpdate(id, data, { new: true });

const softDelete = async (id) =>
  await Review.findByIdAndUpdate(id, { isDeleted: true });

const search = async (filters = {}, options = {}) => {
  const { page = 1, pageSize = 10, sort = '-createdAt' } = options;
  const query = Review.find({ isDeleted: false, ...filters }).sort(sort);
  const countPromise = Review.countDocuments({ isDeleted: false, ...filters });
  const rowsPromise = query.skip((page - 1) * pageSize).limit(pageSize);
  const [count, rows] = await Promise.all([countPromise, rowsPromise]);
  return { count, rows };
};

const aggregateAvgRating = async ({ productId = null, comboId = null }) => {
  const match = { isDeleted: false };
  if (productId) match.productId = productId;
  if (comboId) match.comboId = comboId;

  const res = await Review.aggregate([
    { $match: match },
    {
      $group: {
        _id: productId ? '$productId' : '$comboId',
        avgRating: { $avg: '$rating' },
        total: { $sum: 1 },
      },
    },
  ]);
  return res[0] || { avgRating: 0, total: 0 };
};

export default {
  create,
  findById,
  findByUserAndProduct,
  updateById,
  softDelete,
  search,
  aggregateAvgRating,
}; 