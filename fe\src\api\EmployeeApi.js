// Create EmployeeApi
import AuthorApi from "./baseAPI/AuthorBaseApi";

class EmployeeApi {
  constructor() {
    this.url = "/api/v1";
  }

  // List employees with query params (pagination, search, etc.)
  getEmployees = async (params = {}) => {
    const query = new URLSearchParams(params).toString();
    return AuthorApi.get(`${this.url}/employees?${query}`);
  };

  // Create new employee
  createEmployee = async (data) => {
    return AuthorApi.post(`${this.url}/employees`, data);
  };

  // Toggle block / unblock
  toggleStatus = async (accountId, reasonBlock = "") => {
    return AuthorApi.patch(`${this.url}/employees/${accountId}/status`, { reasonBlock });
  };

  // Update role(s)
  updateRole = async (accountId, role) => {
    return AuthorApi.patch(`${this.url}/employees/${accountId}/role`, { role });
  };

  // Update employee basic info
  updateInfo = async (accountId, data) => {
    return AuthorApi.patch(`${this.url}/employees/${accountId}`, data);
  };
}

export default new EmployeeApi(); 