import {
  productRepository,
  categoryRepository,
  wishlistRepository,
} from '../repositories/index.js';
import InternalServerError from '../utils/response/InternalServerError.js';
import ResponseDataSuccess from '../utils/response/ResponseDataSuccess.js';
import NotFoundError from '../utils/response/NotFoundError.js';
import pick from '../utils/pick.js';
import { ROLES } from '../constants/role.js';
import searchHistoryService from './searchHistory.service.js';
import getTokenInfoFromRequest from '../middlewares/getTokenInfoFromRequest .js';
import notificationService from './notification.service.js';
import { NOTIFICATION_TYPE } from '../utils/constants.js';
import { getIO } from '../../socket/index.js';

const search = async (req) => {
  try {
    const filters = pick(req.query, ['query', 'categoryIds', 'price']);
    const options = pick(req.query, ['page', 'pageSize', 'sort']);
    const { query, categoryIds } = filters;

    // By default, only show products that are not deleted
    const queryFilters = { isDeleted: false };

    // If the user is not an admin or employee, hide the products that are marked as hidden
    if (
      !req.user ||
      (!req.user.role.includes(ROLES.ADMIN) &&
        !req.user.role.includes(ROLES.EMPLOYEE))
    ) {
      queryFilters.isHide = false;
    }

    // search by name or description
    if (query) {
      queryFilters.$text = {
        $search: query, // Wrap the query in double quotes to search for the exact phrase
        $caseSensitive: false,
        $diacriticSensitive: false,
      };
      // Determine userId for logging (from authenticated request if any)
      let userId = req?.user?.id || req?.user?._id || req?.user?.userId;
      console.log('userId', userId);
      if (!userId) {
        const decoded = getTokenInfoFromRequest(req);
        userId = decoded?.user?.id || decoded?.user?._id || decoded?.user?.userId;
        console.log('decoded', decoded);
      }
      console.log('userId', userId);
      if (!userId && req.cookies?.accessToken) {
        try {
          const jwtmod = await import('jsonwebtoken');
          const decodedCookie = jwtmod.default.verify(
            req.cookies.accessToken,
            process.env.ACCESS_TOKEN_SECRET
          );
          userId = decodedCookie?.user?.id || decodedCookie?.user?._id || decodedCookie?.user?.userId;
        } catch (err) {
          // ignore
        }
      }

      if (userId) {
        searchHistoryService
          .addKeyword(userId, query)
          .catch((err) => console.error('Failed to log search history', err));
      }
    }

    // filter by categoryIds array
    if (categoryIds) {
      queryFilters.$or = categoryIds.split(',').map((id) => ({
        categoryPath: { $regex: id, $options: 'i' }, // Regex to find sub category
      }));
    }

    const numberFields = ['price', 'quantity']; // Fields need to be Number
    numberFields.forEach((field) => {
      if (filters[field]) {
        queryFilters[field] = {};
        Object.keys(filters[field]).forEach((key) => {
          queryFilters[field][`$${key}`] = Number(filters[field][key]); // Add `$` before `gte`, `lte`
        });
      }
    });

    const res = await productRepository.search(queryFilters, options);
    return new ResponseDataSuccess(res);
  } catch (error) {
    return new InternalServerError(error);
  }
};

const create = async (req) => {
  try {
    const data = req.body;
    const { categoryId } = data;
    const category = await categoryRepository.findById(categoryId);
    if (!category) return new NotFoundError('Category not found');
    // Add the category path to the product data
    data.categoryPath = category.path;
    const request = await productRepository.create(data);
    return new ResponseDataSuccess(request);
  } catch (error) {
    if (error.status) {
      return error;
    }
    console.log(error);
    return new InternalServerError(error);
  }
};

const findDetailById = async (id, showHide) => {
  try {
    const res = await productRepository.findDetailById(id, showHide);
    return new ResponseDataSuccess(res);
  } catch (error) {
    return new InternalServerError(error);
  }
};

const findDetailInOrderById = async (id) => {
  try {
    const res = await productRepository.findDetailInOrderById(id);
    return new ResponseDataSuccess(res);
  } catch (error) {
    return new InternalServerError(error);
  }
};

const update = async (id, req) => {
  try {
    console.log(`[product.service] Starting update for product ${id}`);
    const data = req.body;

    // Fetch current product to detect quantity change
    const currentProduct = await productRepository.findDetailById(id, true);

    const { categoryId } = data;
    if (categoryId) {
      const category = await categoryRepository.findById(categoryId);
      if (!category) return new NotFoundError('Category not found');
      // Add the category path to the product data
      data.categoryPath = category.path;
    }
    const res = await productRepository.update(id, data);
    console.log(`[product.service] Update for product ${id} successful.`);

    // Stock out / back in stock notifications
    if (typeof data.quantity !== 'undefined') {
      const oldQty = currentProduct.quantity || 0;
      const newQty = data.quantity;

      if (oldQty > 0 && newQty === 0) {
        // Out of stock
        const userIds = await wishlistRepository.findUserIdsByProduct(id);
        userIds.forEach((uId) => {
          notificationService.create(
            {
              userId: uId,
              title: `Sản phẩm đã hết hàng`,
              message: `${currentProduct.name} hiện đang hết hàng.`,
              type: NOTIFICATION_TYPE.STOCK,
              refId: currentProduct._id,
            },
            getIO()
          );
        });
      }
      if (oldQty === 0 && newQty > 0) {
        const userIds = await wishlistRepository.findUserIdsByProduct(id);
        userIds.forEach((uId) => {
          notificationService.create(
            {
              userId: uId,
              title: `Sản phẩm đã có hàng lại`,
              message: `${currentProduct.name} đã có hàng trở lại!`,
              type: NOTIFICATION_TYPE.STOCK,
              refId: currentProduct._id,
            },
            getIO()
          );
        });
      }
    }

    return new ResponseDataSuccess(res);
  } catch (error) {
    if (error.status) {
      return error;
    }
    console.error(`[product.service] Error updating product ${id}:`, error);
    return new InternalServerError(error);
  }
};

const deleteProduct = async (id) => {
  try {
    const res = await productRepository.deleteProduct(id);
    return new ResponseDataSuccess(res);
  } catch (error) {
    return new InternalServerError(error);
  }
};

export default {
  search,
  create,
  findDetailById,
  findDetailInOrderById,
  update,
  deleteProduct,
};
