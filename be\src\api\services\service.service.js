import { serviceRepository } from '../repositories/index.js';
import ResponseDataSuccess from '../utils/response/ResponseDataSuccess.js';
import InternalServerError from '../utils/response/InternalServerError.js';
import NotFoundError from '../utils/response/NotFoundError.js';
import BadRequestError from '../utils/response/BadRequestError.js';

const list = async (req) => {
  try {
    const { page = 1, pageSize = 20, keyword } = req.query;
    const filters = { isDeleted: false };
    if (keyword) filters.name = { $regex: keyword, $options: 'i' };
    const data = await serviceRepository.search(filters, { page: Number(page), pageSize: Number(pageSize) });
    return new ResponseDataSuccess(data);
  } catch (error) {
    return new InternalServerError(error.message);
  }
};

const create = async (req) => {
  try {
    const service = await serviceRepository.create(req.body);
    return new ResponseDataSuccess(service);
  } catch (error) {
    if (error instanceof BadRequestError) return error;
    return new InternalServerError(error.message);
  }
};

const getById = async (req) => {
  try {
    const { id } = req.params;
    const service = await serviceRepository.findById(id);
    if (!service) return new NotFoundError('Service not found');
    return new ResponseDataSuccess(service);
  } catch (error) {
    return new InternalServerError(error.message);
  }
};

const update = async (req) => {
  try {
    const { id } = req.params;
    const service = await serviceRepository.update(id, req.body);
    return new ResponseDataSuccess(service);
  } catch (error) {
    if (error instanceof BadRequestError || error instanceof NotFoundError) return error;
    return new InternalServerError(error.message);
  }
};

const remove = async (req) => {
  try {
    const { id } = req.params;
    await serviceRepository.softDelete(id);
    return new ResponseDataSuccess({ message: 'Deleted service' });
  } catch (error) {
    if (error instanceof NotFoundError) return error;
    return new InternalServerError(error.message);
  }
};

export default { list, create, getById, update, remove }; 