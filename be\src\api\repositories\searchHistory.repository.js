import SearchHistory from '../models/searchHistory.model.js';

const addKeyword = async (userId, keyword) => {
  // If keyword existed for user, update createdAt else insert new
  return await SearchHistory.findOneAndUpdate(
    { userId, keyword },
    { $set: { keyword }, $currentDate: { updatedAt: true, createdAt: true } },
    { upsert: true, new: true }
  );
};

const listByUser = async (userId, options = {}) => {
  const { page = 1, pageSize = 10 } = options;
  const skip = (page - 1) * pageSize;
  const [items, total] = await Promise.all([
    SearchHistory.find({ userId })
      .sort({ updatedAt: -1 })
      .skip(skip)
      .limit(Number(pageSize)),
    SearchHistory.countDocuments({ userId }),
  ]);
  return {
    items,
    total,
    page: Number(page),
    pageSize: Number(pageSize),
  };
};

const deleteOne = async (userId, id) => {
  return await SearchHistory.deleteOne({ _id: id, userId });
};

const deleteAll = async (userId) => {
  return await SearchHistory.deleteMany({ userId });
};

export default {
  addKeyword,
  listByUser,
  deleteOne,
  deleteAll,
}; 