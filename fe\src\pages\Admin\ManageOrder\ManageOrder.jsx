import React, { useEffect, useState } from "react";
import { Table, Tag, Space, Input, Select, Button, DatePicker, message, Modal, Checkbox } from "antd";
import { SearchOutlined } from "@ant-design/icons";
import { useNavigate } from "react-router-dom";
import OrderApi from "../../../api/OrderApi";
import AddressApi from "../../../api/AddressApi";
import s from "./ManageOrder.module.css";
import { toast } from "react-toastify";

const { Option } = Select;
const { RangePicker } = DatePicker;

const statusColors = {
  PENDING: "orange",
  APPROVED: "blue",
  DELIVERING: "cyan",
  DELIVERED: "green",
  CANCELED: "red",
};

const validStatusTransitions = {
  PENDING: ["APPROVED", "CANCELED"],
  APPROVED: ["DELIVERING", "CANCELED"],
  DELIVERING: ["DELIVERED"],
  DELIVERED: [],
  CANCELED: [],
};

const ManageOrder = () => {
  const [orders, setOrders] = useState([]);
  const [displayOrders, setDisplayOrders] = useState([]);
  const [searchText, setSearchText] = useState(null); // Sửa thành null
  const [selectedStatus, setSelectedStatus] = useState(null); // Sửa thành null
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState(null); // Sửa thành null
  const [selectedPaymentStatus, setSelectedPaymentStatus] = useState(null); // Sửa thành null
  const [selectedShippingMethod, setSelectedShippingMethod] = useState(null); // Sửa thành null
  const [selectedProvince, setSelectedProvince] = useState(null); // Sửa thành null
  const [dateRange, setDateRange] = useState(null); // Sửa thành null
  const [totalPriceRange, setTotalPriceRange] = useState(null); // Sửa thành null
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [bulkStatus, setBulkStatus] = useState(null); // Sửa thành null
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalOrders, setTotalOrders] = useState(0);
  const [cities, setCities] = useState([]);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [isColumnModalVisible, setIsColumnModalVisible] = useState(false);
  const [visibleColumns, setVisibleColumns] = useState({
    orderId: true,
    customer: true,
    status: true,
    createdAt: true,
    total: true,
  });
  const [tempVisibleColumns, setTempVisibleColumns] = useState(visibleColumns);

  const navigate = useNavigate();

  const allColumns = [
    { title: "Mã đơn hàng", dataIndex: "orderId", key: "orderId", render: (text, record) => <a onClick={() => handleViewOrder(record.orderId)}>{text}</a> },
    { title: "Khách hàng", dataIndex: "customer", key: "customer" },
    { title: "Số điện thoại", dataIndex: "phone", key: "phone" },
    { title: "Trạng thái", dataIndex: "status", key: "status", render: (status) => <Tag color={statusColors[status] || "default"}>{status}</Tag> },
    { title: "Ngày đặt hàng", dataIndex: "createdAt", key: "createdAt" },
    { title: "Sản phẩm", dataIndex: "items", key: "items" },
    { title: "Phương thức thanh toán", dataIndex: "paymentMethod", key: "paymentMethod" },
    { title: "Trạng thái thanh toán", dataIndex: "paymentStatus", key: "paymentStatus" },
    { title: "Hình thức vận chuyển", dataIndex: "shippingMethod", key: "shippingMethod" },
    { title: "Địa chỉ giao hàng", dataIndex: "deliveryAddress", key: "deliveryAddress" },
    { title: "Tổng tiền", dataIndex: "total", key: "total" },
  ];

  const fetchCities = async () => {
    try {
      const response = await AddressApi.getAllCity();
      const cityData = response?.data || [];
      setCities(cityData);
    } catch (error) {
      console.error("Lỗi khi lấy danh sách tỉnh/thành phố:", error);
    }
  };

  const fetchOrders = async (currentPage = page, currentPageSize = pageSize) => {
    try {
      const payload = {
        status: selectedStatus || "", // Đảm bảo gửi chuỗi rỗng nếu null
        query: searchText || "", // Đảm bảo gửi chuỗi rỗng nếu null
        page: currentPage,
        pageSize: currentPageSize,
        paymentMethod: selectedPaymentMethod || "", // Đảm bảo gửi chuỗi rỗng nếu null
        paymentStatus: selectedPaymentStatus || "", // Đảm bảo gửi chuỗi rỗng nếu null
        province: selectedProvince || "", // Đảm bảo gửi chuỗi rỗng nếu null
        startDate: dateRange && dateRange[0] ? dateRange[0].toISOString() : "", // Kiểm tra dateRange
        endDate: dateRange && dateRange[1] ? dateRange[1].toISOString() : "", // Kiểm tra dateRange
        totalPriceRange: totalPriceRange || "", // Đảm bảo gửi chuỗi rỗng nếu null
      };

      const response = await OrderApi.getOrderList(
        payload.status,
        payload.query,
        payload.page,
        payload.pageSize,
        payload
      );

      const ordersData = response?.data?.data || [];
      const total = response?.data?.pagination.total || 0;

      const formattedOrders = ordersData.map((order) => ({
        key: order._id,
        orderId: order._id,
        customer: order.deliveryAddress.fullName,
        phone: order.deliveryAddress.phone,
        status: order.status,
        total: order.totalPrice.toLocaleString() + " đ",
        paymentMethod: order.payment.method,
        paymentStatus: order.payment.paid ? "Đã thanh toán" : "Chưa thanh toán",
        shippingMethod: order.shipping.method,
        shippingFee: order.shipping.fee.toLocaleString() + " đ",
        province: order.deliveryAddress.province.provinceName,
        createdAt: new Date(order.createdAt).toLocaleString(),
        items: order.items.map((item) => `${item.briefInfo.name} (${item.briefInfo.quantity})`).join(", "),
        totalPrice: order.totalPrice,
        deliveryAddress: `${order.deliveryAddress.address}, ${order.deliveryAddress.ward.wardName}, ${order.deliveryAddress.district.districtName}, ${order.deliveryAddress.province.provinceName}`,
      }));

      setOrders(formattedOrders);
      setDisplayOrders(formattedOrders);
      setTotalOrders(total);
    } catch (error) {
      console.error("Lỗi khi lấy đơn hàng:", error);
    }
  };

  useEffect(() => {
    fetchOrders();
    fetchCities();
  }, [page, pageSize]);

  const rowSelection = {
    selectedRowKeys,
    onChange: (newSelectedRowKeys) => {
      setSelectedRowKeys(newSelectedRowKeys);
    },
    getCheckboxProps: (record) => ({
      disabled: record.status === "DELIVERED" || record.status === "CANCELED" || record.status === "DELIVERING",
    }),
  };

  const getAvailableStatuses = () => {
    const selectedOrders = orders.filter((order) => selectedRowKeys.includes(order.key));
    const currentStatuses = [...new Set(selectedOrders.map((order) => order.status))];

    if (selectedOrders.length === 0 || currentStatuses.length > 1) {
      return [];
    }

    const currentStatus = currentStatuses[0];
    return validStatusTransitions[currentStatus] || [];
  };

  const handleBulkStatusChange = () => {



    if (!bulkStatus) {
      message.error("Vui lòng chọn trạng thái mới!");
      return;
    }

    if (selectedRowKeys.length === 0) {
      message.error("Vui lòng chọn ít nhất một đơn hàng!");
      return;
    }

    const selectedOrders = orders.filter((order) => selectedRowKeys.includes(order.key));
    const currentStatuses = [...new Set(selectedOrders.map((order) => order.status))];
    if (currentStatuses.length > 1) {
      toast.error("Chỉ có thể cập nhật các đơn hàng có cùng trạng thái!");
      return;
    }

    const currentStatus = currentStatuses[0];
    const validNextStatuses = validStatusTransitions[currentStatus] || [];

    if (!validNextStatuses.includes(bulkStatus)) {
      toast.error(`Không thể chuyển từ ${currentStatus} sang ${bulkStatus}!`);
      message.error(`Không thể chuyển từ ${currentStatus} sang ${bulkStatus}!`);
      return;
    }

    setIsModalVisible(true);
  };

  const handleConfirmUpdate = async () => {
    setLoading(true);
    try {
      await Promise.all(
        selectedRowKeys.map((orderId) =>
          OrderApi.updateStatusOrder(orderId, bulkStatus)
        )
      );
      message.success(`Đã cập nhật trạng thái cho ${selectedRowKeys.length} đơn hàng thành ${bulkStatus}`);
      setSelectedRowKeys([]);
      setBulkStatus(null); // Reset về null
      setIsModalVisible(false);
      fetchOrders();
    } catch (error) {
      console.error("Lỗi khi cập nhật trạng thái hàng loạt:", error);
      toast.error("Có lỗi xảy ra khi cập nhật trạng thái!");
    } finally {
      setLoading(false);
    }
  };

  const handleCancelUpdate = () => {
    setIsModalVisible(false);
  };

  const applyFilters = () => {
    setPage(1);
    fetchOrders(1, pageSize);
  };

  const resetFilters = () => {
    setSearchText(null); // Reset về null
    setSelectedStatus(null); // Reset về null
    setSelectedPaymentMethod(null); // Reset về null
    setSelectedPaymentStatus(null); // Reset về null
    setSelectedShippingMethod(null); // Reset về null
    setSelectedProvince(null); // Reset về null
    setDateRange(null); // Reset về null
    setTotalPriceRange(null); // Reset về null
    setSelectedRowKeys([]);
    setBulkStatus(null); // Reset về null
    setPage(1);
    fetchOrders(1, pageSize);
  };

  const handleViewOrder = (orderId) => {
    navigate(`/admin/orders/${orderId}`);
  };

  const handleTableChange = (pagination) => {
    setPage(pagination.current);
    setPageSize(pagination.pageSize);
    fetchOrders(pagination.current, pagination.pageSize);
  };

  const handleColumnChange = (key, checked) => {
    setTempVisibleColumns((prev) => ({
      ...prev,
      [key]: checked,
    }));
  };

  const handleConfirmColumnSelection = () => {
    setVisibleColumns(tempVisibleColumns);
    setIsColumnModalVisible(false);
  };

  const handleCancelColumnSelection = () => {
    setTempVisibleColumns(visibleColumns);
    setIsColumnModalVisible(false);
  };

  const filteredColumns = allColumns.filter((col) => visibleColumns[col.key]);
  const availableStatuses = getAvailableStatuses();

  const searchPlaceholder = displayOrders.length === 0
    ? "Không có đơn hàng nào để tìm kiếm"
    : "Tìm theo mã, tên khách, số điện thoại, sản phẩm...";

  return (
    <div className="order-management-container">
      <h1 style={{ textAlign: "left" }}>Quản lý đơn hàng</h1>

      <Space className={s["filter-bar"]} style={{ marginBottom: 20, marginTop: 20, maxWidth: "100%", flexWrap: "wrap" }} wrap>
        <Input
          placeholder={searchPlaceholder}
          prefix={<SearchOutlined />}
          value={searchText}
          onChange={(e) => setSearchText(e.target.value || null)} // Đảm bảo trả về null nếu rỗng
          style={{ width: 300 }}
          disabled={displayOrders.length === 0}
        />
        <Select
          placeholder="Trạng thái đơn hàng"
          value={selectedStatus}
          onChange={(value) => setSelectedStatus(value || null)} // Đảm bảo trả về null nếu rỗng
          allowClear
          style={{ width: 200 }}
        >
          {Object.keys(statusColors).map((status) => (
            <Option key={status} value={status}>
              {status}
            </Option>
          ))}
        </Select>
        <Select
          placeholder="Phương thức thanh toán"
          value={selectedPaymentMethod}
          onChange={(value) => setSelectedPaymentMethod(value || null)} // Đảm bảo trả về null nếu rỗng
          allowClear
          style={{ width: 250 }}
        >
          <Option value="COD">COD</Option>
          <Option value="CREDIT_CARD">Thẻ tín dụng</Option>
        </Select>
        <Select
          placeholder="Trạng thái thanh toán"
          value={selectedPaymentStatus}
          onChange={(value) => setSelectedPaymentStatus(value || null)} // Đảm bảo trả về null nếu rỗng
          allowClear
          style={{ width: 250 }}
        >
          <Option value="true">Đã thanh toán</Option>
          <Option value="false">Chưa thanh toán</Option>
        </Select>
        <Select
          placeholder="Phương thức vận chuyển"
          value={selectedShippingMethod}
          onChange={(value) => setSelectedShippingMethod(value || null)} // Đảm bảo trả về null nếu rỗng
          allowClear
          style={{ width: 250 }}
        >
          <Option value="STANDARD">Tiêu chuẩn</Option>
        </Select>
        <Select
          placeholder="Tỉnh/Thành phố"
          value={selectedProvince}
          onChange={(value) => setSelectedProvince(value || null)} // Đảm bảo trả về null nếu rỗng
          allowClear
          style={{ width: 250 }}
        >
          {cities.map((city) => (
            <Option key={city.cityId} value={city.name}>
              {city.name}
            </Option>
          ))}
        </Select>
        <RangePicker
          value={dateRange}
          onChange={(dates) => setDateRange(dates || null)} // Đảm bảo trả về null nếu rỗng
          placeholder={["Start date", "End date"]}
          style={{ width: 250 }}
        />
        <Select
          placeholder="Khoảng tổng tiền"
          value={totalPriceRange}
          onChange={(value) => setTotalPriceRange(value || null)} // Đảm bảo trả về null nếu rỗng
          allowClear
          style={{ width: 250 }}
        >
          <Option value="0-100000">Dưới 100k</Option>
          <Option value="100000-500000">100k - 500k</Option>
          <Option value="500000-1000000">500k - 1 triệu</Option>
          <Option value="1000000-9999999">Trên 1 triệu</Option>
        </Select>
        <Button type="primary" onClick={applyFilters}>
          Tìm kiếm & Lọc
        </Button>
        <Button type="dashed" onClick={resetFilters}>
          Reset
        </Button>
      </Space>

      <Space
        style={{
          marginBottom: 20,
          maxWidth: "100%",
          flexWrap: "wrap",
          display: "flex",
          justifyContent: "space-between",
          width: "100%",
        }}
      >
        <Space>
          <span>Đã chọn: {selectedRowKeys.length} đơn hàng</span>
          <Select
            placeholder="Chọn trạng thái mới"
            value={bulkStatus}
            onChange={(value) => setBulkStatus(value || null)} // Đảm bảo trả về null nếu rỗng
            style={{ width: 250 }}
            disabled={availableStatuses.length === 0}
          >
            {availableStatuses.map((status) => (
              <Option key={status} value={status}>
                {status}
              </Option>
            ))}
          </Select>
          <Button type="primary" onClick={handleBulkStatusChange}>
            Cập nhật trạng thái
          </Button>
        </Space>

        <Button
          onClick={() => {
            setTempVisibleColumns(visibleColumns);
            setIsColumnModalVisible(true);
          }}
          type="primary"
        >
          Chọn cột hiển thị
        </Button>
      </Space>

      <Table
        rowSelection={rowSelection}
        columns={filteredColumns}
        dataSource={displayOrders}
        className={s["order-table"]}
        locale={{
          emptyText: "Không có đơn hàng nào",
        }}
        pagination={{
          current: page,
          pageSize: pageSize,
          total: totalOrders,
          showSizeChanger: true,
          pageSizeOptions: ["10", "20", "50"],
          onChange: (page, pageSize) => handleTableChange({ current: page, pageSize }),
        }}
      />

      <Modal
        title="Xác nhận cập nhật trạng thái"
        visible={isModalVisible}
        onOk={handleConfirmUpdate}
        onCancel={handleCancelUpdate}
        okText="Xác nhận"
        cancelText="Hủy"
        confirmLoading={loading}
      >
        <p>
          Bạn có chắc chắn muốn cập nhật trạng thái của <strong>{selectedRowKeys.length}</strong> đơn hàng thành{" "}
          <strong>{bulkStatus}</strong> không?
        </p>
        <p style={{ color: "red" }}>Lưu ý: Hành động này không thể hoàn tác.</p>
      </Modal>

      <Modal
        title="Chọn cột hiển thị"
        visible={isColumnModalVisible}
        onOk={handleConfirmColumnSelection}
        onCancel={handleCancelColumnSelection}
        okText="Xác nhận"
        cancelText="Hủy"
      >
        <Space direction="vertical">
          {allColumns.map((col) => (
            <Checkbox
              key={col.key}
              checked={tempVisibleColumns[col.key] || false}
              onChange={(e) => handleColumnChange(col.key, e.target.checked)}
            >
              {col.title}
            </Checkbox>
          ))}
        </Space>
      </Modal>
    </div>
  );
};

export default ManageOrder;