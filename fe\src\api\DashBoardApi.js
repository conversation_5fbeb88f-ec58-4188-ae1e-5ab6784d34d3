// import storage from "../utils/storage"
import AuthorApi from "./baseAPI/AuthorBaseApi"



class DashBoardApi {

    constructor() {
        this.url = "/api/v1"
    }

    getInforByDate = async (date) => {
        return AuthorApi.get(`${this.url}/order/admin/metrics/date-range?datePeriod=${date}`);
    };

    
    getChart = async () => {
        return AuthorApi.get(`${this.url}/order/admin/metrics/months-in-year`);
    };

}

export default new DashBoardApi()
