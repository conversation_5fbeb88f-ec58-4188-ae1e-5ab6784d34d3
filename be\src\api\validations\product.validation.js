import Joi from 'joi';
import mongoose from 'mongoose';

const objectId = Joi.string().custom((value, helpers) => {
  if (!mongoose.Types.ObjectId.isValid(value)) {
    return helpers.message('Product not found');
  }
  return value;
});

const imageValidationRules = {
  public_id: Joi.string().trim().required().messages({
    'any.required': 'public_id is required',
  }),
  url: Joi.string().trim().required().messages({
    'any.required': 'url is required',
  }),
};

const imageSchema = Joi.object(imageValidationRules);

const optionValueSchema = Joi.object({
  name: Joi.string().trim().required().messages({
    'any.required': 'Option value is required',
  }),
  image: imageSchema.optional(),
});

const optionSchema = Joi.object({
  name: Joi.string().trim().required().messages({
    'any.required': 'Option name is required',
  }),
  values: Joi.array().items(optionValueSchema).min(1).required().messages({
    'array.min': 'Option must have at least one value',
    'any.required': 'Option values are required',
  }),
});

const variantSchema = Joi.object({
  sku: Joi.string().allow(null, ''),
  optionIndex: Joi.array().items(Joi.number().min(0)).required().messages({
    'array.base': 'Option indexes must be an array',
    'any.required': 'Option indexes are required',
  }),
  price: Joi.number().min(0).required().messages({
    'number.min': 'Price cannot be negative',
    'any.required': 'Price is required',
  }),
  quantity: Joi.number().integer().min(0).required().messages({
    'number.min': 'Quantity cannot be negative',
    'any.required': 'Quantity is required',
    'number.integer': 'Quantity must be an integer',
  }),
  isDefault: Joi.boolean().optional().messages({
    'boolean.base': 'isDefault must be a boolean',
  }),
});

const variantUpdateSchema = Joi.object({
  _id: objectId.optional(),
  sku: Joi.string().allow(null, ''),
  optionIndex: Joi.array().items(Joi.number().min(0)).optional().messages({
    'array.base': 'Option indexes must be an array',
  }),
  price: Joi.number().min(0).optional().messages({
    'number.min': 'Price cannot be negative',
  }),
  quantity: Joi.number().integer().min(0).optional().messages({
    'number.min': 'Quantity cannot be negative',
    'number.integer': 'Quantity must be an integer',
  }),
  isDefault: Joi.boolean().optional().messages({
    'boolean.base': 'isDefault must be a boolean',
  }),
});

// Custom validation function to ensure at most one default variant
const validateVariants = (variants, helpers) => {
  if (!variants || !Array.isArray(variants)) {
    return variants;
  }

  const defaultVariantsCount = variants.filter(
    (v) => v.isDefault === true
  ).length;

  if (defaultVariantsCount > 1) {
    return helpers.message('Only one variant can be set as default');
  }

  return variants;
};

const create = {
  body: Joi.object({
    name: Joi.string().trim().min(3).max(100).required().messages({
      'string.min': 'Name must be at least 3 characters long.',
      'string.max': 'Name cannot exceed 100 characters.',
      'any.required': 'Please enter a name.',
    }),
    categoryId: Joi.string().trim().required().messages({
      'any.required': 'Please select a category.',
    }),
    description: Joi.string().trim().max(1000).optional().messages({
      'string.max': 'Description cannot exceed 1000 characters',
    }),
    price: Joi.number().min(1).optional().messages({
      'number.min': 'Price cannot be less than 1',
    }),
    quantity: Joi.number().integer().min(0).optional().messages({
      'number.min': 'Quantity cannot be negative',
      'number.integer': 'Quantity must be an integer',
    }),
    options: Joi.array().items(optionSchema).max(2).optional().messages({
      'array.max': 'Product cannot have more than 2 options',
    }),
    variants: Joi.alternatives().conditional('options', {
      is: Joi.array().min(1).required(), // Explicitly check if options exist and have items
      then: Joi.array()
        .items(variantSchema)
        .min(1)
        .custom(validateVariants)
        .required()
        .messages({
          'array.min': 'Variants are required when options are provided.',
          'any.required':
            'Please provide at least one variant if options exist.',
        }),
      otherwise: Joi.array()
        .items(variantSchema)
        .custom(validateVariants)
        .optional(),
    }),
    thumbnail: imageSchema.optional(), // Use the schema directly
    images: Joi.array().items(imageSchema).optional().max(5).messages({
      'array.max': 'Product can only have up to 5 images',
    }),
    isHide: Joi.boolean().optional().messages({
      'boolean.base': 'isHide must be a boolean',
    }),
  }),
};

const update = {
  params: Joi.object({
    id: objectId.required(),
  }),
  body: Joi.object({
    name: Joi.string().trim().min(3).max(100).optional().messages({
      'string.min': 'Name must be at least 3 characters long.',
      'string.max': 'Name cannot exceed 100 characters.',
    }),
    categoryId: Joi.string().trim().optional(),
    description: Joi.string().trim().max(1000).optional().messages({
      'string.max': 'Description cannot exceed 1000 characters',
    }),
    price: Joi.number().min(1).optional().messages({
      'number.min': 'Price cannot be less than 1',
    }),
    quantity: Joi.number().integer().min(0).optional().messages({
      'number.min': 'Quantity cannot be negative',
      'number.integer': 'Quantity must be an integer',
    }),
    options: Joi.array().items(optionSchema).max(2).optional().messages({
      'array.max': 'Product cannot have more than 2 options',
    }),
    variants: Joi.array()
      .items(variantUpdateSchema)
      .custom(validateVariants)
      .optional(),
    imagesToKeep: Joi.string().optional().messages({
      'array.max': 'Images to keep is public_id and cannot exceed 5',
    }),
    thumbnail: imageSchema.optional(), // Use the schema directly
    images: Joi.array().items(imageSchema).optional().max(5).messages({
      'array.max': 'Product can only have up to 5 images',
    }),
    isHide: Joi.boolean().optional().messages({
      'boolean.base': 'isHide must be a boolean',
    }),
  }),
};

const deleteValidation = {
  params: Joi.object({
    id: objectId.required(),
  }),
};

const search = {
  query: Joi.object({
    query: Joi.string().trim().optional(),
    categoryIds: Joi.string().trim().optional(),
    price: Joi.any().optional(),
    quantity: Joi.any().optional(),
    page: Joi.number().min(1).optional(),
    pageSize: Joi.number().min(1).max(200).optional(),
    sort: Joi.string().optional(),
  }),
};

const get = {
  params: Joi.object({
    id: objectId.required(),
  }),
};

export default { create, update, deleteValidation, search, get };
