import { useState } from 'react';
import { Button, Form, Input, message } from 'antd';
import CommentApi from '../../api/CommentApi.js';

const { TextArea } = Input;

const CommentForm = ({ productId, target = null, parentId = null, afterSubmit }) => {
  const [content, setContent] = useState('');
  const [loading, setLoading] = useState(false);

  const handleSubmit = async () => {
    if (!content.trim()) {
      message.error('Nội dung không được trống');
      return;
    }
    setLoading(true);
    try {
      const payload = target ? (target.type==='combo' ? { comboId: target.id } : { productId: target.id }) : { productId };
      await CommentApi.create({ ...payload, content, parentId });
      setContent('');
      message.success('Đã gửi bình luận');
      afterSubmit?.();
    } catch (err) {
      message.error(err?.response?.data?.message || 'Lỗi bình luận');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Form layout="vertical" onFinish={handleSubmit}>
      <Form.Item>
        <TextArea
          rows={3}
          value={content}
          onChange={(e) => setContent(e.target.value)}
          maxLength={1000}
          placeholder="Viết bình luận ..."
        />
      </Form.Item>
      <Button type="primary" htmlType="submit" loading={loading}>
        Gửi
      </Button>
    </Form>
  );
};

export default CommentForm; 