import Wishlist from '../models/wishlist.model.js';
import NotFoundError from '../utils/response/NotFoundError.js';
import mongoose from 'mongoose';

const findByAccountId = async (accountId) =>
  await Wishlist.findOne({ accountId })
    .select('items.productId items.comboId');

const create = async (data, options) => await Wishlist.create(data, options);

const addItem = async (accountId, item, options) => {
  let wishlist = await Wishlist.findOne({ accountId });
  if (!wishlist) {
    wishlist = await Wishlist.create({ accountId, items: [item] }, options);
    return wishlist;
  }

  const isExisted = wishlist.items.some((i) => {
    if (item.comboId) {
      return i.comboId && i.comboId.toString() === item.comboId.toString();
    }
    return i.productId && i.productId.toString() === item.productId.toString();
  });

  if (!isExisted) {
    wishlist.items.push(item);
    await wishlist.save(options);
  }

  return wishlist;
};

const removeItem = async (accountId, item, options) => {
  const wishlist = await Wishlist.findOne({ accountId });
  if (!wishlist) {
    return new NotFoundError('Wishlist not found');
  }

  wishlist.items = wishlist.items.filter((i) => {
    if (item.comboId) {
      return !(i.comboId && i.comboId.toString() === item.comboId.toString());
    }
    return !(i.productId && i.productId.toString() === item.productId.toString());
  });

  await wishlist.save(options);
  return wishlist;
};

// Get distinct userIds of users whose wishlist contains productId
const findUserIdsByProduct = async (productId) => {
  const result = await Wishlist.aggregate([
    { $match: { 'items.productId': new mongoose.Types.ObjectId(productId) } },
    {
      $lookup: {
        from: 'ph_accounts',
        localField: 'accountId',
        foreignField: '_id',
        as: 'account',
      },
    },
    { $unwind: '$account' },
    { $project: { userId: '$account.userId' } },
  ]);
  // extract distinct ids
  const ids = [...new Set(result.map((r) => r.userId.toString()))];
  return ids;
};

export default { findByAccountId, create, addItem, removeItem, findUserIdsByProduct }; 