import { HttpStatusCode } from 'axios';
import { categoryService } from '../services/index.js';
import pick from '../utils/pick.js';
import { ROLES } from '../constants/role.js';

const search = async (req, res) => {
  const filters = pick(req.query, ['query']);

  // If the user is not an admin or employee, hide the products that are marked as hidden
  if (
    !req.user ||
    (!req.user.role.includes(ROLES.ADMIN) &&
      !req.user.role.includes(ROLES.EMPLOYEE))
  ) {
    filters.isHide = false;
  }

  const data = await categoryService.search(filters);

  if (data.status === HttpStatusCode.Ok) {
    return res.status(data.status).send(data.data);
  }
  return res.status(data.status).send(data);
};

const create = async (req, res) => {
  const data = await categoryService.create(req);
  // check unique name
  if (data.message?.status >= 400) {
    return res.status(data.message.status).send(data.message);
  }

  if (data.status === HttpStatusCode.Ok) {
    return res.status(data.status).send(data.data);
  }

  return res.status(data.status).send(data);
};

const update = async (req, res) => {
  const data = await categoryService.update(req);
  // check unique name
  if (data.message?.status >= 400) {
    return res.status(data.message.status).send(data.message);
  }
  
  if (data.status === HttpStatusCode.Ok) {
    return res.status(data.status).send(data.data);
  }
  return res.status(data.status).send(data);
};

const deleteCategory = async (req, res) => {
  const data = await categoryService.deleteCategory(req);

  if (data.status === HttpStatusCode.Ok) {
    return res.status(data.status).send(data.data);
  }
  return res.status(data.status).send(data);
};

const hasProductsInCategoryTree = async (req, res) => {
  const data = await categoryService.hasProductsInCategoryTree(req);
  if (data.status === HttpStatusCode.Ok) {
    return res.status(data.status).send(data.data);
  }
  return res.status(data.status).send(data.data);
};

export default {
  search,
  create,
  update,
  delete: deleteCategory,
  hasProductsInCategoryTree,
};
