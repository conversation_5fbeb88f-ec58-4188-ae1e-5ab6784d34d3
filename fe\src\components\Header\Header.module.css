.app-header {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 40px;
  background-color: var(--primary-color);
  color: var(--white-color);
  position: sticky;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000;
}

/* Logo */
.logo {
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-img, .logo-text {
  cursor: pointer;
}

.logo-img:hover, .logo-text:hover, .cartIconWrapper:hover {
  color: var(--text-light-color);
}

.menu {
  display: flex;
  justify-content: center;
  align-items: center;
}

.section-left {
  padding-right: 10px;
  display: flex;
  justify-content: center;
}

.section-right {
  display: flex;
}

.icon-label {
  font-size: 12px;
  text-align: center;
  margin-top: 4px;
  color: var(--white-color);
}


.logo-img {
  height: 40px;
  margin-right: 10px;
}

.logo-text {
  font-size: var(--font-size-lg);
  font-weight: 700;
}

.search-bar {
  display: flex;
  align-items: center;
  border-radius: 20px;
  padding: 5px;
  position: relative;
  width: 100%;
  max-width: 500px;
}

.search-input {
  border: none;
  outline: none;
  padding: 6px 10px 6px 10px;
  font-size: var(--font-size-md);
  width: 100%;
  border-radius: 20px;
  padding: 10px 15px;
}

.search-button {
  background: var(--white-color);
  border: none;
  padding: var(--spacing-sm) var(--spacing-md);
  color: var(--primary-color);
  cursor: pointer;
  border-radius: 20px;
  right: 0;
  margin: 0 var(--spacing-xs);
}

@media (max-width: 768px) {
  .search-bar {
    max-width: 300px;
    width: 100%;
  }
}

@media (max-width: 480px) {
  .search-bar {
    max-width: 200px;
    width: 100%;
  }
}



.header-actions {
  display: flex;
  align-items: center;
  justify-content: space-around;
  color: var(--white-color);
  gap: 20px;
  padding-left: 10px;
}

.cart-badge, .user-avatar {
  text-align: center;
  position: relative;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;

}

.cart-badge:hover, .cart-badge:hover .icon-label, .user-avatar:hover, .user-avatar:hover .icon-label {
  color: var(--text-light-color);
}


.divider {
  display: flex;
  justify-content: center;
  width: 1px;
  height: 100%;
  background-color: var(--text-light-color);
  margin: 0 10px;
}

.cart-icon, .user-avatar svg {
  font-size: 24px;
}

.cart-count {
  position: absolute;
  top: -5px;
  right: -10px;
  background: rgb(24, 22, 22);
  color: white;
  border-radius: 50%;
  padding: 1px 2px;
  font-size: 12px;
}

.icon-label {
  font-size: 12px;
  margin-top: 4px;
  color: var(--white-color);
}

@media (max-width: 768px) {
  .icon-label {
    display: none;
  }

  .logo-text {
    display: none;
  }
}

.category-menu {
  max-height: 400px;
  overflow-y: auto;
}

.Menu, .SubMenu {
  display: flex;
  justify-content: start;
  align-items: center;
  margin: 4px 0 !important;
  color: var(--primary-color) !important;
  padding: 0;
}

.Menu span {
  display: flex;
  justify-content: start;
  align-items: center;
}

.ant-dropdown-menu-submenu-title {
  display: flex;
  align-items: center;
}

.ant-dropdown-menu-title-content {
  display: flex;
  justify-content: start;
  align-items: center;
}

.header-home {
  min-height: 60px;
  display: flex;
  justify-content: center;
}



@media (min-width:768px) {
  .header-home {
    width: 750px;
  }
}

@media (min-width:992px) {
  .header-home {
    width: 970px;
  }
}

@media (min-width: 1280px) {
  .header-home {
    width: 1200px;
    margin: 0 auto;
  }
}

.menu-cate {
  display: flex;
  list-style-type: none;
  padding: 0;
  flex-wrap: wrap;
}

.menu-item {
  cursor: pointer;
  border-radius: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  max-width: 160px;
  width: 100%;
  margin: 0 4px;
  font-size: var(--font-size-lg);
  font-weight: 500;
  padding: 4px;
  border: 1px solid var(--primary-color);
  color: var(--primary-color);
  user-select: none;
  outline: none;
}

.menu-item:hover {
  background-color: var(--primary-color);
  color: var(--white-color);
}

.menu-item:focus {
  outline: none;
}

.logo-header {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Search suggestions */
.search-wrapper {
  position: relative;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.suggestions-list {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--white-color);
  border: 1px solid var(--primary-color);
  border-top: none;
  border-radius: 20px;
  max-height: 200px;
  overflow-y: auto;
  z-index: 1200;
  list-style: none;
  margin: 10px 0;
  padding: 5px;
}

.suggestion-item {
  padding: 8px 12px;
  cursor: pointer;
  font-size: var(--font-size-sm);
  color: var(--primary-color);
}

.suggestion-item:hover {
  background: #f5f5f5;
}

.delete-icon {
  float: right;
  color: #bbb;
}

.delete-icon:hover {
  color: #ff4d4f;
}