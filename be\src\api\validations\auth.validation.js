import Joi from 'joi';
import { password } from './custom.validation.js';
import { objectId } from '../helper/ObjectId.js';

const loginValidation = {
    body: Joi.object({
        email: Joi.string().email().required(),
        password: Joi.string().min(8).required(),
    })
};


const registerValidation = {
    body: Joi.object({
        email: Joi.string()
            .email({ tlds: { allow: false } })
            .required()
            .messages({
                "string.email": "Email must be a valid email address",
                "any.required": "Email is required"
            }),
        password: Joi.string().required().custom(password),
        fullName: Joi.string().required().min(6),
        address: Joi.string().optional(),
        phone: Joi.string()
            .pattern(/^0\d{8,10}$/)
            .optional()
    })
};

const editAccount = {
    body: Joi.object({
        accountId: Joi.string().required(),
        password: Joi.string().min(8).custom(password),
        status: Joi.object(),
        role: Joi.array().min(1),
    })
}

const logout = {
}

const forgotPassword = {
    body: Joi.object({
        email: Joi.string().required(),
    })
}

const resetPassword = {
    body: Joi.object({
        newPassword: Joi.string().required().custom(password),
        token: Joi.string().required(),
    })
}

const checkTokenExprided = {
    body: Joi.object({
        resetPWToken: Joi.string(),
        activeToken: Joi.string(),
    }).xor('resetPWToken', 'activeToken')
}

const changePassword = {
    body: Joi.object({
        password: Joi.string().required().custom(password),
        newPassword: Joi.string().required().custom(password),
    })
}

const getProfile = {
    params: Joi.object({
        accountId: objectId.required(),
    }),
}


const updateProfile = {
    params: Joi.object({
        accountId: objectId.required(),
    }),
    body: Joi.object({
        fullName: Joi.string().min(6).optional(),
        address: Joi.string().optional().allow(null, ''),
        phone: Joi.string()
            .pattern(/^0\d{8,10}$/)
            .optional()
            .allow(null, ''),
    })
}

export default { loginValidation, registerValidation, editAccount, logout, forgotPassword, resetPassword, checkTokenExprided, changePassword, getProfile, updateProfile };