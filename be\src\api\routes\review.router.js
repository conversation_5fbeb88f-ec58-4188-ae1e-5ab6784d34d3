import express from 'express';
import reviewController from '../controllers/review.controller.js';
import validate from '../middlewares/validates.js';
import reviewValidation from '../validations/review.validation.js';
import { verifyToken } from '../middlewares/authenticateJWT.js';

const router = express.Router();

router.post('/', verifyToken, validate(reviewValidation.createReview), reviewController.createReview);
router.get('/', reviewController.getReviews);
router.get('/:id', reviewController.getReviewById);
router.put('/:id', verifyToken, validate(reviewValidation.updateReview), reviewController.updateReview);
router.delete('/:id', verifyToken, reviewController.deleteReview);

export default router; 