import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import NotificationApi from '../../api/NotificationApi';

export const fetchNotifications = createAsyncThunk(
  'notifications/fetch',
  async ({ page = 1, pageSize = 10 } = {}) => {
    const res = await NotificationApi.list({ page, pageSize });
    return res;
  }
);

const notificationSlice = createSlice({
  name: 'notifications',
  initialState: {
    items: [],
    total: 0,
    unreadCount: 0,
  },
  reducers: {
    addNotification(state, action) {
      state.items = [action.payload, ...state.items];
      state.unreadCount += 1;
    },
    setUnreadCount(state, action) {
      state.unreadCount = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder.addCase(fetchNotifications.fulfilled, (state, action) => {
      state.items = action.payload.items;
      state.total = action.payload.total;
      state.unreadCount = action.payload.items.filter((i) => !i.isRead).length;
    });
  },
});

export const { addNotification, setUnreadCount } = notificationSlice.actions;
export default notificationSlice.reducer; 