version: '3.8'

services:
  mongo:
    image: mongo
    container_name: mongo-container
    restart: always
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=123456
    volumes:
      - mongo-data:/data/db

  backend:
    build:
      context: .
      dockerfile: Dockerfile.be  
    container_name: pethaven-backend
    restart: always
    depends_on:
      - mongo
    environment:
      - MONGO_URI=************************************************************
    ports:
      - "3000:3000"

volumes:
  mongo-data:
    driver: local
