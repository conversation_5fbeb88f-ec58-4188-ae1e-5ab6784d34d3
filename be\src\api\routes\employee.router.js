import express from 'express';
import { employeeController } from '../controllers/index.js';
import validate from '../middlewares/validates.js';
import employeeValidation from '../validations/employee.validation.js';
import { verifyToken } from '../middlewares/authenticateJWT.js';
import authorizeRoles from '../middlewares/authorizeRoles.js';
import { ROLES } from '../constants/role.js';

const employeeRouter = express.Router();

// List employees
employeeRouter.get(
  '/',
  verifyToken,
  authorizeRoles(ROLES.ADMIN),
  validate(employeeValidation.search),
  employeeController.search,
);

// Create new employee
employeeRouter.post(
  '/',
  verifyToken,
  authorizeRoles(ROLES.ADMIN),
  validate(employeeValidation.create),
  employeeController.create,
);

// Toggle block/unblock status
employeeRouter.patch(
  '/:accountId/status',
  verifyToken,
  authorizeRoles(ROLES.ADMIN),
  validate(employeeValidation.updateStatus),
  employeeController.toggleStatus,
);

// Update role(s)
employeeRouter.patch(
  '/:accountId/role',
  verifyToken,
  authorizeRoles(ROLES.ADMIN),
  validate(employeeValidation.updateRole),
  employeeController.updateRole,
);

// Update basic info
employeeRouter.patch(
  '/:accountId',
  verifyToken,
  authorizeRoles(ROLES.ADMIN),
  validate(employeeValidation.updateInfo),
  employeeController.updateInfo,
);

export default employeeRouter; 