import mongoose from 'mongoose';
import { MODELS, COLLECTIONS } from '../utils/constants.js';

// Define schema for an item inside wishlist
const wishlistItemSchema = new mongoose.Schema(
  {
    productId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: MODELS.PRODUCT,
      required: function() { return !this.comboId; },
    },
    comboId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: MODELS.COMBO,
      required: function() { return !this.productId; },
    },
  },
  { _id: false }
);

// Wishlist schema – one wishlist per account
const wishlistSchema = new mongoose.Schema(
  {
    accountId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: MODELS.ACCOUNT,
      required: true,
      unique: true,
    },
    items: { type: [wishlistItemSchema], default: [] },
  },
  {
    collection: COLLECTIONS.WISHLIST,
    timestamps: false,
    toJSON: {
      transform: function (doc, ret) {
        delete ret.__v;
        return ret;
      },
    },
    toObject: {
      transform: function (doc, ret) {
        delete ret.__v;
        return ret;
      },
    },
  }
);

const Wishlist = mongoose.model(MODELS.WISHLIST, wishlistSchema, COLLECTIONS.WISHLIST);

export default Wishlist; 