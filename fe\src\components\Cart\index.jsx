import React from "react";
import { Pop<PERSON>, <PERSON><PERSON>, Flex } from "antd";
import { ShoppingCartOutlined } from "@ant-design/icons";
import s from "./styles.module.css";
import { formatPrice } from "../../utils/formatPrice";

const Cart = ({ cartItems = [], onViewCart }) => {
    const content = (
        <div className={s.cartContainer}>
            <h3 className={s.title}>GIỎ HÀNG</h3>

            {cartItems && cartItems.length > 0 ? (
                <ul className={s.cartList} >
                    {cartItems?.slice(0, 3).map((item, index) => {
                        const isCombo = !!item.comboId;
                        const target = isCombo ? item.comboId : item.productId;
                        const optionIndexes = item?.variantId?.optionIndex || [];
                        const productOptions = target?.options || [];
                        const optionImage = isCombo ? (target?.thumbnail?.url || "https://placehold.co/100x100") : (item.variantId
                                ? (
                                    productOptions[0]?.values?.[item.variantId.optionIndex[0]]?.image?.url ||
                                    target?.thumbnail?.url ||
                                    "https://placehold.co/100x100"
                                )
                                : target?.thumbnail?.url || "https://placehold.co/100x100");
                        // Lấy danh sách các tùy chọn đã chọn
                        const selectedOptionsText = isCombo ? '' : optionIndexes
                            .map((optIdx, i) => {
                                if (productOptions[i] && productOptions[i].values[optIdx]) {
                                    return `${productOptions[i].name}: ${productOptions[i].values[optIdx].name}`;
                                }
                                return null;
                            })
                            .filter(Boolean)
                            .join(", ");
                        return (
                            <li key={index} className={s.cartItem}>
                                <img src={optionImage} alt={target?.name} className={s.itemImage} />
                                <div className={s.itemInfo}>
                                    <p className={s.itemName}>{target?.name}</p>
                                    <p className={s.optionValues}>{selectedOptionsText}</p>
                                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                        <p style={{ margin: 0 }}>Số lượng: {item.quantity}</p>
                                        <span className={s.itemPrice}>{isCombo ? formatPrice(target.discountPrice || target.price) : formatPrice(item.variantId?.price || target.price)}đ</span>
                                    </div>
                                </div>
                            </li>
                        );
                    })}
                    {cartItems.length > 3 && (
                        <li className={s.moreItems}>
                            <span>...</span>
                        </li>
                    )}

                </ul>
            ) : (
                <div className={s.emptyCart}>
                    <ShoppingCartOutlined className={s.cartIcon} />
                    <p>Hiện chưa có sản phẩm</p>
                </div>
            )}

            <div className={s.cartFooter}>
                <span className={s.totalLabel}>TỔNG TIỀN:</span>
                <span className={s.totalPrice}>
                    {cartItems?.length > 0
                        ? cartItems.reduce((total, item) => {
                            const target = item.comboId || item.productId;
                            const price = item?.variantId?.price || target?.discountPrice || target?.price || 0;
                            return total + (item?.quantity || 0) * price;
                        }, 0).toLocaleString()
                        : 0}đ
                </span>
            </div>

            <Button type="primary" block className={s.viewCartButton} onClick={onViewCart}>
                XEM GIỎ HÀNG
            </Button>
        </div>
    );

    return (
        <Popover content={content} trigger="click" placement="bottomRight">
            <div className={s.cartIconWrapper}>
                <ShoppingCartOutlined className={s.cartIcon} />
                {cartItems.length > 0 && <span className={s.cartCount}>{cartItems.length}</span>}
                <div className={s.iconLabel}>Giỏ hàng</div>
            </div>
        </Popover>
    );
};

export default Cart;
