import pagination from "../helper/pagination.js";
import Account from "../models/account.model.js";

const search = async (filters, options) => {
    const { page, pageSize, sort } = options;
    const { offset, limit } = pagination(page, pageSize);

    const sortCriteria = sort ? { price: sort } : { create_at: 1 };

    const rows = await Account.find(filters)
        .select("_id email status role")
        .sort(sortCriteria)
        .skip(offset)
        .limit(limit)
        .populate("userId");

    const processedRows = rows.map(row => {
        const user = row.userId;
        const { userId, ...rest } = row.toObject();
        return { ...rest, user };
    });

    const count = await Account.countDocuments(filters);
    return {
        count,
        rows: processedRows,
    };
};

const getByAccountId = async (accountId) => {
    return await Account.findOne({ _id: accountId });
}


const changeStatus = async (accountId, isBlocked, reasonBlock) => {
    const account = await getByAccountId(accountId);
    account.status.isBlocked = !isBlocked;
    account.status.reasonBlock = reasonBlock;
    account.save();
}


const update = async (accountId, data) => {
    try {
        const account = await Account.findById(accountId); // Sử dụng findById thay vì getByAccountId lặp lại
        if (!account) {
            throw new Error("Account không tồn tại");
        }

        // Cập nhật dữ liệu
        Object.assign(account, data); // Sử dụng Object.assign để cập nhật object
        const updatedAccount = await account.save();

        // Populate userId để trả về fullName nếu cần
        await updatedAccount.populate("userId", "fullName");

        return updatedAccount; // Trả về document đã cập nhật
    } catch (error) {
        throw new Error("Lỗi khi cập nhật account: " + error.message);
    }
};

const getByEmail = async (email) => {
    return await Account.findOne({ email: email });
}


export default { search, getByAccountId, changeStatus, update, getByEmail };