import mongoose from 'mongoose';
import { MODELS, COLLECTIONS } from '../utils/constants.js';

const cartItemSchema = new mongoose.Schema(
  {
    productId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: MODELS.PRODUCT,
      required: function() { return !this.comboId; }
    },
    comboId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: MODELS.COMBO,
      required: function() { return !this.productId; }
    },
    variantId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: MODELS.VARIANT,
    },
    quantity: {
      type: Number,
      required: true,
    },
  },
  {
    _id: false,
  }
);

const cartSchema = new mongoose.Schema(
  {
    accountId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: MODELS.ACCOUNT,
      required: true,
    },
    items: [cartItemSchema],
  },
  {
    collection: COLLECTIONS.CART,
    timestamps: false,
    toJSON: {
      transform: function (doc, ret) {
        delete ret.__v;
        // delete ret.createdAt;
        // delete ret.updatedAt;
        return ret;
      },
    },
    toObject: {
      transform: function (doc, ret) {
        delete ret.__v;
        // delete ret.createdAt;
        // delete ret.updatedAt;
        return ret;
      },
    },
  }
);

const Cart = mongoose.model(MODELS.CART, cartSchema, COLLECTIONS.CART);

export default Cart;
