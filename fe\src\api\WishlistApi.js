import AuthorApi from "./baseAPI/AuthorBaseApi";

class WishlistApi {
  constructor() {
    this.url = "/api/v1/wishlists";
  }

  getWishlist = async () => {
    return AuthorApi.get(`${this.url}`);
  };

  addToWishlist = async (body) => {
    return AuthorApi.post(`${this.url}`, body);
  };

  removeFromWishlist = async (body) => {
    // Axios delete with body uses { data: body }
    return AuthorApi.delete(`${this.url}`, { data: body });
  };
}

export default new WishlistApi(); 