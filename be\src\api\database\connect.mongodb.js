"use strict";

import mongoose from "mongoose";
import dotenv from "dotenv";
import config from "../config/config.js";
dotenv.config();

// Dòng này để kết nói cloud mongo

// const connectString = `mongodb+srv://${process.env.MONGO_CLOUD_USERNAME}:${process.env.MONGO_CLOUD_PASSWORD}@${process.env.MONGO_CLOUD_CLUTER_ADDRESS}/${process.env.MONGO_CLOUD_DB_NAME}?retryWrites=true&w=majority&tls=true&appName=${process.env.MONGO_CLOUD_APP_NAME}`;

// const connectString = `mongodb+srv://truongtanlocvuongmycam:<EMAIL>/WDP301?retryWrites=true&w=majority&tls=true&appName=WDP301`;

// Dòng này để kết nói local mongo
const connectString = config.dbUri;

// const dbName = connectString.split("/").pop().split("?")[0];


class Database {
  constructor() {
    this.connect();
  }

  //connect
  connect(type = "mongodb") {
    mongoose
      .connect(connectString, { maxPoolSize: 50 })
      .then(() => {
        console.log("Connected Mongodb Success");
        console.log("Connected:", connectString);
      })
      .catch((err) => {
        console.log("Error Connect: ", err);
      });
  }

  static getInstance() {
    if (!Database.instance) {
      Database.instance = new Database();
    }
    return Database.instance;
  }
}

const instanceMongoDb = Database.getInstance();

export default instanceMongoDb;
