import { HttpStatusCode } from 'axios';
import { notificationService } from '../services/index.js';
import pick from '../utils/pick.js';

const list = async (req, res) => {
  const { user } = req;
  if (!user) return res.status(HttpStatusCode.Unauthorized).send({ message: 'Unauthorized' });
  const options = pick(req.query, ['page', 'pageSize']);
  const data = await notificationService.list(user.userId || user.id || user._id, options);
  return res.status(data.status).send(data.data);
};

const markRead = async (req, res) => {
  const { user } = req;
  if (!user) return res.status(HttpStatusCode.Unauthorized).send({ message: 'Unauthorized' });
  const { id } = req.params;
  const data = await notificationService.markRead(user.userId || user.id || user._id, id);
  return res.status(data.status).send(data.data);
};

export default { list, markRead }; 