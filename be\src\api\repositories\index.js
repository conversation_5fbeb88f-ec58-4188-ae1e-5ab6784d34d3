import authRepository from './auth.repository.js';
import categoryRepository from './category.repository.js';
import productRepository from './product.repository.js';
import userRepository from './user.repository.js';
import variantRepository from './variant.repository.js';
import deliveryAddressRepository from './deliveryAddress.repository.js';
import orderRepository from './order.repository.js';
import cartRepository from './cart.repository.js';
import voucherRepository from './voucher.repository.js';
import wishlistRepository from './wishlist.repository.js';
import searchHistoryRepository from './searchHistory.repository.js';
import notificationRepository from './notification.repository.js';
import reviewRepository from './review.repository.js';
import commentRepository from './comment.repository.js';
import comboRepository from './combo.repository.js';
import serviceRepository from './service.repository.js';
export {
  authRepository,
  categoryRepository,
  productRepository,
  userRepository,
  variantRepository,
  deliveryAddressRepository,
  orderRepository,
  cartRepository,
  voucherRepository,
  wishlistRepository,
  searchHistoryRepository,
  notificationRepository,
  reviewRepository,
  commentRepository,
  comboRepository,
  serviceRepository,
};
