import { HttpStatusCode } from 'axios';
import <PERSON><PERSON> from 'joi';
import pick from 'lodash/pick.js';

const validate = (schema, options = {}) => (req, res, next) => {
  const validSchema = pick(schema, ['params', 'query', 'body']);
  const object = pick(req, Object.keys(validSchema));



  const { value, error } = Joi.compile(validSchema)
    .prefs({ errors: { label: 'key' }, abortEarly: false })
    .validate(object, options);

  if (error) {
    const errorMessage = error.details.map((details) => details.message).join(', ');

    return res.status(HttpStatusCode.BadRequest).json({
      status: HttpStatusCode.BadRequest,
      message: errorMessage,
    });

  }

  Object.assign(req, value);
  next();
};

export default validate;