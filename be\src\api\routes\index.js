import express from 'express';
import authRouter from './auth.router.js';
import categoryRouter from './category.router.js';
import productRouter from './product.router.js';
import userRouter from './user.router.js';
import deliveryAddressRouter from './deliveryAddress.router.js';
import orderRouter from './order.router.js';
import uploadRouter from './upload.router.js';
import cartRouter from './cart.router.js';
import voucherRouter from './voucher.router.js';
import wishlistRouter from './wishlist.router.js';
import searchHistoryRouter from './searchHistory.router.js';
import notificationRouter from './notification.router.js';
import reviewRouter from './review.router.js';
import commentRouter from './comment.router.js';
import employeeRouter from './employee.router.js';
import comboRouter from './combo.router.js';
import serviceRouter from './service.router.js';

const router = express.Router();
router.use('/auth', authRouter);
router.use('/categories', categoryRouter);
router.use('/products', productRouter);
router.use('/users', userRouter);
router.use('/deliveryAddress', deliveryAddressRouter);
router.use('/order', orderRouter);
router.use('/upload', uploadRouter);
router.use('/carts', cartRouter);
router.use('/vouchers', voucherRouter);
router.use('/wishlists', wishlistRouter);
router.use('/search-histories', searchHistoryRouter);
router.use('/notifications', notificationRouter);
router.use('/reviews', reviewRouter);
router.use('/comments', commentRouter);
router.use('/employees', employeeRouter);
router.use('/combos', comboRouter);
router.use('/services', serviceRouter);

export default router;
