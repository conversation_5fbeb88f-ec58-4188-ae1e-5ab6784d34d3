import UnauthorApi from './baseAPI/UnauthorBaseApi.js';
import AuthorApi from './baseAPI/AuthorBaseApi.js';

class ReviewApi {
  constructor() {
    this.url = '/api/v1/reviews';
  }

  getReviews = (params) => {
    return UnauthorApi.get(this.url, { params });
  };

  createReview = (data) => {
    return AuthorApi.post(this.url, data);
  };

  updateReview = (id, data) => {
    return AuthorApi.put(`${this.url}/${id}`, data);
  };

  deleteReview = (id) => {
    return AuthorApi.delete(`${this.url}/${id}`);
  };
}

export default new ReviewApi(); 