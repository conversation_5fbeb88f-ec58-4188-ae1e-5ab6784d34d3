import mongoose from 'mongoose';
import { MODELS, COLLECTIONS, VOUCHER_STATUS, VOUCHER_TYPE } from '../utils/constants.js';

const voucherConditionSchema = new mongoose.Schema(
  {
    minOrderValue: { type: Number, default: 0 },
    productIds: [{ type: mongoose.Schema.Types.ObjectId, ref: MODELS.PRODUCT }],
    usageLimit: { type: Number, default: 0 }, // 0 = unlimited
    startDate: { type: Date },
    endDate: { type: Date },
  },
  { _id: false }
);

const voucherSchema = new mongoose.Schema(
  {
    code: {
      type: String,
      required: true,
      unique: true,
      trim: true,
      uppercase: true,
    },
    type: {
      type: String,
      enum: Object.values(VOUCHER_TYPE),
      required: true,
    },
    value: {
      type: Number,
      required: true,
      min: 1,
    },
    conditions: {
      type: voucherConditionSchema,
      default: {},
    },
    usedCount: {
      type: Number,
      default: 0,
    },
    status: {
      type: String,
      enum: Object.values(VOUCHER_STATUS),
      default: VOUCHER_STATUS.ACTIVE,
    },
    isDeleted: {
      type: Boolean,
      default: false,
    },
  },
  {
    collection: COLLECTIONS.VOUCHER,
    timestamps: true,
    toJSON: {
      transform: function (doc, ret) {
        delete ret.__v;
        return ret;
      },
    },
    toObject: {
      transform: function (doc, ret) {
        delete ret.__v;
        return ret;
      },
    },
  }
);

// Unique code only for vouchers not deleted
voucherSchema.index(
  { code: 1 },
  {
    unique: true,
    partialFilterExpression: { isDeleted: false },
  }
);
voucherSchema.index({ status: 1 });
voucherSchema.index({ 'conditions.endDate': 1 });

const Voucher = mongoose.model(MODELS.VOUCHER, voucherSchema, COLLECTIONS.VOUCHER);

export default Voucher; 