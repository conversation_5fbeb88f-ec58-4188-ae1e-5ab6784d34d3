import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import Wishlist<PERSON>pi from "../../api/WishlistApi";
import { toast } from "react-toastify";

const initialState = {
  wishlistItems: [],
  status: "idle",
  error: null,
  loading: false,
};

export const fetchWishlist = createAsyncThunk(
  "wishlist/fetchWishlist",
  async (_, { rejectWithValue }) => {
    try {
      const response = await WishlistApi.getWishlist();
      // Response format: { _id, accountId, items: [...] }
      return response.data.items || [];
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const addToWishlist = createAsyncThunk(
  "wishlist/addToWishlist",
  async ({ item }, { rejectWithValue }) => {
    try {
      const response = await WishlistApi.addToWishlist(item);
      toast.success("Đã thêm vào danh sách yêu thích!");
      // Response format: { _id, accountId, items: [...] }
      return response.data.items || [];
    } catch (error) {
      toast.error("Không thể thêm vào danh sách yêu thích");
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const removeFromWishlist = createAsyncThunk(
  "wishlist/removeFromWishlist",
  async ({ item }, { rejectWithValue }) => {
    try {
      const response = await WishlistApi.removeFromWishlist(item);
      toast.success("Đã xóa khỏi danh sách yêu thích!");
      // Response format: { _id, accountId, items: [...] }
      return response.data.items || [];
    } catch (error) {
      toast.error("Không thể xóa khỏi danh sách yêu thích");
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

const wishlistSlice = createSlice({
  name: "wishlist",
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setLoading: (state, action) => {
      state.loading = action.payload;
    },
    // Thêm reducer để cập nhật state ngay lập tức
    updateWishlistItems: (state, action) => {
      state.wishlistItems = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchWishlist.pending, (state) => {
        state.status = "loading";
        state.loading = true;
      })
      .addCase(fetchWishlist.fulfilled, (state, action) => {
        state.status = "succeeded";
        state.wishlistItems = action.payload;
        state.loading = false;
        state.error = null;
      })
      .addCase(fetchWishlist.rejected, (state, action) => {
        state.status = "failed";
        state.error = action.payload;
        state.loading = false;
      })

      .addCase(addToWishlist.pending, (state) => {
        state.status = "loading";
        state.loading = true;
      })
      .addCase(addToWishlist.fulfilled, (state, action) => {
        state.status = "succeeded";
        state.wishlistItems = action.payload;
        state.loading = false;
        state.error = null;
      })
      .addCase(addToWishlist.rejected, (state, action) => {
        state.status = "failed";
        state.error = action.payload;
        state.loading = false;
      })

      .addCase(removeFromWishlist.pending, (state) => {
        state.status = "loading";
        state.loading = true;
      })
      .addCase(removeFromWishlist.fulfilled, (state, action) => {
        state.wishlistItems = action.payload;
        state.status = "succeeded";
        state.loading = false;
        state.error = null;
      })
      .addCase(removeFromWishlist.rejected, (state, action) => {
        state.status = "failed";
        state.error = action.payload;
        state.loading = false;
      });
  },
});

export const { clearError, setLoading, updateWishlistItems } = wishlistSlice.actions;
export default wishlistSlice.reducer; 