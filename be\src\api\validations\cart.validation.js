import Joi from 'joi';
import mongoose from 'mongoose';

const objectId = Joi.string().custom((value, helpers) => {
  if (!mongoose.Types.ObjectId.isValid(value)) {
    return helpers.message('Invalid Object ID');
  }
  return value;
});

const quantitySchema = Joi.number().integer().min(1).max(500).required().messages({
  'any.required': 'quantity is required',
  'number.base': 'quantity must be a number',
  'number.integer': 'quantity must be an integer',
  'number.min': 'quantity must be at least 1',
  'number.max': 'quantity must be at most 100',
});

const addCartItem = {
  body: Joi.alternatives().try(
    Joi.object({
      productId: objectId.required(),
      variantId: objectId.allow(null),
      quantity: quantitySchema,
    }),
    Joi.object({
      comboId: objectId.required(),
      quantity: quantitySchema,
    })
  ),
};

const updateCartItem = {
  body: Joi.alternatives().try(
    Joi.object({
      productId: objectId.required().messages({
        'any.required': 'Please enter productId.',
      }),
      variantId: objectId.optional(),
      quantity: Joi.number().integer().min(0).required().messages({
        'any.required': 'quantity is required',
        'number.base': 'quantity must be a number',
        'number.integer': 'quantity must be an integer',
        'number.min': 'quantity must be at least 0',
        'number.max': 'quantity must be at most 100',
      }),
    }),
    Joi.object({
      comboId: objectId.required().messages({
        'any.required': 'Please enter comboId.',
      }),
      quantity: Joi.number().integer().min(0).required().messages({
        'any.required': 'quantity is required',
        'number.base': 'quantity must be a number',
        'number.integer': 'quantity must be an integer',
        'number.min': 'quantity must be at least 0',
        'number.max': 'quantity must be at most 100',
      }),
    })
  ),
};

const updateCartItemVariant = {
  body: Joi.object({
    productId: objectId.required().messages({
      'any.required': 'Please enter productId.',
    }),
    oldVariantId: objectId.required().messages({
      'any.required': 'Please enter oldVariantId.',
    }),
    newVariantId: objectId.required().messages({
      'any.required': 'Please enter newVariantId.',
    }),
  }),
};

const deleteCartItems = {
  body: Joi.object({
    items: Joi.array()
      .items(
        Joi.alternatives().try(
          Joi.object({
            productId: objectId.required().messages({
              'any.required': 'Please enter productId.',
            }),
            variantId: objectId.optional(),
          }),
          Joi.object({
            comboId: objectId.required().messages({
              'any.required': 'Please enter comboId.',
            }),
          })
        )
      )
      .min(1)
      .required(),
  }),
};

export default {
  addCartItem,
  updateCartItem,
  updateCartItemVariant,
  deleteCartItems,
};
