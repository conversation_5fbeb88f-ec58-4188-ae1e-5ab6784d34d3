import express from 'express';
import { searchController } from '../controllers/index.js';
import { verifyToken } from '../middlewares/authenticateJWT.js';

const searchHistoryRouter = express.Router();

searchHistoryRouter.get('/', verifyToken, searchController.list);
searchHistoryRouter.delete('/:id', verifyToken, searchController.deleteOne);
searchHistoryRouter.delete('/', verifyToken, searchController.deleteAll);

export default searchHistoryRouter; 