import React, { useCallback, useEffect, useState } from "react";
import { Table, Space, Tag, Input, Button, Modal, Form, message, Tooltip } from "antd";
import { PlusOutlined, InfoCircleOutlined } from "@ant-design/icons";
import EmployeeApi from "../../../api/EmployeeApi";
import withAuthentication from "../../../hoc/withAuthentication";
import withAuthorization from "../../../hoc/withAuthorization";
import * as constants from "../../../constants/index.js";

const { Search } = Input;

const ManageEmployees = () => {
  const [employees, setEmployees] = useState([]);
  const [pagination, setPagination] = useState({ current: 1, pageSize: 10, total: 0 });
  const [loading, setLoading] = useState(false);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [createForm] = Form.useForm();
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editForm] = Form.useForm();
  const [editingRecord, setEditingRecord] = useState(null);

  const fetchEmployees = useCallback(async (params = {}) => {
    setLoading(true);
    try {
      const { page = 1, pageSize = 10, search } = params;
      const apiParams = { page, pageSize, ...(search && { search }) };
      const res = await EmployeeApi.getEmployees(apiParams);
      const { rows, total } = res.data.data || res.data; // service returns ResponseDataSuccess
      setEmployees(rows);
      setPagination({ current: page, pageSize, total });
    } catch (err) {
      console.error(err);
      message.error("Không thể lấy dữ liệu nhân viên");
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchEmployees({ page: 1, pageSize: 10 });
  }, [fetchEmployees]);

  const handleSearch = (value) => {
    fetchEmployees({ page: 1, pageSize: pagination.pageSize, search: value.trim() });
  };

  const handleTableChange = (paginationConfig) => {
    fetchEmployees({ page: paginationConfig.current, pageSize: paginationConfig.pageSize });
  };

  const openCreateModal = () => {
    setIsCreateModalOpen(true);
    createForm.resetFields();
  };

  const handleCreate = async () => {
    try {
      const values = await createForm.validateFields();
      setLoading(true);
      await EmployeeApi.createEmployee(values);
      message.success("Tạo nhân viên thành công");
      setIsCreateModalOpen(false);
      fetchEmployees({ page: pagination.current, pageSize: pagination.pageSize });
    } catch (err) {
      if (err?.response?.data?.message) message.error(err.response.data.message);
      else if (err.errorFields) {
        // validation error ignore
      } else {
        message.error("Tạo nhân viên thất bại");
      }
    } finally {
      setLoading(false);
    }
  };

  const openEditModal = (record) => {
    setEditingRecord(record);
    editForm.setFieldsValue({
      fullName: record.user?.fullName,
      email: record.email,
      phone: record.user?.phone || "",
      address: record.user?.address || "",
    });
    setIsEditModalOpen(true);
  };

  const handleEdit = async () => {
    try {
      const values = await editForm.validateFields();
      setLoading(true);
      await EmployeeApi.updateInfo(editingRecord._id, values);
      message.success("Cập nhật thành công");
      setIsEditModalOpen(false);
      fetchEmployees({ page: pagination.current, pageSize: pagination.pageSize });
    } catch (err) {
      if (err?.response?.data?.message) message.error(err.response.data.message);
      else message.error("Cập nhật thất bại");
    } finally {
      setLoading(false);
    }
  };

  const columns = [
    {
      title: "Họ tên",
      dataIndex: ["user", "fullName"],
      key: "fullName",
    },
    {
      title: "Email",
      dataIndex: "email",
      key: "email",
    },
    {
      title: "Trạng thái",
      dataIndex: ["status", "isBlocked"],
      key: "status",
      render: (blocked) => (
        <Tag color={blocked ? "red" : "green"}>{blocked ? "Bị chặn" : "Hoạt động"}</Tag>
      ),
    },
    {
      title: "Hành động",
      key: "action",
      render: (_, record) => (
        <Space>
          <Button
            type={record.status.isBlocked ? "primary" : "default"}
            danger={!record.status.isBlocked}
            onClick={() => toggleStatus(record)}
          >
            {record.status.isBlocked ? "Kích hoạt" : "Chặn"}
          </Button>
          <Button onClick={() => openEditModal(record)}>Sửa</Button>
        </Space>
      ),
    },
  ];

  const toggleStatus = async (record) => {
    setLoading(true);
    try {
      await EmployeeApi.toggleStatus(record._id, record.status.isBlocked ? "" : "Admin block");
      message.success("Cập nhật trạng thái thành công");
      fetchEmployees({ page: pagination.current, pageSize: pagination.pageSize });
    } catch (err) {
      message.error("Cập nhật trạng thái thất bại");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <h1>Quản lý nhân viên</h1>
      <Space style={{ marginBottom: 16 }}>
        <Tooltip title="Tìm kiếm theo tên hoặc email">
          <Search 
            placeholder="Nhập tên hoặc email nhân viên..." 
            onSearch={handleSearch} 
            allowClear
            style={{ width: 300 }}
            suffix={
              <InfoCircleOutlined style={{ color: 'rgba(0,0,0,.45)' }} />
            }
          />
        </Tooltip>
        <Button type="primary" icon={<PlusOutlined />} onClick={openCreateModal}>
          Thêm nhân viên
        </Button>
      </Space>
      <Table
        columns={columns}
        dataSource={employees}
        loading={loading}
        onChange={handleTableChange}
        pagination={{
          current: pagination.current,
          pageSize: pagination.pageSize,
          total: pagination.total,
        }}
        rowKey="_id"
      />

      <Modal
        title="Thêm nhân viên mới"
        open={isCreateModalOpen}
        onOk={handleCreate}
        onCancel={() => setIsCreateModalOpen(false)}
        okText="Tạo"
        confirmLoading={loading}
      >
        <Form form={createForm} layout="vertical">
          <Form.Item label="Họ tên" name="fullName" rules={[{ required: true, message: "Nhập họ tên" }]}> 
            <Input />
          </Form.Item>
          <Form.Item label="Email" name="email" rules={[{ required: true, type: "email", message: "Nhập email hợp lệ" }]}> 
            <Input />
          </Form.Item>
          <Form.Item label="Mật khẩu" name="password" rules={[{ required: true, min: 6, message: "Tối thiểu 6 ký tự" }]}> 
            <Input.Password />
          </Form.Item>
          <Form.Item label="Số điện thoại" name="phone" rules={[{ pattern: /^0\d{8,10}$/, message: "Số điện thoại không hợp lệ" }]}> 
            <Input />
          </Form.Item>
          <Form.Item label="Địa chỉ" name="address">
            <Input />
          </Form.Item>
        </Form>
      </Modal>

      <Modal
        title="Chỉnh sửa thông tin nhân viên"
        open={isEditModalOpen}
        onOk={handleEdit}
        onCancel={() => setIsEditModalOpen(false)}
        okText="Lưu"
        confirmLoading={loading}
      >
        <Form form={editForm} layout="vertical">
          <Form.Item label="Họ tên" name="fullName" rules={[{ required: true, message: "Nhập họ tên" }]}> 
            <Input />
          </Form.Item>
          <Form.Item label="Email" name="email" rules={[{ required: true, type: "email", message: "Nhập email hợp lệ" }]}> 
            <Input />
          </Form.Item>
          <Form.Item label="Số điện thoại" name="phone" rules={[{ pattern: /^0\d{8,10}$/, message: "Số điện thoại không hợp lệ" }]}> 
            <Input />
          </Form.Item>
          <Form.Item label="Địa chỉ" name="address">
            <Input />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default withAuthentication(withAuthorization([constants.userRoles.ROLE_ADMIN])(ManageEmployees)); 