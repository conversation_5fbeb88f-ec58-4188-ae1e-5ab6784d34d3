.checkoutContainer {
    max-width: 80%;
    margin: 0 auto;
    padding: 50px 0;
}


.checkoutTitle {
    text-align: start;
}

.paymentMethod,
.addressInfo,
.productList {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.paymentMethodCard,
.addressInfoCard,
.orderInfoCard {
    padding: 20px;
    margin-top: 24px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}



.productDetails {
    padding: 20px 0;
}


.productName {
    font-size: 1.2rem;
    font-weight: bold;
    padding-bottom: 10px;
    color: #333;
    margin: 0;
    color: var(--text-light-color);

}

.productPrice {
    /* font-size: 1rem;
    color: #666; */
    margin: 0;
}

.orderItem {
    display: flex;
    align-items: center;
    /* padding: 12px 0; */
    border-bottom: 1px solid #e0e0e0;
}

.productImage {
    width: 100px;
    height: 100px;
    border-radius: 8px;
    object-fit: cover;
}

.largeText {
    font-size: 18px;
    font-weight: bold;
}

.addressContainer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.changeAddressButton {
    margin-left: 20px;
}


.addressModal {
    max-width: 500px;
}

.addressList {
    max-height: 400px;
    overflow-y: auto;
}

.addressCard {
    margin-bottom: 10px;
    cursor: pointer;
    position: relative;
    border: 1px solid #ddd;
    transition: border-color 0.3s;
}

.addressCard:hover {
    background-color: #f0f0f0;
}

.defaultAddress {
    /* border: 2px solid #4CAF50;  */
    /* background-color: #e8f5e9;  */
}

.defaultTag {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: #4CAF50;
    color: white;
    padding: 5px 10px;
    font-size: 12px;
    border-radius: 5px;
}

.selectedAddress {
    border: 2px solid #007bff;
    /* Màu xanh dương cho địa chỉ đang chọn */
}

.selectedAddress:hover {
    border-color: #0056b3;
    /* Đổi màu khi hover địa chỉ đang chọn */
}


.editButton {
    color: #1890ff;
    /* Màu sắc cho nút Chỉnh sửa */
    text-decoration: none;
    font-size: 14px;
    padding: 0;
}

.paymentSummary {
    margin-top: 20px;
}

.paymentSummaryCard {
    padding: 20px;
    background-color: #fff;
}

.sectionTitle {
    font-size: 18px;
    margin-bottom: 10px;
}

.summaryItems {
    margin-bottom: 20px;
}

.summaryRow {
    display: flex;
    justify-content: space-between;
    margin: 5px 0;
}

.amount {
    text-align: right;
    font-weight: bold;
    color: #e74c3c;
    /* Hoặc màu sắc khác bạn muốn */
}



.paymentMethodCard {
    border-radius: 10px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.sectionTitle {
    font-size: 1.5rem;
    font-weight: bold;
    color: #333;
    margin: 15px 0;
    /* justify-content: center; */
}

.paymentOptions {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.option {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    background-color: #f1f1f1;
    border: 1px solid #ddd;
}

.option:hover {
    background-color: #e6f7ff;
    border-color: #1890ff;
}

.option input[type="radio"] {
    width: 18px;
    height: 18px;
    cursor: pointer;
}

.icon {
    width: 20px;
    height: 20px;
    color: #1890ff;
}

.option span {
    font-size: 1rem;
    color: #555;
    font-weight: 500;
}

.option:hover .icon {
    color: #1890ff;
}

.completeOrderButtonContainer {
    display: flex;
    justify-content: end;
    align-items: end;
}

.completeOrderButton {
    justify-content: end;
    align-items: end;
    width: 20%;
    padding: 15px;
    margin-top: 20px;
    font-size: 1.2rem;
    font-weight: bold;
    text-align: center;
    background-color: #1890ff;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.completeOrderButton:hover {
    background-color: #40a9ff;
}

/* Các style hiện có giữ nguyên, chỉ thêm phần mới dưới đây */
.noAddressContainer {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
    padding: 20px;
    text-align: center;
}

.noAddressContainer p {
    font-size: 16px;
    color: #666;
    margin: 0;
}

.voucherInput {
    width: 60%;
    padding: 6px 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
    margin-right: 8px;
}

.applyVoucherButton {
    padding: 6px 16px;
}