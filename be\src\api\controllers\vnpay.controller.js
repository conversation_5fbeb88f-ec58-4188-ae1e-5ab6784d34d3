import { vnpayService } from '../services/index.js';

const handleReturn = (req, res) => {
  const vnp_Params = req.query;
  // Kiểm tra chữ ký
  const isSignatureValid = vnpayService.verifyReturn(vnp_Params);

  if (isSignatureValid) {
    // Chữ ký hợp lệ, kiểm tra thêm dữ liệu trong DB nếu cần
    const responseCode = vnp_Params['vnp_ResponseCode'];
    res.render('success.jade', { code: responseCode });
  } else {
    // Chữ ký không hợp lệ
    res.render('success', { code: '97' });
  }
};

const vnpayIPN = async (req, res) => {
  const vnp_Params = req.query;

  // 🔹 Kiểm tra checksum
  if (!vnpayService.verifyChecksum(vnp_Params)) {
    return res.status(400).json({ RspCode: '97', Message: '<PERSON>su<PERSON> failed' });
  }

  // 🔹 Xử lý thanh toán
  const result = await vnpayService.processPayment(vnp_Params);
  return res.status(200).json(result);
};

const queryDR = async (req, res) => {
  try {
    const { orderId, transDate } = req.body;

    const ipAddr =
      req.headers['x-forwarded-for'] ||
      req.connection.remoteAddress ||
      req.socket.remoteAddress ||
      req.connection.socket?.remoteAddress;

    if (!orderId || !transDate) {
      return res
        .status(400)
        .json({ message: 'Thiếu thông tin orderId hoặc transDate' });
    }

    const result = await vnpayService.queryTransaction(
      orderId,
      transDate,
      ipAddr
    );
    res.status(200).json(result);
  } catch (error) {
    res
      .status(500)
      .json({ message: 'Lỗi truy vấn giao dịch', error: error.message });
  }
};

const refund = async (req, res) => {
  try {
    const { orderId, transDate, amount, transType, user } = req.body;

    const ipAddr =
      req.headers['x-forwarded-for'] ||
      req.connection.remoteAddress ||
      req.socket.remoteAddress ||
      req.connection.socket?.remoteAddress;

    if (!orderId || !transDate || !amount || !transType || !user) {
      return res.status(400).json({ message: 'Thiếu thông tin cần thiết' });
    }

    const result = await vnpayService.refundTransaction(
      orderId,
      transDate,
      amount,
      transType,
      user,
      ipAddr
    );
    res.status(200).json(result);
  } catch (error) {
    res
      .status(500)
      .json({ message: 'Lỗi hoàn tiền giao dịch', error: error.message });
  }
};

const createPayment = async (req, res) => {
  try {
    const { amount, bankCode, language } = req.body;

    const ipAddr =
      req.headers['x-forwarded-for'] ||
      req.connection.remoteAddress ||
      req.socket.remoteAddress ||
      req.connection.socket?.remoteAddress;

    if (!amount) {
      return res.status(400).json({ message: 'Thiếu số tiền thanh toán' });
    }

    const paymentUrl = vnpayService.createPaymentUrl(
      amount,
      bankCode,
      language,
      ipAddr
    );
    res.status(200).json({ paymentUrl });
  } catch (error) {
    res
      .status(500)
      .json({ message: 'Lỗi tạo URL thanh toán', error: error.message });
  }
};

export default { handleReturn, vnpayIPN, queryDR, refund, createPayment };
