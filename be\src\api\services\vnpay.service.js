import QueryString from "qs";
import sortObject from "../helper/sortObject.js";
import crypto from "crypto";
import moment from 'moment/moment.js';
import config from '../config/config.js';


const verifyReturn = (vnp_Params) => {
    const secureHash = vnp_Params['vnp_SecureHash'];

    // Loại bỏ các trường không cần thiết để tính toán chữ ký
    delete vnp_Params['vnp_SecureHash'];
    delete vnp_Params['vnp_SecureHashType'];

    // Sắp xếp các tham số theo thứ tự alphabet
    const sortedParams = sortObject(vnp_Params);

    // Tạo chuỗi dữ liệu để ký
    const signData = QueryString.stringify(sortedParams, { encode: false });

    // Tạo chữ ký HMAC-SHA512
    const hmac = crypto.createHmac("sha512", config.vnp_HashSecret);
    const signed = hmac.update(Buffer.from(signData, 'utf-8')).digest("hex");

    // So sánh chữ ký
    return secureHash === signed;
}


const verifyChecksum = (vnp_Params) => {
    let secureHash = vnp_Params['vnp_SecureHash'];
    delete vnp_Params['vnp_SecureHash'];
    delete vnp_Params['vnp_SecureHashType'];

    let sortedParams = Object.fromEntries(Object.entries(vnp_Params).sort());
    let signData = QueryString.stringify(sortedParams, { encode: false });

    let hmac = crypto.createHmac('sha512', config.vnp_HashSecret);
    let signed = hmac.update(Buffer.from(signData, 'utf-8')).digest('hex');

    return secureHash === signed;
};

const processPayment = async (vnp_Params) => {
    let orderId = vnp_Params['vnp_TxnRef'];
    let rspCode = vnp_Params['vnp_ResponseCode'];

    // 🔹 Kiểm tra đơn hàng có tồn tại không
    let order = await findOrderById(orderId);
    if (!order) {
        return { RspCode: '01', Message: 'Order not found' };
    }

    // 🔹 Kiểm tra số tiền
    let orderAmount = order.amount;
    let vnpAmount = parseInt(vnp_Params['vnp_Amount']) / 100;
    if (orderAmount !== vnpAmount) {
        return { RspCode: '04', Message: 'Amount invalid' };
    }

    // 🔹 Kiểm tra trạng thái giao dịch
    if (order.status !== 'PENDING') {
        return { RspCode: '02', Message: 'This order has been updated' };
    }

    // 🔹 Cập nhật trạng thái đơn hàng
    if (rspCode === '00') {
        await updateOrderStatus(orderId, 'PAID');
        return { RspCode: '00', Message: 'Success' };
    } else {
        await updateOrderStatus(orderId, 'FAILED');
        return { RspCode: '00', Message: 'Payment failed' };
    }
};

const queryTransaction = async (orderId, transDate, ipAddr) => {
    try {
        let date = new Date();

        let vnp_TmnCode = config.vnp_TmnCode;
        let secretKey = config.vnp_HashSecret;
        let vnp_Api = config.vnp_Api;

        let vnp_TxnRef = orderId;
        let vnp_TransactionDate = transDate;

        let vnp_IpAddr = ipAddr;

        let vnp_CreateDate = moment(date).format('YYYYMMDDHHmmss');
        let vnp_RequestId = moment(date).format('HHmmss');
        let vnp_Version = '2.1.0';
        let vnp_Command = 'querydr';
        let vnp_OrderInfo = 'Truy vấn GD mã: ' + vnp_TxnRef;

        let data = `${vnp_RequestId}|${vnp_Version}|${vnp_Command}|${vnp_TmnCode}|${orderId}|${transDate}|${vnp_CreateDate}|${ipAddr}|${vnp_OrderInfo}`;

        let hmac = crypto.createHmac('sha512', secretKey);
        let vnp_SecureHash = hmac.update(Buffer.from(data, 'utf-8')).digest('hex');

        let requestData = {
            vnp_RequestId,
            vnp_Version,
            vnp_Command,
            vnp_TmnCode,
            vnp_TxnRef,
            vnp_OrderInfo,
            vnp_TransactionDate,
            vnp_CreateDate,
            vnp_IpAddr,
            vnp_SecureHash
        };

        let response = await fetch(vnp_Api, {
            method: 'POST',
            headers: {
            'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        });

        return response.json();
    } catch (error) {
        console.error('Lỗi khi truy vấn giao dịch:', error);
        throw error;
    }
};


const refundTransaction = async (orderId, transDate, amount, transType, user, ipAddr) => {
    try {
        let date = new Date();

        let vnp_TmnCode = config.vnp_TmnCode;
        let secretKey = config.vnp_HashSecret;
        let vnp_Api = config.vnp_Api;

        let vnp_TxnRef = orderId;
        let vnp_TransactionDate = transDate;
        let vnp_Amount = amount *100;
        let vnp_TransactionType = transType;
        let vnp_CreateBy = user;

        let vnp_RequestId = moment(date).format('HHmmss');
        let vnp_Version = '2.1.0';
        let vnp_Command = 'refund';
        let vnp_OrderInfo = 'Hoàn tiền GD mã: ' + vnp_TxnRef;
        
        let vnp_IpAddr = ipAddr;

        let vnp_CreateDate = moment(date).format('YYYYMMDDHHmmss');

        let vnp_TransactionNo = '0';

        let data = `${vnp_RequestId}|${vnp_Version}|${vnp_Command}|${vnp_TmnCode}|${transType}|${orderId}|${vnp_Amount}|${vnp_TransactionNo}|${transDate}|${user}|${vnp_CreateDate}|${ipAddr}|${vnp_OrderInfo}`;

        let hmac = crypto.createHmac('sha512', secretKey);
        let vnp_SecureHash = hmac.update(Buffer.from(data, 'utf-8')).digest('hex');

        let requestData = {
            vnp_RequestId,
            vnp_Version,
            vnp_Command,
            vnp_TmnCode,
            vnp_TransactionType,
            vnp_TxnRef,
            vnp_Amount,
            vnp_TransactionNo,
            vnp_CreateBy,
            vnp_OrderInfo,
            vnp_TransactionDate,
            vnp_CreateDate,
            vnp_IpAddr,
            vnp_SecureHash
        };

        let response = await fetch(vnp_Api, {
            method: 'POST',
            headers: {
            'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        });

        return response.json();
    } catch (error) {
        console.error('Lỗi khi hoàn tiền giao dịch:', error);
        throw error;
    }
};


const createPaymentUrl = (amount, bankCode, language, ipAddr) => {
    try {
        let date = new Date();
        let createDate = moment(date).format('YYYYMMDDHHmmss');
        let orderId = moment(date).format('DDHHmmss');

        let tmnCode = config.vnp_TmnCode;
        let secretKey = config.vnp_HashSecret;
        let vnpUrl = config.vnp_Url;
        let returnUrl = config.vnp_ReturnUrl;

        let locale = language || 'vn';
        let currCode = 'VND';

        let vnp_Params = {
            'vnp_Version': '2.1.0',
            'vnp_Command': 'pay',
            'vnp_TmnCode': tmnCode,
            'vnp_Locale': locale,
            'vnp_CurrCode': currCode,
            'vnp_TxnRef': orderId,
            'vnp_OrderInfo': 'Thanh toán cho mã GD: ' + orderId,
            'vnp_OrderType': 'other',
            'vnp_Amount': amount * 100,
            'vnp_ReturnUrl': returnUrl,
            'vnp_IpAddr': ipAddr,
            'vnp_CreateDate': createDate
        };

        if (bankCode) {
            vnp_Params['vnp_BankCode'] = bankCode;
        }

        vnp_Params = sortObject(vnp_Params);

        let signData = QueryString.stringify(vnp_Params, { encode: false });
        let hmac = crypto.createHmac('sha512', secretKey);
        let signed = hmac.update(Buffer.from(signData, 'utf-8')).digest('hex');
        vnp_Params['vnp_SecureHash'] = signed;

        let paymentUrl = vnpUrl + '?' + QueryString.stringify(vnp_Params, { encode: false });
        return paymentUrl;
    } catch (error) {
        console.error('Lỗi khi tạo URL thanh toán:', error);
        throw error;
    }
};


export default { verifyReturn, processPayment, verifyChecksum, queryTransaction, refundTransaction, createPaymentUrl }