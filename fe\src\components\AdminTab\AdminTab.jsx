import { Link, useLocation } from "react-router-dom";
import "../../assets/css/common/AdminTab.css";
import { useSelector } from "react-redux";

const AdminTab = () => {
  const location = useLocation();
  const { userInfo } = useSelector((state) => state.user);
  
  // Kiểm tra xem người dùng có vai trò "ADMIN" hay không
  const isAdmin = userInfo?.role?.includes("ADMIN");

  return (
    <div className="admin-tab">
      <h2 className="admin-title">
        <Link to="/admin/dashboard" className="dashboard-link">DashBoard</Link>
      </h2>
      <ul className="admin-menu">
        <li className={`admin-item ${location.pathname === "/admin/dashboard" ? "active" : ""}`}>
          <Link to="/admin/dashboard">DashBoard</Link>
        </li>
        {/* Chỉ hiển thị "Tài khoản" nếu là admin */}
        {isAdmin && (
          <li className={`admin-item ${location.pathname === "/admin/users" ? "active" : ""}`}>
            <Link to="/admin/users">Tài khoản</Link>
          </li>
        )}
        {/* Chỉ hiển thị "Danh mục" nếu là admin */}
        {isAdmin && (
          <li className={`admin-item ${location.pathname === "/admin/categories" ? "active" : ""}`}>
            <Link to="/admin/categories">Danh mục</Link>
          </li>
        )}
        <li className={`admin-item ${location.pathname === "/admin/products" ? "active" : ""}`}>
          <Link to="/admin/products">Sản phẩm</Link>
        </li>
        <li className={`admin-item ${location.pathname === "/admin/orders" ? "active" : ""}`}>
          <Link to="/admin/orders">Đơn hàng</Link>
        </li>
        {isAdmin && (
          <li className={`admin-item ${location.pathname === "/admin/vouchers" ? "active" : ""}`}>
            <Link to="/admin/vouchers">Mã giảm giá</Link>
          </li>
        )}
        {isAdmin && (
          <li className={`admin-item ${location.pathname === "/admin/combos" ? "active" : ""}`}>
            <Link to="/admin/combos">Combo</Link>
          </li>
        )}
        {isAdmin && (
          <li className={`admin-item ${location.pathname === "/admin/services" ? "active" : ""}`}>
            <Link to="/admin/services">Dịch vụ</Link>
          </li>
        )}
      </ul>
    </div>
  );
};

export default AdminTab;