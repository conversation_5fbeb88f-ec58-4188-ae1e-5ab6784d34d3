import Joi from 'joi';
import mongoose from 'mongoose';

const objectId = Joi.string().custom((v, helpers) => {
  if (!mongoose.Types.ObjectId.isValid(v)) return helpers.message('Invalid Object ID');
  return v;
});

const create = {
  body: Joi.object({
    productId: objectId.optional(),
    comboId: objectId.optional(),
    content: Joi.string().max(1000).required(),
    parentId: objectId.allow(null, ''),
  }).xor('productId', 'comboId'),
};

const update = {
  body: Joi.object({
    content: Joi.string().max(1000).required(),
  }),
};

const like = {
  body: Joi.object({
    action: Joi.string().valid('like', 'unlike').required(),
  }),
};

export default { create, update, like }; 