import {
  verifyToken,
  refreshAccessToken,
  createAccessToken,
  createRefreshToken,
} from "./authenticateJWT.js";
import authorizeRoles from "./authorizeRoles.js";
// import getTokenInfoFromRequest from "./getTokenInfoFromRequest.js";
import {
  handleBadRequest,
  handleNotFound,
  handleServerErrors,
} from "./errorHandlers.js";
import validate from "./validates.js";

// Xuất các hàm
export {
  handleBadRequest,
  handleNotFound,
  handleServerErrors,
  verifyToken,
  refreshAccessToken,
  createAccessToken,
  createRefreshToken,
  authorizeRoles,
  // getTokenInfoFromRequest,
  validate,
};