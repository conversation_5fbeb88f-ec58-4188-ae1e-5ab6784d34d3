import Combo from '../models/combo.model.js';
import NotFoundError from '../utils/response/NotFoundError.js';
import BadRequestError from '../utils/response/BadRequestError.js';
import pagination from '../helper/pagination.js';

const search = async (filters = {}, options = {}) => {
  const { page = 1, pageSize = 10 } = options;
  const { offset, limit } = pagination(page, pageSize);
  const sort = options.sort || { updatedAt: -1 };

  const rows = await Combo.find(filters)
    .sort(sort)
    .skip(offset)
    .limit(limit)
    .populate({ path: 'products.productId', select: 'name price thumbnail' })
    .populate({ path: 'products.variantId', select: 'price optionIndex' })
    .populate({ path: 'services.serviceId', select: 'name price duration' });

  const count = await Combo.countDocuments(filters);
  return { count, rows };
};

const create = async (data) => {
  try {
    return await Combo.create(data);
  } catch (error) {
    throw new BadRequestError(error.message);
  }
};

const findById = async (id) => {
  return await Combo.findOne({ _id: id, isDeleted: false })
    .populate({ path: 'products.productId', select: 'name price thumbnail' })
    .populate({ path: 'products.variantId', select: 'price optionIndex' })
    .populate({ path: 'services.serviceId', select: 'name price duration' });
};

const update = async (id, data = {}) => {
  try {
    const combo = await Combo.findOne({ _id: id, isDeleted: false });
    if (!combo) {
      throw new NotFoundError('Combo not found');
    }
    Object.assign(combo, data);
    await combo.save();
    return combo;
  } catch (error) {
    throw new BadRequestError(error.message);
  }
};

const softDelete = async (id) => {
  const combo = await Combo.findOne({ _id: id, isDeleted: false });
  if (!combo) {
    throw new NotFoundError('Combo not found');
  }
  combo.isDeleted = true;
  await combo.save();
  return true;
};

const updateRating = async (comboId, avgRating, ratingCount) => {
  await Combo.findByIdAndUpdate(comboId, {
    ratingAvg: avgRating,
    ratingCount,
  });
};

export default { search, create, findById, update, softDelete, updateRating }; 