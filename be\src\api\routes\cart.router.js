import express from 'express';
import validate from '../middlewares/validates.js';
import { cartController } from '../controllers/index.js';
import cartValidation from '../validations/cart.validation.js';
import { verifyToken } from '../middlewares/authenticateJWT.js';
import authorizeRoles from '../middlewares/authorizeRoles.js';
import { ROLES } from '../constants/role.js';

const cartRouter = express.Router();

cartRouter.get(
  '/',
  verifyToken,
  cartController.getCart
);
cartRouter.post(
  '/',
  verifyToken,
  validate(cartValidation.addCartItem),
  cartController.addCartItem
);
cartRouter.patch(
  '/update-item-variant',
  verifyToken,
  validate(cartValidation.updateCartItemVariant),
  cartController.updateCartItemVariant
);
cartRouter.patch(
  '/',
  verifyToken,
  validate(cartValidation.updateCartItem),
  cartController.updateCartItem
);
cartRouter.delete(
  '/',
  verifyToken,
  validate(cartValidation.deleteCartItems),
  cartController.deleteCartItems
);

export default cartRouter;
