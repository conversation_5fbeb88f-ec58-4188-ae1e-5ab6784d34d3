import { HttpStatusCode } from 'axios';
import { cartService } from '../services/index.js';

const getCart = async (req, res) => {

  const data = await cartService.getCart(req);
  if (data.status === HttpStatusCode.Ok) {
    return res.status(data.status).send(data.data);
  }

  return res.status(data.status).send(data);
};

const addCartItem = async (req, res) => {
  const data = await cartService.addCartItem(req);
  if (data.status === HttpStatusCode.Ok) {
    return res.status(data.status).send(data.data);
  }

  return res.status(data.status).send(data);
};

const updateCartItem = async (req, res) => {
  const data = await cartService.updateCartItem(req);
  if (data.status === HttpStatusCode.Ok) {
    return res.status(data.status).send(data.data);
  }

  return res.status(data.status).send(data);
};

const updateCartItemVariant = async (req, res) => {
  const data = await cartService.updateCartItemVariant(req);
  if (data.status === HttpStatusCode.Ok) {
    return res.status(data.status).send(data.data);
  }

  return res.status(data.status).send(data);
};

const deleteCartItems = async (req, res) => {
  const data = await cartService.deleteCartItems(req);
  if (data.status === HttpStatusCode.Ok) {
    return res.status(data.status).send(data.data);
  }

  return res.status(data.status).send(data);
};

export default {
  getCart,
  addCartItem,
  updateCartItem,
  deleteCartItems,
  updateCartItemVariant,
};
