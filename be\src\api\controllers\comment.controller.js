import commentService from '../services/comment.service.js';

export default {
  create: async (req, res) => {
    const result = await commentService.createComment(req);
    res.status(result.status).json(result);
  },
  list: async (req, res) => {
    const result = await commentService.getComments(req);
    res.status(result.status).json(result);
  },
  replies: async (req, res) => {
    const result = await commentService.getReplies(req);
    res.status(result.status).json(result);
  },
  update: async (req, res) => {
    const result = await commentService.updateComment(req);
    res.status(result.status).json(result);
  },
  delete: async (req, res) => {
    const result = await commentService.deleteComment(req);
    res.status(result.status).json(result);
  },
  like: async (req, res) => {
    const result = await commentService.likeComment(req);
    res.status(result.status).json(result);
  },
}; 