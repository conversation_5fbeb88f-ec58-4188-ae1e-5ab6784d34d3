import Variant from '../models/variant.model.js';

const findByProductId = async (productId, showHidden = true) => {
  const baseQuery = { productId };

  if (!showHidden) {
    baseQuery.isHide = false;
  }

  const variant = await Variant.find(baseQuery);

  return variant;
};

const findById = async (id) => await Variant.findById(id);

const create = async (data) => {
  const variants = await Variant.create(data);
  return variants;
};

const deleteByProductId = async (productId) =>
  await Variant.deleteMany({ productId });

const deleteMany = async (ids) =>
  await Variant.deleteMany({ _id: { $in: ids } });

const findByIdAndUpdate = async (id, data) => {
  const variant = await Variant.findByIdAndUpdate(id, data, { new: true });
  return variant;
};

export default {
  create,
  findById,
  deleteByProductId,
  findByProductId,
  deleteMany,
  findByIdAndUpdate,
};
