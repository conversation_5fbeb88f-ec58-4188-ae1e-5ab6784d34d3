import { reviewRepository, productRepository, orderRepository, comboRepository } from '../repositories/index.js';
import BadRequestError from '../utils/response/BadRequestError.js';
import NotFoundError from '../utils/response/NotFoundError.js';
import ResponseDataSuccess from '../utils/response/ResponseDataSuccess.js';
import InternalServerError from '../utils/response/InternalServerError.js';

const createReview = async (req) => {
  try {
    const { productId = null, comboId = null, rating, comment } = req.body;
    const userId = req.user?.userId;

    // Prevent duplicate review
    const existed = await reviewRepository.findByUserAndProduct(userId, productId, comboId);
    if (existed) {
      return new BadRequestError('Bạn đã đánh giá mục nà<PERSON>');
    }

    // Check if user purchased item
    let purchased = false;
    if (productId) {
      purchased = await orderRepository.hasUserPurchasedProduct(userId, productId);
    } else if (comboId) {
      purchased = await orderRepository.hasUserPurchasedCombo
        ? await orderRepository.hasUserPurchasedCombo(userId, comboId)
        : true; // fallback
    }
    if (!purchased) {
      return new BadRequestError('Bạn cần mua trước khi đánh giá');
    }

    const review = await reviewRepository.create({ productId, comboId, userId, rating, comment });

    // Re-calculate rating stats
    const { avgRating, total } = await reviewRepository.aggregateAvgRating({ productId, comboId });
    if (productId) {
      await productRepository.updateRating(productId, avgRating, total);
    } else if (comboId) {
      await comboRepository.updateRating(comboId, avgRating, total);
    }

    return new ResponseDataSuccess(review);
  } catch (error) {
    return new InternalServerError(error.message);
  }
};

const getReviews = async (req) => {
  try {
    const filters = {};
    const { productId, comboId, userId } = req.query;
    if (productId) filters.productId = productId;
    if (comboId) filters.comboId = comboId;
    if (userId) filters.userId = userId;

    const res = await reviewRepository.search(filters, req.query);
    return new ResponseDataSuccess(res);
  } catch (error) {
    return new InternalServerError(error.message);
  }
};

const getReviewById = async (req) => {
  try {
    const { id } = req.params;
    const review = await reviewRepository.findById(id);
    if (!review || review.isDeleted) return new NotFoundError('Review not found');
    return new ResponseDataSuccess(review);
  } catch (error) {
    return new InternalServerError(error.message);
  }
};

const updateReview = async (req) => {
  try {
    const { id } = req.params;
    const review = await reviewRepository.findById(id);
    if (!review || review.isDeleted) return new NotFoundError('Review not found');

    if (String(review.userId) !== String(req.user?.userId)) {
      return new BadRequestError('Bạn không có quyền sửa đánh giá này');
    }

    const updated = await reviewRepository.updateById(id, req.body);

    const { avgRating, total } = await reviewRepository.aggregateAvgRating({ productId: review.productId, comboId: review.comboId });
    if (review.productId) {
      await productRepository.updateRating(review.productId, avgRating, total);
    } else if (review.comboId) {
      await comboRepository.updateRating(review.comboId, avgRating, total);
    }

    return new ResponseDataSuccess(updated);
  } catch (error) {
    return new InternalServerError(error.message);
  }
};

const deleteReview = async (req) => {
  try {
    const { id } = req.params;
    const review = await reviewRepository.findById(id);
    if (!review || review.isDeleted) return new NotFoundError('Review not found');

    if (String(review.userId) !== String(req.user?.userId)) {
      return new BadRequestError('Bạn không có quyền xoá đánh giá này');
    }

    await reviewRepository.softDelete(id);

    const { avgRating, total } = await reviewRepository.aggregateAvgRating({ productId: review.productId, comboId: review.comboId });
    if (review.productId) {
      await productRepository.updateRating(review.productId, avgRating, total);
    } else if (review.comboId) {
      await comboRepository.updateRating(review.comboId, avgRating, total);
    }

    return new ResponseDataSuccess({ message: 'Deleted' });
  } catch (error) {
    return new InternalServerError(error.message);
  }
};

export default {
  createReview,
  getReviews,
  getReviewById,
  updateReview,
  deleteReview,
}; 