import AuthorApi from "./baseAPI/AuthorBaseApi";
import * as constants from "../constants/index.js";

class OrderApi {
    constructor() {
        this.url = "/api/v1/order";
    }

    createOrder = async (body) => {
        return AuthorApi.post(`${this.url}/create-order`, body);
    };

    createPaymentUrl = async (amount, bankCode, language) => {
        const body = {
            amount: amount,
            bankCode: bankCode,
            language: language,
        };
        return AuthorApi.post(`${this.url}/create_payment_url`, body);
    };

    getOrderList = async (status, query = "", page = 1, pageSize = 10, payload = {}) => {
        const {
            paymentMethod = "",
            paymentStatus = "",
            shippingMethod = "",
            province = "",
            startDate = "",
            endDate = "",
            totalPriceRange = "",
        } = payload;

        const queryParams = [
            `status=${encodeURIComponent(status)}`,
            `query=${encodeURIComponent(query)}`,
            `page=${page}`,
            `pageSize=${pageSize}`,
            paymentMethod ? `paymentMethod=${encodeURIComponent(paymentMethod)}` : "",
            paymentStatus ? `paymentStatus=${encodeURIComponent(paymentStatus)}` : "",
            shippingMethod ? `shippingMethod=${encodeURIComponent(shippingMethod)}` : "",
            province ? `province=${encodeURIComponent(province)}` : "",
            startDate ? `startDate=${encodeURIComponent(startDate)}` : "",
            endDate ? `endDate=${encodeURIComponent(endDate)}` : "",
            totalPriceRange ? `totalPriceRange=${encodeURIComponent(totalPriceRange)}` : "",
        ]
            .filter(Boolean)
            .join("&");

        return AuthorApi.get(`${this.url}/admin/list?${queryParams}`);
    };



    getOrderListByCustomer = async (status, query = "", page = 1, pageSize = 10, payload = {}) => {
        const {
            paymentMethod = "",
            paymentStatus = "",
            shippingMethod = "",
            province = "",
            startDate = "",
            endDate = "",
            totalPriceRange = "",
        } = payload;

        const queryParams = [
            `status=${encodeURIComponent(status)}`,
            `query=${encodeURIComponent(query)}`,
            `page=${page}`,
            `pageSize=${pageSize}`,
            paymentMethod ? `paymentMethod=${encodeURIComponent(paymentMethod)}` : "",
            paymentStatus ? `paymentStatus=${encodeURIComponent(paymentStatus)}` : "",
            shippingMethod ? `shippingMethod=${encodeURIComponent(shippingMethod)}` : "",
            province ? `province=${encodeURIComponent(province)}` : "",
            startDate ? `startDate=${encodeURIComponent(startDate)}` : "",
            endDate ? `endDate=${encodeURIComponent(endDate)}` : "",
            totalPriceRange ? `totalPriceRange=${encodeURIComponent(totalPriceRange)}` : "",
        ]
            .filter(Boolean)
            .join("&");

        return AuthorApi.get(`${this.url}/list?${queryParams}`);
    };


    getOrderDetail = async (orderId) => {
        return AuthorApi.get(`${this.url}/${orderId}`);
    };

    getAllOrder = async () => {
        return AuthorApi.get(`${this.url}/list?status=ALL`);
    };


    updateStatusOrder = async (orderId, status, canceledReason) => {
        const body = {
            status
        };
        // Chỉ thêm canceledReason vào body nếu status là "CANCELED" và canceledReason được cung cấp
        if (status === "CANCELED" && canceledReason) {
            body.canceledReason = canceledReason;
        }

        return AuthorApi.patch(`${this.url}/${orderId}/update-status`, body);
    };


}

export default new OrderApi();