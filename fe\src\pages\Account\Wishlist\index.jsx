import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { fetchWishlist, removeFromWishlist } from "../../../redux/slices/wishlistSlice";
import { Row, Col, Empty, Spin, Button, Popconfirm } from "antd";
import { DeleteOutlined, HeartFilled } from "@ant-design/icons";
import CustomCard from "../../../components/Card";
import ProductApi from "../../../api/ProductApi";
import ComboApi from "../../../api/ComboApi";
import ComboCard from "../../../components/Combo/ComboCard.jsx";

const WishlistPage = () => {
  const dispatch = useDispatch();
  const { wishlistItems, status, loading, error } = useSelector((state) => state.wishlist);
  const { userInfo } = useSelector((state) => state.user);
  const [detailedItems, setDetailedItems] = useState([]);

  useEffect(() => {
    if (userInfo?.accountId) {
      dispatch(fetchWishlist());
    }
  }, [dispatch, userInfo]);

  // Fetch detailed data when wishlistItems change
  useEffect(() => {
    const fetchDetails = async () => {
      if (!wishlistItems || wishlistItems.length === 0) {
        setDetailedItems([]);
        return;
      }

      try {
        const promises = wishlistItems.map(async (item) => {
          // Product case
          if (item.productId) {
            // If product object already populated
            if (typeof item.productId === "object" && item.productId !== null) {
              return { type: "product", data: item.productId };
            }
            // Otherwise fetch product detail by id
            const productRes = await ProductApi.searchProductDetail(item.productId);
            // API returns { product, variants } or { ...product }
            const productData = productRes.product || productRes;
            return { type: "product", data: productData };
          }

          // Combo case
          if (item.comboId) {
            if (typeof item.comboId === "object" && item.comboId !== null) {
              return { type: "combo", data: item.comboId };
            }
            const comboRes = await ComboApi.getComboById(item.comboId);
            const comboData = comboRes.combo || comboRes;
            return { type: "combo", data: comboData };
          }

          return null;
        });

        const results = await Promise.all(promises);
        // Filter out nulls (if any)
        setDetailedItems(results.filter(Boolean));
      } catch (err) {
        console.error("Failed to fetch wishlist item details", err);
      }
    };

    fetchDetails();
  }, [wishlistItems]);

  const handleRemoveFromWishlist = async (item) => {
    try {
      const payload = { item };
      await dispatch(removeFromWishlist(payload)).unwrap();
    } catch (error) {
      console.error("Error removing from wishlist:", error);
    }
  };

  if (status === "loading" && !detailedItems.length) {
    return (
      <div style={{ textAlign: "center", padding: "50px" }}>
        <Spin size="large" />
        <p style={{ marginTop: "16px" }}>Đang tải danh sách yêu thích...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ textAlign: "center", padding: "50px" }}>
        <p style={{ color: "red" }}>Lỗi: {error}</p>
        <Button 
          type="primary" 
          onClick={() => dispatch(fetchWishlist())}
          style={{ marginTop: "16px" }}
        >
          Thử lại
        </Button>
      </div>
    );
  }

  if (!detailedItems || detailedItems.length === 0) {
    return (
      <div style={{ textAlign: "center", padding: "50px" }}>
        <Empty 
          description="Danh sách yêu thích trống" 
          image={<HeartFilled style={{ fontSize: "64px", color: "#f0f0f0" }} />}
        />
      </div>
    );
  }

  return (
    <div>
      <div style={{ marginBottom: "24px" }}>
        <h2>Danh sách yêu thích ({detailedItems.length} sản phẩm)</h2>
      </div>
      
      <Row gutter={[16, 16]}>
        {detailedItems.map((item) => {
          if (item.type === "product") {
            const product = item.data;
            return (
              <Col key={product._id} xs={24} sm={12} md={8} lg={6}>
                <div style={{ position: "relative" }}>
                  <CustomCard product={product} />
                  {/* Remove button */}
                  <Popconfirm
                    title="Xóa khỏi danh sách yêu thích"
                    description="Bạn có chắc chắn muốn xóa sản phẩm này khỏi danh sách yêu thích?"
                    onConfirm={() => handleRemoveFromWishlist({ productId: product._id })}
                    okText="Xóa"
                    cancelText="Hủy"
                    placement="top"
                  >
                    <Button
                      type="text"
                      danger
                      icon={<DeleteOutlined />}
                      style={{
                        position: "absolute",
                        top: "8px",
                        right: "8px",
                        zIndex: 10,
                        background: "rgba(255, 255, 255, 0.9)",
                        border: "none",
                        borderRadius: "50%",
                        width: "32px",
                        height: "32px",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                      }}
                      loading={loading}
                    />
                  </Popconfirm>
                </div>
              </Col>
            );
          }

          if (item.type === "combo") {
            const combo = item.data;
            return (
              <Col key={combo._id} xs={24} sm={12} md={8} lg={6}>
                <div style={{ position: "relative" }}>
                  {/* Using ComboCard for combos */}
                  <ComboCard combo={combo} />
                  {/* Remove button */}
                  <Popconfirm
                    title="Xóa khỏi danh sách yêu thích"
                    description="Bạn có chắc chắn muốn xóa combo này khỏi danh sách yêu thích?"
                    onConfirm={() => handleRemoveFromWishlist({ comboId: combo._id })}
                    okText="Xóa"
                    cancelText="Hủy"
                    placement="top"
                  >
                    <Button
                      type="text"
                      danger
                      icon={<DeleteOutlined />}
                      style={{
                        position: "absolute",
                        top: "8px",
                        right: "8px",
                        zIndex: 10,
                        background: "rgba(255, 255, 255, 0.9)",
                        border: "none",
                        borderRadius: "50%",
                        width: "32px",
                        height: "32px",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                      }}
                      loading={loading}
                    />
                  </Popconfirm>
                </div>
              </Col>
            );
          }

          return null;
        })}
      </Row>
    </div>
  );
};

export default WishlistPage; 