import express from 'express';
import validate from '../middlewares/validates.js';
import { comboController } from '../controllers/index.js';
import comboValidation from '../validations/combo.validation.js';
import { verifyToken } from '../middlewares/authenticateJWT.js';
import authorizeRoles from '../middlewares/authorizeRoles.js';

const comboRouter = express.Router();

comboRouter.get('/', comboController.listCombos);
comboRouter.get('/:id', validate(comboValidation.getById), comboController.getComboById);

// Admin routes
comboRouter.post('/', verifyToken, authorizeRoles('ADMIN'), validate(comboValidation.create), comboController.createCombo);
comboRouter.put('/:id', verifyToken, authorizeRoles('ADMIN'), validate(comboValidation.update), comboController.updateCombo);
comboRouter.delete('/:id', verifyToken, authorizeRoles('ADMIN'), validate(comboValidation.remove), comboController.deleteCombo);

export default comboRouter; 