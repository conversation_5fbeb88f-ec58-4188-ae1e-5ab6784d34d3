import globals from "globals";
import pluginJs from "@eslint/js";

export default [
  {
    languageOptions: {
      globals: {
        ...globals.builtin,
        ...globals.node,
        ...globals.browser,
      },
    },
    ...pluginJs.configs.recommended,
    rules: {
      'eqeqeq': 'warn',                // <PERSON><PERSON><PERSON> cầu sử dụng toán tử so sánh nghiêm ngặt (=== và !==)
      'no-unused-vars': 'warn',        // Cảnh báo khi có biến không sử dụng
      'no-undef': 'warn',              // Cấm việc sử dụng các biến không được định nghĩa
      'prefer-const': 'warn',          // K<PERSON>yến khích việc sử dụng const thay vì let cho các biến không thay đổi
      'arrow-body-style': ['warn', 'as-needed'], // Khuyến khích sử dụng các hàm mũi tên mà không cần cặp {}
      'no-console': 'warn',            // <PERSON><PERSON><PERSON> báo khi sử dụng console
    },
  },
];
