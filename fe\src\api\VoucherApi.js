import AuthorBaseApi from './baseAPI/AuthorBaseApi.js';
import UnauthorBaseApi from './baseAPI/UnauthorBaseApi.js';

const VoucherApi = {
  path: '/api/v1/vouchers',
  // Admin endpoints
  create(data) {
    return AuthorBaseApi.post(`${this.path}`, data);
  },
  list(params) {
    return AuthorBaseApi.get(`${this.path}`, { params });
  },
  update(id, data) {
    return AuthorBaseApi.patch(`${this.path}/${id}`, data);
  },
  remove(id) {
    return AuthorBaseApi.delete(`${this.path}/${id}`);
  },
  // Public validation endpoint
  validate(code, total) {
    return UnauthorBaseApi.get('/api/v1/vouchers/validate', {
      params: { code, total },
    });
  },
};

export default VoucherApi; 