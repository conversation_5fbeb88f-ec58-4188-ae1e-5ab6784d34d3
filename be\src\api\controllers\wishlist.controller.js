import { HttpStatusCode } from 'axios';
import { wishlistService } from '../services/index.js';

const getWishlist = async (req, res) => {
  const data = await wishlistService.getWishlist(req);
  if (data.status === HttpStatusCode.Ok) {
    return res.status(data.status).send(data.data);
  }
  return res.status(data.status).send(data);
};

const addWishlistItem = async (req, res) => {
  const data = await wishlistService.addWishlistItem(req);
  if (data.status === HttpStatusCode.Ok) {
    return res.status(data.status).send(data.data);
  }
  return res.status(data.status).send(data);
};

const deleteWishlistItem = async (req, res) => {
  const data = await wishlistService.deleteWishlistItem(req);
  if (data.status === HttpStatusCode.Ok) {
    return res.status(data.status).send(data.data);
  }
  return res.status(data.status).send(data);
};

export default { getWishlist, addWishlistItem, deleteWishlistItem }; 