import mongoose from 'mongoose';
import { MODELS, COLLECTIONS, SERVICE_STATUS } from '../utils/constants.js';
import { ImageSchema } from './product.model.js';

const serviceSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
      minlength: 3,
      maxlength: 100,
      trim: true,
    },
    description: {
      type: String,
      maxlength: 1000,
      default: null,
    },
    price: {
      type: Number,
      required: true,
      min: 0,
    },
    duration: {
      // duration in minutes
      type: Number,
      required: true,
      min: 5,
    },
    thumbnail: ImageSchema,
    images: [ImageSchema],
    status: {
      type: String,
      enum: Object.values(SERVICE_STATUS),
      default: SERVICE_STATUS.ACTIVE,
    },
    isDeleted: {
      type: Boolean,
      default: false,
    },
  },
  {
    collection: COLLECTIONS.SERVICE,
    timestamps: true,
    toJSON: {
      transform: function (doc, ret) {
        delete ret.__v;
        return ret;
      },
    },
    toObject: {
      transform: function (doc, ret) {
        delete ret.__v;
        return ret;
      },
    },
  }
);

// Unique name for non-deleted services
serviceSchema.index({ name: 1 }, {
  unique: true,
  partialFilterExpression: { isDeleted: false },
});

const Service = mongoose.model(MODELS.SERVICE, serviceSchema, COLLECTIONS.SERVICE);

export default Service; 