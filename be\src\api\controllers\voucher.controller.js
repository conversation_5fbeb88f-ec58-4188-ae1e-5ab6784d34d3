import voucherService from '../services/voucher.service.js';

const createVoucher = async (req, res) => {
  const result = await voucherService.createVoucher(req);
  res.status(result.status).json(result);
};

const getVouchers = async (req, res) => {
  const result = await voucherService.getVouchers(req);
  res.status(result.status).json(result);
};

const getVoucherById = async (req, res) => {
  const result = await voucherService.getVoucherById(req);
  res.status(result.status).json(result);
};

const updateVoucher = async (req, res) => {
  const result = await voucherService.updateVoucher(req);
  res.status(result.status).json(result);
};

const deleteVoucher = async (req, res) => {
  const result = await voucherService.deleteVoucher(req);
  res.status(result.status).json(result);
};

const validateVoucher = async (req, res) => {
  const { code, total } = req.query;
  try {
    const { voucher, discountAmount } = await voucherService.validateAndApply(
      code,
      Number(total || 0),
      []
    );
    res.status(200).json({ voucher, discountAmount });
  } catch (error) {
    res.status(error.status || 400).json(error);
  }
};

export default {
  createVoucher,
  getVouchers,
  getVoucherById,
  updateVoucher,
  deleteVoucher,
  validateVoucher,
}; 