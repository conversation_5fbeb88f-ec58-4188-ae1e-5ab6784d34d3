import Joi from 'joi';
import mongoose from 'mongoose';
import {
  DATE_PERIOD,
  ORDER_STATUS,
  PAYMENT_METHOD,
} from '../utils/constants.js';

const objectId = Joi.string().custom((value, helpers) => {
  if (!mongoose.Types.ObjectId.isValid(value)) {
    return helpers.message('Invalid Object ID');
  }
  return value;
});

const orderItemSchema = Joi.object({
  productId: objectId.optional(),
  comboId: objectId.optional(),
  variantId: objectId.allow(null),
  briefInfo: Joi.object({
    quantity: Joi.number().integer().min(1).required().messages({
      'any.required': 'briefInfo quantity is required',
      'number.base': 'briefInfo quantity must be a number',
      'number.integer': 'briefInfo quantity must be an integer',
      'number.min': 'briefInfo quantity must be at least 1',
    }),
    price: Joi.number().min(0).required().messages({
      'number.min': 'briefInfo price cannot be negative',
      'any.required': 'briefInfo price is required',
    }),
    name: Joi.string().max(150).optional().messages({
      'string.max': 'briefInfo name must be at most 150 characters',
    }),
    variant: Joi.string().max(150).optional().messages({
      'string.max': 'briefInfo variant must be at most 150 characters',
    }),
    type: Joi.string().valid('product', 'combo').required().messages({
      'any.required': 'briefInfo type is required',
      'any.only': 'briefInfo type must be either product or combo',
    }),
  }).required(),
}).xor('productId', 'comboId');

const deliveryAddressValidationRules = {
  fullName: Joi.string().max(100).required().messages({
    'any.required': 'fullName is required',
    'string.max': 'fullName must be at most 100 characters',
  }),
  phone: Joi.string()
    .pattern(/^[0-9]{10,11}$/)
    .required()
    .messages({
      'any.required': 'phone is required',
      'string.pattern.base':
        'phone must contain only digits and be 10-11 characters long',
    }),
  address: Joi.string().max(255).required().messages({
    'any.required': 'address is required',
    'string.max': 'address must be at most 255 characters',
  }),
  province: Joi.object({
    provinceId: Joi.string().required(),
    provinceName: Joi.string().required(),
  }).required(),
  district: Joi.object({
    districtId: Joi.string().required(),
    districtName: Joi.string().required(),
  }).required(),
  ward: Joi.object({
    wardId: Joi.string().required(),
    wardName: Joi.string().required(),
  }).required(),
  note: Joi.string().allow(null, ''),
};

const paymentValidationRules = {
  method: Joi.string()
    .valid(PAYMENT_METHOD.COD, PAYMENT_METHOD.CREDIT_CARD)
    .required()
    .messages({
      'any.required': 'method is required',
    }),
  paymentId: Joi.string().optional(),
  paid: Joi.boolean().required().messages({
    'any.required': 'paid is required',
  }),
  transactionDate: Joi.string().required().messages({
    'any.required': 'transactionDate is required',
  }),
  refunded: Joi.boolean().required().messages({
    'any.required': 'refunded is required',
  }),
};

const shippingValidationRules = {
  method: Joi.string().allow(null, ''),
  fee: Joi.number().allow(null),
};

const search = {
  query: Joi.object({
    query: Joi.string().trim().allow('', null).optional(),
    status: Joi.string().trim().allow('', null).optional(),
    page: Joi.number().min(1).optional(),
    pageSize: Joi.number().min(1).max(200).optional(),
    sort: Joi.string().trim().optional(),
  }),
};

const getOrderById = {
  params: Joi.object({
    orderId: objectId.required(),
  }),
};

const createOrder = {
  body: Joi.object({
    items: Joi.array().items(orderItemSchema).min(1).required().messages({
      'any.required': 'items is required',
      'array.min': 'items must have at least 1 item',
    }),
    deliveryAddress: deliveryAddressValidationRules,
    payment: paymentValidationRules,
    shipping: shippingValidationRules,
    orderedBy: objectId.optional(),
    status: Joi.string()
      .valid(
        ORDER_STATUS.PENDING,
        ORDER_STATUS.APPROVED,
        ORDER_STATUS.DELIVERING,
        ORDER_STATUS.DELIVERED,
        ORDER_STATUS.CANCELED
      )
      .default(ORDER_STATUS.PENDING)
      .messages({
        'any.only': `Status must be one of: ${ORDER_STATUS.PENDING}, ${ORDER_STATUS.APPROVED}, ${ORDER_STATUS.DELIVERING}, ${ORDER_STATUS.DELIVERED}, ${ORDER_STATUS.CANCELED}`,
      }),
    totalPrice: Joi.number().min(1).optional().messages({
      'number.min': 'totalPrice must be at least 1',
    }),
    voucherCode: Joi.string().trim().optional(),
    approvedBy: objectId.allow(null),
    approvedDate: Joi.date().allow(null),
  }),
};

const updateOrderStatus = {
  params: Joi.object({
    orderId: objectId.required(),
  }),
  body: Joi.object({
    status: Joi.string()
      .valid(
        ORDER_STATUS.PENDING,
        ORDER_STATUS.APPROVED,
        ORDER_STATUS.DELIVERING,
        ORDER_STATUS.DELIVERED,
        ORDER_STATUS.CANCELED
      )
      .required()
      .messages({
        'any.only': `Status must be one of: ${ORDER_STATUS.PENDING}, ${ORDER_STATUS.APPROVED}, ${ORDER_STATUS.DELIVERING}, ${ORDER_STATUS.DELIVERED}, ${ORDER_STATUS.CANCELED}`,
        'any.required': 'status is required',
      }),
    canceledReason: Joi.string().allow(null, '').optional(),
  }),
};

const updateOrderStatusAdmin = {
  body: Joi.object({
    orderIds: Joi.array()
      .items(objectId.required())
      .min(1)
      .required()
      .messages({
        'array.min': 'At least one orderId is required',
        'any.required': 'orderIds is required',
      }),
    status: Joi.string()
      .valid(ORDER_STATUS.APPROVED, ORDER_STATUS.DELIVERING)
      .required()
      .messages({
        'any.only': `Status must be one of: ${ORDER_STATUS.APPROVED}, ${ORDER_STATUS.DELIVERING}`,
        'any.required': 'status is required',
      }),
  }),
};

const changeOrderDeliveryAddress = {
  params: Joi.object({
    orderId: objectId.required(),
  }),
  body: Joi.object(deliveryAddressValidationRules),
};

const getMetricsByDateRange = {
  query: Joi.object({
    datePeriod: Joi.string()
      .valid(DATE_PERIOD.WEEK, DATE_PERIOD.MONTH, DATE_PERIOD.YEAR)
      .required()
      .messages({
        'any.only': `Date period must be one of: ${DATE_PERIOD.WEEK}, ${DATE_PERIOD.MONTH}, ${DATE_PERIOD.YEAR}`,
        'any.required': 'datePeriod is required',
      }),
  }),
};

export default {
  search,
  getOrderById,
  createOrder,
  changeOrderDeliveryAddress,
  updateOrderStatus,
  updateOrderStatusAdmin,
  getMetricsByDateRange
};
