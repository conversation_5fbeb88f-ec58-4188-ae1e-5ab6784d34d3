export const breadcrumbMap = {
    "/cart": [
        { label: "Trang chủ", link: "/" },
        { label: "Giỏ hàng" },
    ],
    "/products/:id": [
        { label: "Trang chủ", link: "/" },
        { label: "Sản phẩm" }, // <PERSON><PERSON> thể thay bằng tên sản phẩm thực tế từ params hoặc API
    ],
    "/collections/:slug": [
        { label: "Trang chủ", link: "/" },
        { label: "Bộ sưu tập" }, // <PERSON><PERSON> thể thay bằng tên bộ sưu tập từ slug
    ],
    "/search": [
        { label: "Trang chủ", link: "/" },
        { label: "Tìm kiếm" },
    ],
    "/account/address": [
        { label: "Trang chủ", link: "/" },
        { label: "Tài khoản", link: "/account" },
        { label: "Địa chỉ" },
    ],
    "/account/profile": [
        { label: "Trang chủ", link: "/" },
        { label: "<PERSON><PERSON><PERSON> khoản", link: "/account" },
        { label: "<PERSON><PERSON> sơ" },
    ],
    "/account/change-password": [
        { label: "Trang chủ", link: "/" },
        { label: "Tài khoản", link: "/account" },
        { label: "Đổi mật khẩu" },
    ],
    "/account/order": [
        { label: "Trang chủ", link: "/" },
        { label: "Tài khoản", link: "/account" },
        { label: "Đơn hàng" },
    ],
    "/account/order/:orderId": [
        { label: "Trang chủ", link: "/" },
        { label: "Tài khoản", link: "/account" },
        { label: "Đơn hàng", link: "/account/order" },
        { label: "Chi tiết đơn hàng" }, // Có thể thay bằng mã đơn hàng từ params
    ],
};