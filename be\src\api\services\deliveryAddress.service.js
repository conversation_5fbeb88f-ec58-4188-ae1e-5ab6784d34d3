import { categoryRepository, deliveryAddressRepository } from '../repositories/index.js';
import InternalServerError from '../utils/response/InternalServerError.js';
import ResponseDataSuccess from '../utils/response/ResponseDataSuccess.js';
import NotFoundError from '../utils/response/NotFoundError.js';
import User from '../models/user.model.js';
import { HttpStatusCode } from 'axios';
import { CRUDCode } from '../constants/errorCode.js';

const search = async (userId) => {
    try {
        const res = await deliveryAddressRepository.search(userId);
        return res;
    } catch (error) {
        return ({ status: HttpStatusCode.NotFound, message: error.message });
    }
};

const create = async (userId, data) => {
    try {
        const user = await User.findById(userId);
        if (!user) {
            return ({
                status: HttpStatusCode.NotFound,
                message: 'User not found'
            });
        }

        const deliveryAddress = await deliveryAddressRepository.create(userId, data);
        return deliveryAddress;
    } catch (error) {
        return ({ status: HttpStatusCode.InternalServerError, message: error.message })
    }
};

const update = async (id, dataUpdate) => {
    try {
        const deliveryAddress = await deliveryAddressRepository.findById(id);
        if (!deliveryAddress) {
            return NotFoundError();
        }
        const res = await deliveryAddressRepository.update(id, dataUpdate);
        return ({ status: HttpStatusCode.Created, message: CRUDCode.CC002, data: res.data });
    } catch (error) {
        return ({ status: HttpStatusCode.BadRequest, message: error.message });
    }
};

const deleteDeliveryAddress = async (id) => {
    try {
        const deliveryAddress = await deliveryAddressRepository.findById(id);
        if (!deliveryAddress) {
            return ({ status: HttpStatusCode.NotFound, message: CRUDCode.CC004 })
        }
        const response = await deliveryAddressRepository.deleteDeliveryAddress(id)
        return response
    } catch (error) {
        return new InternalServerError(error);
    }
};

const setDefault = async (id) => {
    try {
        const deliveryAddress = await deliveryAddressRepository.findById(id);
        if (!deliveryAddress) {
            return ({ status: HttpStatusCode.NotFound, message: CRUDCode.CC004 })
        }
        return await deliveryAddressRepository.setDefault(id)
    } catch (error) {
        return new InternalServerError(error);
    }
}

export default { search, create, update, deleteDeliveryAddress, setDefault };
