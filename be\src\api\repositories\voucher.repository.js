import Voucher from '../models/voucher.model.js';

const create = async (data) => await Voucher.create(data);

const findById = async (id) => await Voucher.findById(id);

const findByCode = async (code, onlyActive = true) => {
  const filter = { code: code.toUpperCase() };
  if (onlyActive) filter.isDeleted = false;
  return Voucher.findOne(filter);
};

const updateById = async (id, data) =>
  await Voucher.findByIdAndUpdate(id, data, { new: true });

const softDelete = async (id) =>
  await Voucher.findByIdAndUpdate(id, { isDeleted: true, status: 'HIDDEN' });

const search = async (filters = {}, options = {}) => {
  const { page = 1, pageSize = 10, sort = '-createdAt' } = options;
  const query = Voucher.find({ isDeleted: false, ...filters }).sort(sort);
  const countPromise = Voucher.countDocuments({ isDeleted: false, ...filters });
  const rowsPromise = query.skip((page - 1) * pageSize).limit(pageSize);

  const [count, rows] = await Promise.all([countPromise, rowsPromise]);
  return { count, rows };
};

export default {
  create,
  findById,
  findByCode,
  updateById,
  softDelete,
  search,
}; 