import { objectToQueryString } from "../utils/queryString";
import <PERSON><PERSON>or<PERSON><PERSON> from "./baseAPI/UnauthorBaseApi";
import AuthorApi from "./baseAPI/AuthorBaseApi";

class ComboApi {
  constructor() {
    this.url = "/api/v1/combos";
  }

  // Public list combos
  searchCombos = async (filters = {}) => {
    const queryString = objectToQueryString(filters);
    return (await UnauthorApi.get(`${this.url}?${queryString}`)).data;
  };

  getComboById = async (id) => {
    return (await UnauthorApi.get(`${this.url}/${id}`)).data;
  };

  /* ===== Admin APIs ===== */
  createCombo = async (body) => {
    return (await AuthorApi.post(this.url, body)).data;
  };

  updateCombo = async (id, body) => {
    return (await AuthorApi.put(`${this.url}/${id}`, body)).data;
  };

  deleteCombo = async (id) => {
    return (await AuthorApi.delete(`${this.url}/${id}`)).data;
  };
}

export default new ComboApi(); 