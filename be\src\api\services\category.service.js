import { categoryRepository } from '../repositories/index.js';
import InternalServerError from '../utils/response/InternalServerError.js';
import ResponseDataSuccess from '../utils/response/ResponseDataSuccess.js';
import NotFoundError from '../utils/response/NotFoundError.js';

const search = async (filters) => {
  try {
    const res = await categoryRepository.search(filters);
    return new ResponseDataSuccess(res);
  } catch (error) {
    return new InternalServerError(error);
  }
};

const create = async (req) => {
  try {
    const request = await categoryRepository.create(req.body);
    return new ResponseDataSuccess(request);
  } catch (error) {
    return new InternalServerError(error);
  }
};

const update = async (req) => {
  try {
    const { id } = req.params;

    const updated = await categoryRepository.update(id, req.body);
    return new ResponseDataSuccess(updated);
  } catch (error) {
    return new InternalServerError(error);
  }
};

const deleteCategory = async (req) => {
  try {
    const { id } = req.params;
    const category = await categoryRepository.findById(id);
    if (!category) return new NotFoundError('Category not found');

    const allRelatedCategories = await categoryRepository.findByPath(
      category.path
    );

    categoryRepository.softDelete(allRelatedCategories.map((c) => c._id));
    return new ResponseDataSuccess('Category deleted successfully');
  } catch (error) {
    return new InternalServerError(error);
  }
};

const hasProductsInCategoryTree = async (req) => {
  try {
    const { categoryId } = req.params;
    const request = await categoryRepository.hasProductsInCategoryTree(
      categoryId
    );
    return new ResponseDataSuccess(request);
  } catch (error) {
    return new InternalServerError(error);
  }
};

export default {
  search,
  create,
  update,
  deleteCategory,
  hasProductsInCategoryTree,
};
