.containerFather {
  margin: 20px 0;
}

.container {
  display: flex;
  gap: 30px;
  max-width: 1200px;
  width: 100%;
  margin: auto;
  padding: 20px;
}

.left {
  position: sticky;
  top: 100px;
  width: 50%;
}

.right {
  width: 50%;
  padding: 8px;
}

.title {
  font-size: 24px;
  font-weight: 600;
  text-align: left;
  margin-bottom: 16px;
  color: var(--text-color);
}

.heartIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: 1px solid #d9d9d9;
  border-radius: 50%;
  background: white;
  transition: all 0.3s ease;
  cursor: pointer;
  flex-shrink: 0;
}

.heartIcon:hover {
  border-color: var(--primary-color);
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transform: translateY(-1px);
}

.price {
  margin: 0;
  font-size: 24px;
  color: red;
  font-weight: bold;
}

.oldPrice {
  text-decoration: line-through;
  color: gray;
  margin-right: 10px;
  font-size: 18px;
}

.description {
  margin: 16px 0;
  line-height: 1.6;
  color: var(--text-color);
  font-size: 16px;
}

.productsSection {
  margin-top: 32px;
  padding: 20px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
}

.priceCard {
  margin-bottom: 20px;
  border: 2px solid #52c41a;
  border-radius: 8px;
}

.productCard {
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.productCard:hover {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  border-color: var(--primary-color);
}

.sectionTitle {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
  color: var(--primary-color);
  border-bottom: 2px solid var(--primary-color);
  padding-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.productItem {
  display: flex;
  align-items: center;
  padding: 8px 0;
}

.productItem:last-child {
  border-bottom: none;
}

.productAvatar {
  margin-right: 16px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e8e8e8;
  flex-shrink: 0;
}

.productInfo {
  flex: 1;
  min-width: 0;
}

.productName {
  font-weight: 500;
  margin-bottom: 4px;
  color: var(--text-color);
  font-size: 14px;
}

.productQuantity {
  color: #666;
  font-size: 13px;
  margin-bottom: 2px;
}

.productPrice {
  font-weight: 600;
  color: var(--primary-color);
  font-size: 14px;
  text-align: right;
  flex-shrink: 0;
}

.oldPrice {
  text-decoration: line-through;
  color: #999;
  margin-right: 8px;
  font-size: 16px;
}

.cartAction {
  display: flex;
  gap: 15px;
  margin-top: 32px;
  align-items: center;
  padding: 20px 0;
  border-top: 1px solid #e8e8e8;
}

.quantity {
  display: flex;
  align-items: center;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  overflow: hidden;
  background: white;
}

.quantity button {
  width: 40px;
  height: 40px;
  border: none;
  cursor: pointer;
  font-size: 18px;
  font-weight: bold;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #f5f5f5;
  color: var(--primary-color);
  transition: all 0.3s ease;
}

.quantity button:hover {
  background: var(--primary-color);
  color: white;
}

.quantity button:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.quantityInput {
  width: 60px;
  height: 40px;
  text-align: center;
  border: none;
  outline: none;
  font-size: 16px;
  font-weight: 500;
  background: white;
}

.addToCart {
  position: relative;
  flex: 1;
  height: 50px;
  font-size: 16px;
  font-weight: 600;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 6px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.6s ease-in-out;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.addToCart::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.3);
  transition: left 0.6s ease-out;
}

.addToCart:hover::before {
  left: 100%;
}

.addToCart:hover {
  background-color: var(--white-color) !important;
  color: var(--primary-color) !important;
  border: 1px solid var(--primary-color) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    flex-direction: column;
    gap: 20px;
    padding: 15px;
  }
  
  .left, .right {
    width: 100%;
    position: static;
  }
  
  .title {
    font-size: 20px;
  }
  
  .heartIcon {
    width: 36px;
    height: 36px;
  }
  
  .price {
    font-size: 20px;
  }
  
  .cartAction {
    flex-direction: column;
    gap: 15px;
  }
  
  .addToCart {
    width: 100%;
  }
  
  .productItem {
    flex-direction: column;
    align-items: flex-start;
    text-align: left;
  }
  
  .productAvatar {
    margin-right: 0;
    margin-bottom: 8px;
    align-self: center;
  }
  
  .productPrice {
    text-align: left;
    margin-top: 4px;
  }
  
  .sectionTitle {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .containerFather {
    margin: 10px 0;
  }
  
  .container {
    padding: 10px;
  }
  
  .productsSection {
    padding: 15px;
  }
  
  .title {
    font-size: 18px;
  }
  
  .heartIcon {
    width: 32px;
    height: 32px;
  }
  
  .price {
    font-size: 18px;
  }
  
  .priceCard {
    margin-bottom: 15px;
  }
} 