import React, { useEffect, useState } from 'react';
import { Row, Col, Button, Spin } from 'antd';
import ComboApi from '../../api/ComboApi';
import ComboCard from '../../components/Combo/ComboCard.jsx';

const ListCombos = () => {
  const [combos, setCombos] = useState([]);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [loading, setLoading] = useState(false);

  const pageSize = 8;

  const fetchCombos = async (nextPage = 1, append = false) => {
    setLoading(true);
    try {
      const data = await ComboApi.searchCombos({ page: nextPage, pageSize });
      const newItems = data?.rows || data?.items || data?.rows || [];
      if (append) {
        setCombos((prev) => [...prev, ...newItems]);
      } else {
        setCombos(newItems);
      }
      const totalCount = data?.count ?? newItems.length;
      if (nextPage * pageSize >= totalCount) {
        setHasMore(false);
      } else {
        setHasMore(true);
      }
    } catch (error) {
      console.error('Failed to load combos', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCombos(1);
  }, []);

  const handleLoadMore = () => {
    const next = page + 1;
    setPage(next);
    fetchCombos(next, true);
  };

  return (
    <div style={{ margin: '20px' }}>
      <h2>Gói Combo Sản Phẩm / Dịch Vụ</h2>
      <Row gutter={[16, 16]}>
        {combos.map((combo) => (
          <Col key={combo._id} xs={24} sm={12} md={8} lg={6} xl={6}>
            <ComboCard combo={combo} />
          </Col>
        ))}
      </Row>
      {loading && <Spin style={{ marginTop: 16 }} />}
      {hasMore && !loading && (
        <div style={{ textAlign: 'center', marginTop: 16 }}>
          <Button onClick={handleLoadMore}>Xem thêm</Button>
        </div>
      )}
    </div>
  );
};

export default ListCombos; 