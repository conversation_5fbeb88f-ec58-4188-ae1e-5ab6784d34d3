import { useEffect, useState } from 'react';
import { Table, Button, Modal, Form, Input, InputNumber, Select, Space, Tag, Popconfirm, message, Upload } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, UploadOutlined } from '@ant-design/icons';
import ComboApi from '../../../api/ComboApi';
import ProductApi from '../../../api/ProductApi';

const { Option } = Select;

const ManageCombos = () => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editing, setEditing] = useState(null);
  const [form] = Form.useForm();

  const [products, setProducts] = useState([]);
  const [thumbnailList, setThumbnailList] = useState([]);
  const [imagesList, setImagesList] = useState([]);

  const loadReferenceData = async () => {
    try {
      const prodRes = await ProductApi.getAllProductsAdmin({ pageSize: 100 });
      setProducts(prodRes.rows || prodRes.items || []);
    } catch (err) {
      console.error(err);
    }
  };

  const fetchData = async () => {
    setLoading(true);
    try {
      const res = await ComboApi.searchCombos();
      setData(res.rows || []);
    } catch (err) {
      console.error(err);
      message.error('Lỗi khi tải danh sách combo');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
    loadReferenceData();
  }, []);

  const openCreate = () => {
    setEditing(null);
    form.resetFields();
    setThumbnailList([]);
    setImagesList([]);
    setIsModalOpen(true);
  };

  const openEdit = (record) => {
    setEditing(record);
    const initial = {
      ...record,
      price: record.price,
      discountPrice: record.discountPrice,
      products: record.products?.map((p) => p.productId?._id),
    };
    form.setFieldsValue(initial);
    setThumbnailList(record.thumbnail ? [{ uid: '-1', url: record.thumbnail.url, name: 'thumbnail' }] : []);
    setImagesList(record.images?.map((img, idx)=>({ uid: String(-idx-2), url: img.url, name: 'image'}))||[]);
    setIsModalOpen(true);
  };

  const handleDelete = async (id) => {
    try {
      await ComboApi.deleteCombo(id);
      message.success('Xóa combo thành công');
      fetchData();
    } catch (err) {
      console.error(err);
      message.error('Lỗi khi xóa combo');
    }
  };

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      const payload = {
        name: values.name,
        description: values.description,
        price: values.price,
        discountPrice: values.discountPrice,
        products: values.products?.map((id) => ({ productId: id })),
        status: values.status || 'ACTIVE',
        thumbnail: thumbnailList[0]?.response || (thumbnailList[0]?.url ? {url: thumbnailList[0].url} : undefined),
        images: imagesList.map(f => f.response || { url: f.url }).filter(Boolean),
      };
      if (editing) {
        await ComboApi.updateCombo(editing._id, payload);
        message.success('Cập nhật thành công');
      } else {
        await ComboApi.createCombo(payload);
        message.success('Tạo combo thành công');
      }
      setIsModalOpen(false);
      fetchData();
    } catch (err) {
      console.error(err);
      message.error('Có lỗi xảy ra');
    }
  };

  const columns = [
    {
      title: 'Tên combo',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Giá',
      dataIndex: 'price',
      key: 'price',
      render: (_, record) => (
        record.discountPrice && record.discountPrice < record.price ? (
          <span>
            <del>{record.price.toLocaleString()}đ</del> <b style={{ color: 'green' }}>{record.discountPrice.toLocaleString()}đ</b>
          </span>
        ) : (
          `${record.price.toLocaleString()}đ`
        )
      ),
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      render: (s) => <Tag color={s === 'ACTIVE' ? 'green' : s === 'HIDDEN' ? 'orange' : 'red'}>{s}</Tag>,
    },
    {
      title: 'Thao tác',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Button type="link" icon={<EditOutlined />} onClick={() => openEdit(record)} />
          <Popconfirm title="Chắc chắn xóa?" onConfirm={() => handleDelete(record._id)}>
            <Button type="link" danger icon={<DeleteOutlined />} />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 16 }}>
        <h2>Quản lý Combo</h2>
        <Button icon={<PlusOutlined />} type="primary" onClick={openCreate}>Thêm combo</Button>
      </div>
      <Table rowKey="_id" loading={loading} columns={columns} dataSource={data} pagination={{ pageSize: 10 }} />

      <Modal width={600} title={editing ? 'Chỉnh sửa Combo' : 'Thêm Combo'} open={isModalOpen} onOk={() => form.submit()} onCancel={() => setIsModalOpen(false)} destroyOnClose>
        <Form form={form} layout="vertical" onFinish={handleOk}>
          <Form.Item name="name" label="Tên combo" rules={[{ required: true, message: 'Nhập tên' }]}><Input/></Form.Item>
          <Form.Item name="description" label="Mô tả"><Input.TextArea rows={3}/></Form.Item>
          <Form.Item name="price" label="Giá" rules={[{ required: true }]}><InputNumber min={0} style={{ width: '100%' }}/></Form.Item>
          <Form.Item name="discountPrice" label="Giá khuyến mãi"><InputNumber min={0} style={{ width: '100%' }}/></Form.Item>
          <Form.Item name="products" label="Sản phẩm"><Select mode="multiple" allowClear placeholder="Chọn sản phẩm" options={products.map((p) => ({ value: p._id, label: p.name }))}/></Form.Item>
          <Form.Item name="status" label="Trạng thái" initialValue="ACTIVE"><Select><Option value="ACTIVE">ACTIVE</Option><Option value="HIDDEN">HIDDEN</Option><Option value="DISCONTINUED">DISCONTINUED</Option></Select></Form.Item>
          <Form.Item label="Ảnh đại diện" required>
            <Upload
              action="http://localhost:3000/api/v1/upload/single"
              listType="picture-card"
              maxCount={1}
              fileList={thumbnailList}
              onChange={({fileList})=>setThumbnailList(fileList)}
            >{thumbnailList.length>=1?null:<UploadOutlined/>}</Upload>
          </Form.Item>
          <Form.Item label="Thư viện ảnh">
            <Upload
              action="http://localhost:3000/api/v1/upload/single"
              listType="picture-card"
              multiple
              fileList={imagesList}
              onChange={({fileList})=>setImagesList(fileList)}
            > <UploadOutlined/> </Upload>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ManageCombos; 