import mongoose from 'mongoose';
import { COLLECTIONS, MODELS } from '../utils/constants.js';

const commentSchema = new mongoose.Schema(
  {
    productId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: MODELS.PRODUCT,
      required: function() { return !this.comboId; },
    },
    comboId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: MODELS.COMBO,
      required: function() { return !this.productId; },
    },
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: MODELS.USER,
      required: true,
    },
    content: {
      type: String,
      required: true,
      maxlength: 1000,
    },
    parentId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: MODELS.COMMENT,
      default: null,
    },
    likedBy: {
      type: [mongoose.Schema.Types.ObjectId],
      ref: MODELS.USER,
      default: [],
    },
    isDeleted: {
      type: Boolean,
      default: false,
    },
  },
  {
    collection: MODELS.COMMENT,
    timestamps: true,
    toJSON: {
      transform: (doc, ret) => {
        delete ret.__v;
        delete ret.isDeleted;
        return ret;
      },
    },
    toObject: {
      transform: (doc, ret) => {
        delete ret.__v;
        delete ret.isDeleted;
        return ret;
      },
    },
  }
);

commentSchema.index({ productId: 1, parentId: 1, createdAt: -1 });
commentSchema.index({ comboId: 1, parentId: 1, createdAt: -1 });

const ProductComment = mongoose.model(MODELS.COMMENT, commentSchema, COLLECTIONS.COMMENT);

export default ProductComment; 