.profileContainer {
    padding: 24px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    background-color: #fff;
    margin: 0 auto;
}

.title {
    font-size: 24px;
    font-weight: 600;
    color: #333;
}

.divider {
    margin: 16px 0;
    border-top: 1px solid #f0f0f0;
}

.profileInfo {
    background-color: #f9f9f9;
    padding: 16px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.infoItem {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding: 12px;
    background-color: #fff;
    border-radius: 6px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

.label {
    font-weight: 500;
    font-size: 14px;
    color: #555;
}

.value {
    font-size: 14px;
    color: #000;
}

.imageUploadSection {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 24px;
    height: 100%;
    text-align: center;
}

.avatarWrapper {
    position: relative;
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background-color: #f0f2f5;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    overflow: hidden;
    border: 3px solid #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.avatarWrapper:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.avatarWrapper:hover .uploadHint {
    opacity: 1;
}

.uploadHint {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    font-size: 12px;
    padding: 4px 0;
    text-align: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.userNameDisplay {
    margin-top: 16px;
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.userRoleDisplay {
    margin-top: 4px;
    font-size: 14px;
    color: #888;
    background-color: #f0f2f5;
    padding: 2px 8px;
    border-radius: 12px;
    display: inline-block;
}

.avatarUploader .ant-upload-select {
    width: 120px !important;
    height: 120px !important;
    border-radius: 50% !important;
}

.uploadButton {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
}

.saveButtonRow {
    margin-top: 24px;
    text-align: right;
}

.saveButton {
    background-color: #1890ff;
    border-color: #1890ff;
    color: #fff;
    font-weight: 600;
    padding: 8px 24px;
    font-size: 14px;
    border-radius: 6px;
    transition: background-color 0.3s ease;
}

.saveButton:hover {
    background-color: #40a9ff;
    border-color: #40a9ff;
}