import { comboRepository, productRepository, variantRepository, serviceRepository } from '../repositories/index.js';
import ResponseDataSuccess from '../utils/response/ResponseDataSuccess.js';
import InternalServerError from '../utils/response/InternalServerError.js';
import NotFoundError from '../utils/response/NotFoundError.js';
import BadRequestError from '../utils/response/BadRequestError.js';

// Helper validate combo items existence
const validateComboItems = async (products = [], services = []) => {
  // Validate products and variants existence
  for (const item of products) {
    const product = await productRepository.findById(item.productId, true);
    if (!product) {
      throw new BadRequestError(`Product ${item.productId} not found`);
    }
    if (item.variantId) {
      const variant = await variantRepository.findById(item.variantId);
      if (!variant) {
        throw new BadRequestError(`Variant ${item.variantId} not found`);
      }
      if (variant.productId.toString() !== item.productId) {
        throw new BadRequestError('Variant does not belong to product');
      }
    }
  }
  // Validate services existence
  for (const item of services) {
    const svc = await serviceRepository.findById(item.serviceId);
    if (!svc) {
      throw new BadRequestError(`Service ${item.serviceId} not found`);
    }
  }
};

const search = async (req) => {
  try {
    const { page: pageQuery = 1, pageSize: pageSizeQuery = 10, keyword } = req.query;
    const page = Number(pageQuery);
    const pageSize = Number(pageSizeQuery);
    const filters = { isDeleted: false };
    if (keyword) {
      filters.name = { $regex: keyword, $options: 'i' };
    }
    const combos = await comboRepository.search(filters, { page, pageSize });
    return new ResponseDataSuccess(combos);
  } catch (error) {
    return new InternalServerError(error.message);
  }
};

const create = async (req) => {
  try {
    const { name, description, price, discountPrice, products, services, thumbnail, images, status } = req.body;

    await validateComboItems(products || [], services || []);

    const combo = await comboRepository.create({
      name,
      description,
      price,
      discountPrice,
      products,
      services,
      thumbnail,
      images,
      status,
    });

    return new ResponseDataSuccess(combo);
  } catch (error) {
    if (
      error instanceof BadRequestError ||
      error instanceof NotFoundError
    ) {
      return error;
    }
    return new InternalServerError(error.message);
  }
};

const findById = async (req) => {
  try {
    const { id } = req.params;
    const combo = await comboRepository.findById(id);
    if (!combo) {
      return new NotFoundError('Combo not found');
    }
    return new ResponseDataSuccess(combo);
  } catch (error) {
    return new InternalServerError(error.message);
  }
};

const update = async (req) => {
  try {
    const { id } = req.params;
    const { products, services } = req.body;

    if (products || services) {
      await validateComboItems(products || [], services || []);
    }

    const combo = await comboRepository.update(id, req.body);

    return new ResponseDataSuccess(combo);
  } catch (error) {
    if (
      error instanceof BadRequestError ||
      error instanceof NotFoundError
    ) {
      return error;
    }
    return new InternalServerError(error.message);
  }
};

const remove = async (req) => {
  try {
    const { id } = req.params;
    await comboRepository.softDelete(id);
    return new ResponseDataSuccess({ message: 'Deleted combo successfully' });
  } catch (error) {
    if (error instanceof NotFoundError) {
      return error;
    }
    return new InternalServerError(error.message);
  }
};

export default { search, create, findById, update, remove }; 