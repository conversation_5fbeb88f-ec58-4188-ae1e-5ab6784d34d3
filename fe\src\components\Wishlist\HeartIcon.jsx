// no React import needed (using JSX with modern bundler)
import { HeartOutlined, HeartFilled, LoadingOutlined } from "@ant-design/icons";
import { useDispatch, useSelector } from "react-redux";
import {
  addToWishlist,
  removeFromWishlist,
  fetchWishlist,
  updateWishlistItems,
} from "../../redux/slices/wishlistSlice";
import PropTypes from 'prop-types';

const HeartIcon = ({ product = null, combo = null }) => {
  const dispatch = useDispatch();
  const { wishlistItems, loading } = useSelector((state) => state.wishlist);

  const isFavorited = wishlistItems.some((item) => {
    // Combo case
    if (combo) {
      const comboId = item.comboId?._id || item.comboId;
      return comboId === combo._id;
    }

    // Product case (default) - chỉ check productId, không check variantId
    const itemProductId =
      typeof item.productId === "object" ? item.productId._id : item.productId;
    
    return itemProductId === product._id;
  });

  const handleClick = async () => {
    if (loading) return; // Prevent multiple clicks while loading
    
    let itemPayload = {};
    if (combo) {
      itemPayload.comboId = combo._id;
    } else {
      itemPayload.productId = product._id;
    }
    const payload = { item: itemPayload };
    
    // Optimistic update - cập nhật state ngay lập tức
    const currentItems = [...wishlistItems];
    if (isFavorited) {
      // Remove item optimistically
      const updatedItems = currentItems.filter(item => {
        if (combo) {
          const comboId = item.comboId?._id || item.comboId;
          return comboId !== combo._id;
        }
        const itemProductId = typeof item.productId === "object" ? item.productId._id : item.productId;
        return itemProductId !== product._id;
      });
      dispatch(updateWishlistItems(updatedItems));
    } else {
      // Add item optimistically - chỉ lưu ID
      const newItem = { ...itemPayload };
      dispatch(updateWishlistItems([...currentItems, newItem]));
    }
    
    try {
      if (isFavorited) {
        await dispatch(removeFromWishlist(payload)).unwrap();
      } else {
        await dispatch(addToWishlist(payload)).unwrap();
      }
    } catch (error) {
      console.error('Wishlist operation failed:', error);
      // Revert optimistic update on error
      dispatch(fetchWishlist());
    }
  };

  return (
    <div
      onClick={handleClick}
      style={{ 
        display: "flex", 
        width: "100%", 
        height: "50px", 
        justifyContent: "center", 
        alignItems: "center", 
        cursor: loading ? "not-allowed" : "pointer", 
        fontSize: 18,
        opacity: loading ? 0.6 : 1,
        transition: "opacity 0.2s ease"
      }}
    >
      {loading ? (
        <LoadingOutlined style={{ color: "#1890ff" }} />
      ) : isFavorited ? (
        <HeartFilled style={{ color: "#f5222d" }} />
      ) : (
        <HeartOutlined />
      )}
    </div>
  );
};

HeartIcon.propTypes = {
  product: PropTypes.object,
  combo: PropTypes.object,
};

export default HeartIcon; 