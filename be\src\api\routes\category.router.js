import express from 'express';
import { categoryController } from '../controllers/index.js';
import validate from '../middlewares/validates.js';
import categoryValidation from '../validations/category.validation.js';
import { verifyToken } from '../middlewares/authenticateJWT.js';
import authorizeRoles from '../middlewares/authorizeRoles.js';
import { ROLES } from '../constants/role.js';

const categoryRouter = express.Router();

categoryRouter.get('/', categoryController.search);
categoryRouter.get(
  '/has-product-in-category-tree/:categoryId',
  verifyToken,
  authorizeRoles(ROLES.EMPLOYEE, ROLES.ADMIN),
  categoryController.hasProductsInCategoryTree
);
categoryRouter.post(
  '/',
  verifyToken,
  authorizeRoles(ROLES.EMPLOYEE, ROLES.ADMIN),
  validate(categoryValidation.create),
  categoryController.create
);
categoryRouter.patch(
  '/:id',
  verifyToken,
  authorizeRoles(ROLES.EMPLOYEE, ROLES.ADMIN),
  validate(categoryValidation.update),
  categoryController.update
);
categoryRouter.delete(
  '/:id',
  verifyToken,
  authorizeRoles(ROLES.ADMIN),
  validate(categoryValidation.deleteValidation),
  categoryController.delete
);

export default categoryRouter;
