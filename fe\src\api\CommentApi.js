import UnauthorApi from './baseAPI/UnauthorBaseApi.js';
import AuthorApi from './baseAPI/AuthorBaseApi.js';

class CommentApi {
  constructor() {
    this.url = '/api/v1/comments';
  }

  listComments = (params) => UnauthorApi.get(this.url, { params });

  listReplies = (params) => UnauthorApi.get(`${this.url}/replies`, { params });

  create = (data) => AuthorApi.post(this.url, data);

  update = (id, data) => AuthorApi.put(`${this.url}/${id}`, data);

  remove = (id) => AuthorApi.delete(`${this.url}/${id}`);

  toggleLike = (id) => AuthorApi.patch(`${this.url}/${id}/like`);
}

export default new CommentApi(); 