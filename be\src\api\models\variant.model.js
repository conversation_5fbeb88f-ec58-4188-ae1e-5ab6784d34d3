import mongoose from 'mongoose';
import { COLLECTIONS, MODELS } from '../utils/constants.js';

const variantSchema = new mongoose.Schema(
  {
    sku: {
      type: String,
    },
    productId: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
    },
    optionIndex: {
      type: [Number],
    },
    price: {
      type: Number,
      default: 0,
      required: true,
    },
    quantity: {
      type: Number,
      default: 0,
      required: true,
    },
    isDefault: {
      type: Boolean,
      default: false,
    },
  },
  {
    collection: COLLECTIONS.VARIANT,
    timestamps: false,
    toJSON: {
      transform: function (doc, ret) {
        delete ret.__v;
        return ret;
      },
    },
    toObject: {
      transform: function (doc, ret) {
        delete ret.__v;
        return ret;
      },
    },
  }
);

const Variant = mongoose.model(
  MODELS.VARIANT,
  variantSchema,
  COLLECTIONS.VARIANT
);

export default Variant;
