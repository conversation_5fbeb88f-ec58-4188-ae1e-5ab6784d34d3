import { configureStore } from "@reduxjs/toolkit";
import addressReducer from "./reducers/addressReducer";
import cartReDucer from "./slices/cartSlice";
import productReducer from "./slices/productSlice";
import user from "./slices/authSlice";
import orderReDucer from "./slices/orderSlice"
import categoryReducer from "./slices/cateSlice"
import wishlistReducer from "./slices/wishlistSlice"
import notificationReducer from './slices/notificationSlice';

const store = configureStore({
  reducer: {
    addresses: addressReducer,
    user: user,
    cart: cartReDucer,
    product: productReducer,
    order: orderReDucer,
    categoryReducer: categoryReducer,
    wishlist: wishlistReducer,
    notifications: notificationReducer
  },
});

export default store;
