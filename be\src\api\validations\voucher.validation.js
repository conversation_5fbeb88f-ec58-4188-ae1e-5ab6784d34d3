import Joi from 'joi';
import mongoose from 'mongoose';
import { VOUCHER_TYPE, VOUCHER_STATUS } from '../utils/constants.js';

const objectId = Joi.string().custom((value, helpers) => {
  if (!mongoose.Types.ObjectId.isValid(value)) return helpers.message('Invalid Object ID');
  return value;
});

// Custom validation cho dates
const dateValidation = {
  startDate: Joi.date()
    .min('now')
    .messages({
      'date.min': '<PERSON><PERSON><PERSON> bắt đầu không thể là ngày trong quá khứ'
    }),
  endDate: Joi.date()
    .greater(Joi.ref('startDate'))
    .messages({
      'date.greater': '<PERSON><PERSON><PERSON> kết thúc phải sau ngày bắt đầu'
    })
};

const createVoucher = {
  body: Joi.object({
    code: Joi.string().max(50).required(),
    type: Joi.string()
      .valid(VOUCHER_TYPE.PERCENT, VOUCHER_TYPE.AMOUNT)
      .required(),
    value: Joi.number().positive().required(),
    status: Joi.string().valid(...Object.values(VOUCHER_STATUS)).optional(),
    conditions: Joi.object({
      minOrderValue: Joi.number().min(0),
      usageLimit: Joi.number().integer().min(0),
      startDate: dateValidation.startDate.required(),
      endDate: dateValidation.endDate.required(),
      productIds: Joi.array().items(objectId),
    }).required(),
  }),
};

const updateVoucher = {
  body: Joi.object({
    code: Joi.string().max(50),
    type: Joi.string().valid(VOUCHER_TYPE.PERCENT, VOUCHER_TYPE.AMOUNT),
    value: Joi.number().positive(),
    conditions: Joi.object({
      minOrderValue: Joi.number().min(0),
      usageLimit: Joi.number().integer().min(0),
      startDate: dateValidation.startDate,
      endDate: dateValidation.endDate,
      productIds: Joi.array().items(objectId),
    }),
    status: Joi.string().valid(...Object.values(VOUCHER_STATUS)),
  }),
};

export default { createVoucher, updateVoucher }; 