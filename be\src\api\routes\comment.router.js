import express from 'express';
import commentController from '../controllers/comment.controller.js';
import validate from '../middlewares/validates.js';
import commentValidation from '../validations/comment.validation.js';
import { verifyToken } from '../middlewares/authenticateJWT.js';

const router = express.Router();

router.post('/', verifyToken, validate(commentValidation.create), commentController.create);
router.get('/', commentController.list);
router.get('/replies', commentController.replies);
router.put('/:id', verifyToken, validate(commentValidation.update), commentController.update);
router.delete('/:id', verifyToken, commentController.delete);
router.patch('/:id/like', verifyToken, commentController.like);

export default router; 