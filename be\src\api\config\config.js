import path, { dirname } from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

const __fileName = fileURLToPath(import.meta.url);
const __dirname = dirname(__fileName);

// Debug: Log the resolved path
const envPath = path.join(__dirname, '../../../.env');

dotenv.config({ path: envPath });

export default {
  client: process.env.CLIENT || 5173,
  port: process.env.DB_PORT || 3000,
  username: process.env.DB_USERNAME || '',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_DATABASE || '',
  host: process.env.DB_HOST || 'localhost',
  GMAIL: {
    USER: process.env.GMAIL_USER || '',
    PASS: process.env.GMAIL_PASS || '',
  },
  GOOGLE: {
    clientId: process.env.GOOGLE_CLIENT_ID || '',
    clientSecret: process.env.GOOGLE_CLIENT_SECRET || '',
  },
  // dbUri: process.env.DB_URI,
  dbUri: process.env.MONGO_URI,
  sessionSecret: process.env.SESSION_SECRET || "",
  activationSecret: process.env.ACTIVATION_SECRET || "",
  activattionTokenLife: process.env.ACTIVATION_TOKEN_LIFE || "",
  vnp_TmnCode: process.env.VNP_TMNCODE || "",
  vnp_HashSecret: process.env.VNP_HASHSECRET || "",
  vnp_Url: process.env.VNP_URL || "",
  vnp_Api: process.env.VNP_API || "",
  vnp_ReturnUrl: process.env.VNP_RETURNURL || "",
  resetPasswordTokenLife: 5,
  ghtp_token: process.env.GHTK_TOKEN,
  ghtp_url: process.env.GHTP_URL,
  cloudinary: {
    cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
    api_key: process.env.CLOUDINARY_API_KEY,
    api_secret: process.env.CLOUDINARY_API_SECRET,
  },
  accessTokenSecret: process.env.ACCESS_TOKEN_SECRET,
  refreshTokenSecret: process.env.REFRESH_TOKEN_SECRET,
  accessTokenLife: process.env.ACCESS_TOKEN_LIFE,
  refreshTokenLife: process.env.REFRESH_TOKEN_LIFE
};
