import ProductComment from '../models/comment.model.js';

const create = async (data) => ProductComment.create(data);

const findById = async (id) => ProductComment.findById(id);

const updateById = async (id, data) => ProductComment.findByIdAndUpdate(id, data, { new: true });

const softDelete = async (id) => ProductComment.findByIdAndUpdate(id, { isDeleted: true });

const searchRoots = async (productId, comboId, page = 1, pageSize = 10) => {
  const query = { parentId: null, isDeleted: false };
  if (productId) query.productId = productId;
  if (comboId) query.comboId = comboId;
  const rowsPromise = ProductComment.find(query)
    .sort({ createdAt: -1 })
    .skip((page - 1) * pageSize)
    .limit(pageSize)
    .populate({ path: 'userId', select: 'fullName avatar' });
  const countPromise = ProductComment.countDocuments(query);
  const [rows, count] = await Promise.all([rowsPromise, countPromise]);
  return { rows, count };
};

const searchReplies = async (parentId, page = 1, pageSize = 10) => {
  const query = { parentId, isDeleted: false };
  const rowsPromise = ProductComment.find(query)
    .sort({ createdAt: 1 })
    .skip((page - 1) * pageSize)
    .limit(pageSize)
    .populate({ path: 'userId', select: 'fullName avatar' });
  const countPromise = ProductComment.countDocuments(query);
  const [rows, count] = await Promise.all([rowsPromise, countPromise]);
  return { rows, count };
};

const countReplies = async (parentId) => ProductComment.countDocuments({ parentId, isDeleted: false });

const toggleLike = async (id, userId) => {
  const comment = await ProductComment.findById(id);
  if (!comment) return null;
  const index = comment.likedBy.findIndex((u) => String(u) === String(userId));
  if (index === -1) {
    comment.likedBy.push(userId);
  } else {
    comment.likedBy.splice(index, 1);
  }
  await comment.save();
  return comment;
};

export default {
  create,
  findById,
  updateById,
  softDelete,
  searchRoots,
  searchReplies,
  countReplies,
  toggleLike,
}; 