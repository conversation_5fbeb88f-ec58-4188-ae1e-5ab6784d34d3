import { VOUCHER_STATUS, VOUCHER_TYPE } from '../utils/constants.js';
import { voucherRepository } from '../repositories/index.js';
import BadRequestError from '../utils/response/BadRequestError.js';
import NotFoundError from '../utils/response/NotFoundError.js';
import ResponseDataSuccess from '../utils/response/ResponseDataSuccess.js';
import InternalServerError from '../utils/response/InternalServerError.js';
import notificationService from './notification.service.js';
import { NOTIFICATION_TYPE } from '../utils/constants.js';
import { getIO } from '../../socket/index.js';

const validateDates = (startDate, endDate) => {
  const now = new Date();
  const start = new Date(startDate);
  const end = new Date(endDate);

  if (start < now) {
    throw new BadRequestError('<PERSON><PERSON><PERSON> bắt đầu không thể là ngày trong quá khứ');
  }

  if (end <= start) {
    throw new BadRequestError('<PERSON><PERSON><PERSON> kết thúc phải sau ngày bắt đầu');
  }
};

const createVoucher = async (req) => {
  try {
    const data = req.body;

    // Normalize code to uppercase
    data.code = data.code.toUpperCase();

    // Validate dates
    if (data.conditions?.startDate && data.conditions?.endDate) {
      validateDates(data.conditions.startDate, data.conditions.endDate);
    }

    // Kiểm tra đã tồn tại voucher code hay chưa (kể cả isDeleted)
    const existed = await voucherRepository.findByCode(data.code, false);
    if (existed) {
      if (existed.isDeleted) {
        // revive: cập nhật record cũ
        const revived = await voucherRepository.updateById(existed._id, {
          ...data,
          isDeleted: false,
          usedCount: 0,
        });
        return new ResponseDataSuccess(revived);
      }
      return new BadRequestError('Voucher code already exists');
    }

    const voucher = await voucherRepository.create(data);

    // Broadcast promo notification
    notificationService.create(
      {
        title: `Mã giảm giá mới: ${voucher.code}`,
        message: `Giảm ${voucher.type === VOUCHER_TYPE.PERCENT ? voucher.value + '%': voucher.value + 'đ'} cho đơn hàng của bạn!`,
        type: NOTIFICATION_TYPE.PROMO,
        userId: null,
      },
      getIO()
    );

    return new ResponseDataSuccess(voucher);
  } catch (error) {
    if (error instanceof BadRequestError) {
      return error;
    }
    return new InternalServerError(error.message);
  }
};

const getVouchers = async (req) => {
  try {
    const filters = {};
    const options = req.query;
    const res = await voucherRepository.search(filters, options);
    return new ResponseDataSuccess(res);
  } catch (error) {
    return new InternalServerError(error.message);
  }
};

const getVoucherById = async (req) => {
  try {
    const { id } = req.params;
    const voucher = await voucherRepository.findById(id);
    if (!voucher) return new NotFoundError('Voucher not found');
    return new ResponseDataSuccess(voucher);
  } catch (error) {
    return new InternalServerError(error.message);
  }
};

const updateVoucher = async (req) => {
  try {
    const { id } = req.params;
    const data = req.body;

    // Validate dates if they are being updated
    if (data.conditions?.startDate && data.conditions?.endDate) {
      validateDates(data.conditions.startDate, data.conditions.endDate);
    } else if (data.conditions?.startDate || data.conditions?.endDate) {
      // If only one date is being updated, get the other from existing voucher
      const existingVoucher = await voucherRepository.findById(id);
      if (!existingVoucher) return new NotFoundError('Voucher not found');

      const startDate = data.conditions?.startDate || existingVoucher.conditions.startDate;
      const endDate = data.conditions?.endDate || existingVoucher.conditions.endDate;
      validateDates(startDate, endDate);
    }

    const voucher = await voucherRepository.updateById(id, data);
    if (!voucher) return new NotFoundError('Voucher not found');
    return new ResponseDataSuccess(voucher);
  } catch (error) {
    if (error instanceof BadRequestError) {
      return error;
    }
    return new InternalServerError(error.message);
  }
};

const deleteVoucher = async (req) => {
  try {
    const { id } = req.params;
    await voucherRepository.softDelete(id);
    return new ResponseDataSuccess({ message: 'Deleted' });
  } catch (error) {
    return new InternalServerError(error.message);
  }
};

const validateAndApply = async (code, orderTotal, items = []) => {
  const voucher = await voucherRepository.findByCode(code);
  if (!voucher) throw new BadRequestError('Voucher not found');

  // Check status
  if (voucher.status !== VOUCHER_STATUS.ACTIVE) {
    throw new BadRequestError('Voucher inactive');
  }
  // Check date range
  const now = new Date();
  if (voucher.conditions.startDate && now < voucher.conditions.startDate) {
    throw new BadRequestError('Voucher not started');
  }
  if (voucher.conditions.endDate && now > voucher.conditions.endDate) {
    throw new BadRequestError('Voucher expired');
  }
  // Check usage limit
  if (
    voucher.conditions.usageLimit &&
    voucher.conditions.usageLimit > 0 &&
    voucher.usedCount >= voucher.conditions.usageLimit
  ) {
    throw new BadRequestError('Voucher usage limit reached');
  }
  // Check min order total
  if (
    voucher.conditions.minOrderValue &&
    orderTotal < voucher.conditions.minOrderValue
  ) {
    throw new BadRequestError('Order value too low for voucher');
  }
  // TODO: product restriction check later

  let discountAmount = 0;
  if (voucher.type === VOUCHER_TYPE.PERCENT) {
    discountAmount = (orderTotal * voucher.value) / 100;
  } else {
    discountAmount = voucher.value;
  }
  // Cap at orderTotal
  discountAmount = Math.min(discountAmount, orderTotal);
  return { voucher, discountAmount };
};

const increaseUsedCount = async (voucherId) => {
  await voucherRepository.updateById(voucherId, { $inc: { usedCount: 1 } });
};

export default {
  createVoucher,
  getVouchers,
  getVoucherById,
  updateVoucher,
  deleteVoucher,
  validateAndApply,
  increaseUsedCount,
}; 