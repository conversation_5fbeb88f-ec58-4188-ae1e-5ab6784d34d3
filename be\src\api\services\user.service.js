import { HttpStatusCode } from "axios";
import Account from "../models/account.model.js";
import accountRepository from "../repositories/account.repository.js";
import { userRepository } from "../repositories/index.js";
import InternalServerError from "../utils/response/InternalServerError.js";
import ResponseDataSuccess from "../utils/response/ResponseDataSuccess.js";
import { removeVietnameseTones } from "../helper/removeVietnameseTones.js";
import ForbiddenError from "../utils/response/ForbiddenError.js";
import NotFoundError from "../utils/response/NotFoundError.js";
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

// Since we are using ES modules, __dirname is not available directly.
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const search = async (filters, options) => {
    try {
        const page = parseInt(options.page) || 1;
        const pageSize = parseInt(options.pageSize) || 10;
        const offset = (page - 1) * pageSize;

        const sortCriteria = {};
        if (options.sortField && options.sortOrder) {
            const sortOrder = options.sortOrder === "asc" ? 1 : -1;
            sortCriteria[options.sortField === "fullName" ? "user.fullName" : options.sortField] = sortOrder;
        } else {
            sortCriteria["createdAt"] = 1;
        }

        // Xây dựng match stage cho filters
        let matchStage = {};
        if (filters.search) {
            const searchText = filters.search.trim();
            const searchPattern = removeVietnameseTones(searchText);
            matchStage.$or = [
                { "user.fullName": { $regex: searchPattern, $options: "i" } },
                { email: { $regex: searchPattern, $options: "i" } }
            ];
        }
        if (filters.role) {
            matchStage.role = { $in: filters.role.split(",") };
        }
        if (filters.isBlocked) {
            matchStage["status.isBlocked"] = filters.isBlocked === "true";
        }

        // Aggregation pipeline
        const pipeline = [
            // Join với collection User
            {
                $lookup: {
                    from: "ph_users", // Tên collection của User (thường là "users" nếu model là "User")
                    localField: "userId",
                    foreignField: "_id",
                    as: "user"
                }
            },
            // Unwind để biến mảng user thành object đơn
            {
                $unwind: { path: "$user", preserveNullAndEmptyArrays: true }
            },
            // Áp dụng filters
            { $match: matchStage },
            // Sort
            { $sort: sortCriteria },
            // Pagination
            { $skip: offset },
            { $limit: pageSize },
            // Project để định dạng output
            {
                $project: {
                    _id: 1,
                    email: 1,
                    status: 1,
                    role: 1,
                    createdAt: 1,
                    "user.fullName": 1
                }
            }
        ];

        const rows = await Account.aggregate(pipeline);


        // Đếm tổng số document
        const totalResult = await Account.aggregate([
            ...pipeline.slice(0, 3), // Lấy đến $match
            { $count: "total" }
        ]);
        const total = totalResult[0]?.total || 0;

        // Định dạng lại dữ liệu để khớp với frontend
        const processedRows = rows.map(row => ({
            _id: row._id,
            email: row.email,
            status: row.status,
            role: row.role,
            createdAt: row.createdAt,
            user: { fullName: row.user?.fullName || "" }
        }));

        // Debug
        return { rows: processedRows, total };
    } catch (error) {
        console.error("Search error:", error);
        throw new Error("Failed to search users");
    }
};

const getByAccountId = async (accountId) => {
    try {
        const account = await Account.findById(accountId).select("_id email role").populate('userId')
        return ({ status: HttpStatusCode.Ok, data: account })
    } catch (error) {
        console.log(error);
        return new InternalServerError(error);
    }
}

const updateAvatar = async (accountId, newAvatarPath) => {
    try {
        const account = await Account.findById(accountId);
        if (!account) {
            return new NotFoundError("Tài khoản không tồn tại.");
        }
        const user = await userRepository.findById(account.userId);
        if (!user) {
            return new NotFoundError("Thông tin người dùng không tồn tại.");
        }
        // Delete the old avatar file if it exists
        if (user.avatar && user.avatar !== newAvatarPath) {
            const oldAvatarPath = path.join(
                __dirname,
                "../../public",
                user.avatar
            );
            if (fs.existsSync(oldAvatarPath)) {
                fs.unlinkSync(oldAvatarPath);
            }
        }

        // Update the user with the new avatar path
        const updatedUser = await userRepository.updateAvatar(
            account.userId,
            newAvatarPath
        );
        return new ResponseDataSuccess({
            message: "Cập nhật ảnh đại diện thành công",
            data: updatedUser,
        });
    } catch (error) {
        return new InternalServerError(error.message);
    }
};

const update = async (accountId, data, user) => {
    try {
        // user can only update their own profile
        if (user.accountId !== accountId) {
            return new ForbiddenError("You are not allowed to update this profile");
        }

        const updatedUser = await userRepository.update(user.userId, data);
        return new ResponseDataSuccess(updatedUser);
    } catch (error) {
        return new InternalServerError(error);
    }
}

const getDashboardNewUser = async () => {
    try {
        const currentDate = new Date();
        const sevenDaysAgo = new Date();
        sevenDaysAgo.setDate(currentDate.getDate() - 7);

        const roles = ['USER', 'ADMIN', 'EMPLOYEE'];

        const roleStatsPromises = roles.map(async (role) => {
            const total = await Account.countDocuments({ role: { $in: [role] } });
            const last7Days = await Account.countDocuments({
                role: { $in: [role] },
                createdAt: { $gte: sevenDaysAgo }
            });

            return {
                role,
                total,
                last7Days
            };
        });
        const roleStats = await Promise.all(roleStatsPromises);
        const result = roleStats.reduce((acc, curr) => {
            acc[curr.role] = {
                total: curr.total,
                last7Days: curr.last7Days
            };
            return acc;
        }, {});

        return {
            status: 200,
            data: result
        };
    } catch (error) {
        throw error;
    }
};

export default { search, getByAccountId, getDashboardNewUser, update, updateAvatar };