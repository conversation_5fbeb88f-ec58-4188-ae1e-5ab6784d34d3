import mongoose from 'mongoose';
import { COLLECTIONS, MODELS } from '../utils/constants.js';

const reviewSchema = new mongoose.Schema(
  {
    productId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: MODELS.PRODUCT,
      required: function() { return !this.comboId; },
    },
    comboId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: MODELS.COMBO,
      required: function() { return !this.productId; },
    },
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: MODELS.USER,
      required: true,
    },
    rating: {
      type: Number,
      required: true,
      min: 1,
      max: 5,
    },
    comment: {
      type: String,
      maxlength: 1000,
      default: null,
    },
    isDeleted: {
      type: Boolean,
      default: false,
    },
  },
  {
    collection: MODELS.REVIEW,
    timestamps: true,
    toJSON: {
      transform: function (doc, ret) {
        delete ret.__v;
        delete ret.updatedAt;
        delete ret.isDeleted;
        return ret;
      },
    },
    toObject: {
      transform: function (doc, ret) {
        delete ret.__v;
        delete ret.updatedAt;
        delete ret.isDeleted;
        return ret;
      },
    },
  }
);

// Each user can review product/combo once
reviewSchema.index({ productId: 1, userId: 1 }, { unique: true, partialFilterExpression: { productId: { $exists: true } } });
reviewSchema.index({ comboId: 1, userId: 1 }, { unique: true, partialFilterExpression: { comboId: { $exists: true } } });

reviewSchema.index({ productId: 1 });
reviewSchema.index({ comboId: 1 });

const Review = mongoose.model(MODELS.REVIEW, reviewSchema, COLLECTIONS.REVIEW);

export default Review; 