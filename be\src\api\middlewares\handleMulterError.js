import multer from 'multer';

const handleUploadErrors = (err, req, res, next) => {
  if (err instanceof multer.MulterError) {
    if (err.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).send('Number of files is more than allowed.');
    }
    if (err.code === 'LIMIT_FILE_SIZE') {
      return res
        .status(400)
        .send('File size is too large. Max size allowed is 5MB.');
    }
    if (err.code === 'LIMIT_UNEXPECTED_FILE') {
      return res.status(400).json({
        error: {
          status: 400,
          message: `Invalid field: ${err.field}`,
        },
      });
    }
    return res.status(400).json({
      error: {
        status: 400,
        message: err.message,
      },
    });
  }
  next(err);
};

export default handleUploadErrors;
