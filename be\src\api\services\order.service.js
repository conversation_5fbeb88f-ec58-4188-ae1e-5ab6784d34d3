import mongoose from 'mongoose';
import {
  startOfDay,
  endOfDay,
  subDays,
  startOfMonth,
  endOfMonth,
  startOfYear,
  endOfYear,
  subMonths,
  subYears,
} from 'date-fns';
import { ROLES } from '../constants/role.js';
import {
  cartRepository,
  orderRepository,
  productRepository,
  variantRepository,
  comboRepository,
} from '../repositories/index.js';
import { DATE_PERIOD, ORDER_STATUS } from '../utils/constants.js';
import pick from '../utils/pick.js';
import BadRequestError from '../utils/response/BadRequestError.js';
import InternalServerError from '../utils/response/InternalServerError.js';
import NotFoundError from '../utils/response/NotFoundError.js';
import ResponseDataSuccess from '../utils/response/ResponseDataSuccess.js';
import ForbiddenError from '../utils/response/ForbiddenError.js';
import Order from '../models/order.model.js';
import User from '../models/user.model.js';
import voucherService from './voucher.service.js';
// import vnpayService from './vnpay.service.js';

const search = async (req) => {
  try {
    const filters = pick(req.query, ['query', 'status']);
    const options = pick(req.query, ['page', 'pageSize', 'sort']);

    const { query, status } = filters;
    const queryFilters = {};
    const { role, userId } = req.user;

    if (query) {
      queryFilters.$or = [
        { id: { $regex: query, $options: 'i' } }, // Regex for id
        { 'deliveryAddress.phone': { $regex: query, $options: 'i' } }, // Regex for phone
        {
          $text: {
            $search: `"${query}"`,
            $caseSensitive: false,
            $diacriticSensitive: false,
          }, // Text search for name and fullName
        },
      ];
    }

    // Chỉ lọc trạng thái nếu status khác "ALL"
    if (status && status !== 'ALL') {
      queryFilters.status = status;
    }

    if (!role.includes(ROLES.EMPLOYEE)) {
      queryFilters.orderedBy = userId;
    }

    const res = await orderRepository.search(queryFilters, options);
    return new ResponseDataSuccess(res);
  } catch (error) {
    return new InternalServerError(error);
  }
};

const searchAdmin = async (req) => {
  try {
    // Extract filters and options from request query
    const filters = pick(req.query, [
      'query',
      'status',
      'paymentMethod',
      'paymentStatus',
      'startDate',
      'endDate',
      'totalPriceRange',
      'province',
    ]);
    const options = pick(req.query, ['page', 'pageSize', 'sort']);

    const {
      query,
      status,
      paymentMethod,
      paymentStatus,
      startDate,
      endDate,
      totalPriceRange,
    } = filters;

    const queryFilters = {};

    // Handle text search for query (id, phone, or name/fullName)
    if (query) {
      queryFilters.$or = [
        { id: { $regex: query, $options: 'i' } },
        { 'deliveryAddress.phone': { $regex: query, $options: 'i' } },
        {
          $text: {
            $search: `"${query}"`,
            $caseSensitive: false,
            $diacriticSensitive: false,
          },
        },
      ];
    }

    // Filter by order status if provided and not "ALL"
    if (status && status !== 'ALL') {
      queryFilters.status = status;
    }

    // Filter by payment method
    if (paymentMethod) {
      queryFilters['payment.method'] = paymentMethod;
    }

    // Filter by payment status (paid/unpaid)
    if (paymentStatus) {
      queryFilters['payment.paid'] = paymentStatus === 'true' ? true : false;
    }

    // Filter by province
    if (filters.province) {
      queryFilters['deliveryAddress.province.provinceName'] = filters.province;
    }

    // Filter by creation date range
    if (startDate || endDate) {
      queryFilters.createdAt = {};
      if (startDate) {
        queryFilters.createdAt.$gte = new Date(startDate);
      }
      if (endDate) {
        // Set endDate to end of day for full range coverage
        const end = new Date(endDate);
        end.setHours(23, 59, 59, 999);
        queryFilters.createdAt.$lte = end;
      }
    }

    // Filter by total price range (format: "min-max")
    if (totalPriceRange) {
      const [minPrice, maxPrice] = totalPriceRange.split('-').map(Number);
      queryFilters.totalPrice = {};
      if (!isNaN(minPrice)) {
        queryFilters.totalPrice.$gte = minPrice;
      }
      if (!isNaN(maxPrice)) {
        queryFilters.totalPrice.$lte = maxPrice;
      }
    }

    // Fetch orders using the repository's search method
    const { count, rows } = await orderRepository.search(queryFilters, {
      page: parseInt(options.page) || 1,
      pageSize: parseInt(options.pageSize) || 10,
      sort: options.sort ? options.sort : null, // Use repository's default if not provided
    });

    // Prepare response with pagination details
    const page = parseInt(options.page) || 1;
    const pageSize = parseInt(options.pageSize) || 10;
    const response = {
      data: rows,
      pagination: {
        page,
        pageSize,
        total: count,
        totalPages: Math.ceil(count / pageSize),
      },
    };

    return new ResponseDataSuccess(response);
  } catch (error) {
    // Handle any errors and return InternalServerError
    return new InternalServerError(error);
  }
};

const getOrderById = async (req) => {
  try {
    const { orderId } = req.params;
    const { role, userId } = req.user;
    const order = await orderRepository.findOrderById(orderId);
    if (!order) {
      return new NotFoundError('Order not found');
    }
    // Check if user is not employee or admin and not the owner of the order, deny access
    if (
      (role.includes(ROLES.USER)) &&
      order.orderedBy._id.toString() !== userId.toString()
    ) {
      return new BadRequestError('Access denied');
    }

    return new ResponseDataSuccess(order);
  } catch (error) {
    return new InternalServerError(error);
  }
};

const createOrder = async (req) => {
  try {
    const { items, shipping } = req.body;

    // Assign orderedBy to current user
    req.body.orderedBy = req.user.userId;

    // Check if items are available
    await Promise.all(
      items.map(async (item) => {
        if (item.comboId) {
          // It's a combo item
          const combo = await comboRepository.findById(item.comboId);
          if (!combo) {
            throw new NotFoundError(`Combo ${item.comboId} not found`);
          }
          
          if (combo.status !== 'ACTIVE') {
            throw new BadRequestError(`Combo ${item.comboId} is not available`);
          }

          // Assign combo info to briefInfo
          item.briefInfo.name = combo.name;
          item.briefInfo.image = combo.thumbnail?.url;
          item.briefInfo.type = 'combo';
        } else {
          // It's a product item
          const product = await productRepository.findById(item.productId);
          if (!product) {
            throw new NotFoundError(`Product ${item.productId} not found`);
          }

          if (item.variantId) {
            // It's a variant product
            const variant = await variantRepository.findById(item.variantId);
            if (
              !variant ||
              variant.productId.toString() !== item.productId
            ) {
              throw new NotFoundError(
                `Variant ${item.variantId} not found for product ${item.productId}`
              );
            }

            if (variant.quantity < item.briefInfo.quantity) {
              throw new BadRequestError(
                `Variant ${item.variantId} of Product ${item.productId} is out of stock`
              );
            }

            variant.quantity -= item.briefInfo.quantity;
            await variant.save();

            // Update quantity of product if variant is default
            if (variant.isDefault) {
              product.quantity = variant.quantity;
              await product.save();
            }

            // Assign image, name of product to item briefInfo
            item.briefInfo.name = product.name;
            // Overridden variant image (if have) and assign variant (name) to item briefInfo
            // Check if variant doenst have image, assign product thumbnail
            item.briefInfo.image =
              product.options[0].values[variant.optionIndex[0]].image?.url ||
              product.thumbnail.url;
            // Assign variant name to item briefInfo
            // Check if product has 2 options, assign both options to variant name
            item.briefInfo.variant =
              product.options.length === 2
                ? `${product.options[0].values[[variant.optionIndex[0]]].name}, ${
                    product.options[1].values[[variant.optionIndex[1]]].name
                  }`
                : `${product.options[0].values[[variant.optionIndex[0]]].name}`;
            item.briefInfo.type = 'product';
          } else {
            // It's a simple product
            if (product.quantity < item.briefInfo.quantity) {
              throw new BadRequestError(
                `Product ${item.productId} is out of stock`
              );
            }

            product.quantity -= item.briefInfo.quantity;
            await product.save();

            // Assign image, name of product to item briefInfo
            item.briefInfo.name = product.name;
            item.briefInfo.image = product.thumbnail.url;
            item.briefInfo.type = 'product';
          }
        }
      })
    );

    // Calculate order total and apply voucher if any
    const subTotal = items.reduce(
        (total, item) => total + item.briefInfo.price * item.briefInfo.quantity,
        0
    );
    let totalPrice = subTotal + (shipping.fee || 0);

    if (req.body.voucherCode) {
      try {
        const { voucher, discountAmount } = await voucherService.validateAndApply(
          req.body.voucherCode,
          totalPrice,
          items
        );
        totalPrice -= discountAmount;
        req.body.voucher = {
          voucherId: voucher._id,
          code: voucher.code,
          discount: discountAmount,
        };
        // Update usage count (non-blocking)
        voucherService.increaseUsedCount(voucher._id).catch(() => {});
      } catch (err) {
        return err;
      }
    }

    req.body.totalPrice = totalPrice;

    // Create order here
    const order = await orderRepository.create(req.body);

    await cartRepository.deleteCartItems(req.user.accountId, items);

    return new ResponseDataSuccess(order);
  } catch (error) {
    if (error instanceof NotFoundError) {
      return error;
    }
    if (error instanceof BadRequestError) {
      return error;
    }
    return new InternalServerError(error.message);
  }
};

const changeDeliveryAddress = async (req) => {
  try {
    const { orderId } = req.params;
    const { userId } = req.user;
    const deliveryAddress = req.body;

    const order = await orderRepository.findOrderById(orderId);
    if (!order) {
      return new NotFoundError('Order not found');
    }

    if (order.orderedBy.toString() !== userId) {
      return new BadRequestError('Access denied');
    }

    if (order.status !== ORDER_STATUS.PENDING) {
      return new BadRequestError('Cannot change delivery address');
    }

    order.deliveryAddress = deliveryAddress;
    const updatedOrder = await order.save();
    return new ResponseDataSuccess(updatedOrder);
  } catch (error) {
    return new InternalServerError(error);
  }
};

const updateOrderStatus = async (req) => {
  try {
    const { orderId } = req.params;
    const { status, canceledReason } = req.body;
    const { role, userId } = req.user;

    const order = await orderRepository.findOrderById(orderId);
    // Check if order exists
    if (!order) {
      return new NotFoundError('Order not found');
    }

    // Check if user is not employee or admin and not the owner of the order, deny access
    if (
      !role.includes(ROLES.EMPLOYEE) &&
      !role.includes(ROLES.ADMIN) &&
      userId !== order.orderedBy._id.toString()
    ) {
      return new ForbiddenError('Access denied');
    }

    // Check if user is NOT employee or admin and IS OWNER of the order
    if (
      !role.includes(ROLES.EMPLOYEE) &&
      !role.includes(ROLES.ADMIN) &&
      userId === order.orderedBy._id.toString()
    ) {
      // check if status is APPROVED or DELIVERING
      if (
        status === ORDER_STATUS.APPROVED ||
        status === ORDER_STATUS.DELIVERING
      ) {
        return new ForbiddenError('Access denied');
      }

      // check if status is DELIVERED
      // if (
      //   order.status !== ORDER_STATUS.DELIVERING &&
      //   status === ORDER_STATUS.DELIVERED
      // ) {
      //   return new BadRequestError('Cannot change order status');
      // }
    }

    // Check if status is different
    if (order.status === status) {
      return new BadRequestError('Status is the same');
    }

    // Check if order is already canceled or delivered - cannot change status
    if (
      order.status === ORDER_STATUS.CANCELED ||
      order.status === ORDER_STATUS.DELIVERED
    ) {
      return new BadRequestError('Cannot change order status');
    }

    // Prevent canceling orders that are already confirmed and being delivered
    if (status === ORDER_STATUS.CANCELED) {
      if (order.status === ORDER_STATUS.DELIVERING) {
        return new BadRequestError('Cannot cancel order that is already being delivered');
      }
    }

    if (status === ORDER_STATUS.CANCELED) {
      order.canceledBy = req.user.userId;
      order.canceledReason = canceledReason;
      const { items } = order;
      try {
        await Promise.all(
          items.map(async (item) => {
            let product = null;
            if (item.productId) {
              product = await productRepository.findById(item.productId);
              if (!product) {
                return;
              }
              if (!item.variantId) {
                product.quantity += item.briefInfo.quantity;
                await product.save();
              }
            }

            if (item.variantId) {
              const variant = await variantRepository.findById(item.variantId);
              if (!variant) {
                return;
              }
              variant.quantity += item.briefInfo.quantity;
              await variant.save();
              if (variant.isDefault) {
                product.quantity = variant.quantity;
                await product.save();
              }
            }
          })
        );
      } catch (error) {
        return new InternalServerError(error);
      }
    }
    order.status = status;
    const updatedOrder = await order.save();

    return new ResponseDataSuccess(updatedOrder);
  } catch (error) {
    return new InternalServerError(error);
  }
};

const updateOrderStatusAdmin = async (req) => {
  try {
    const { orderIds, status } = req.body; // { orderIds: [], status: string }

    // Validate allowed status transitions
    const allowedTransitions = {
      [ORDER_STATUS.PENDING]: [ORDER_STATUS.APPROVED],
      [ORDER_STATUS.APPROVED]: [ORDER_STATUS.DELIVERING],
    };

    // Fetch all orders in one query for efficiency
    const orders = await orderRepository.findOrderByIds(orderIds);
    const foundOrderIds = orders.map((order) => order._id.toString());
    const missingOrderIds = orderIds.filter(
      (id) => !foundOrderIds.includes(id)
    );

    if (missingOrderIds.length > 0) {
      throw new NotFoundError(
        `Orders not found: ${missingOrderIds.join(', ')}`
      );
    }

    const updatedOrders = await Promise.all(
      orders.map(async (order) => {
        // Check current status and allowed transition
        const currentStatus = order.status;
        const validNextStatuses = allowedTransitions[currentStatus] || [];

        if (!validNextStatuses.includes(status)) {
          throw new BadRequestError(
            `Order ${order._id
            }: Cannot transition from ${currentStatus} to ${status}. Allowed: ${validNextStatuses.join(
              ', '
            )}`
          );
        }

        // Check if status is the same
        if (order.status === status) {
          throw new BadRequestError(
            `Order ${order._id}: Status is already ${status}`
          );
        }

        // Update status (no cancellation logic needed as CANCELED is not allowed)
        order.status = status;

        // Save updated order
        return await order.save();
      })
    );

    return new ResponseDataSuccess(updatedOrders);
  } catch (error) {
    if (error instanceof NotFoundError || error instanceof BadRequestError) {
      return error;
    }
    return new InternalServerError(error);
  }
};

// Hàm tính % thay đổi
const calculateChange = (current, previous) => {
  if (previous === 0) return current > 0 ? 100 : 0; // Nếu previous = 0, trả về 100% nếu current > 0
  return (((current - previous) / previous) * 100).toFixed(1);
};

const getMetricsByDateRange = async (req) => {
  try {
    const { datePeriod } = pick(req.query, ['datePeriod']); // 'YEAR', 'MONTH', 'WEEK'

    const now = new Date(); // Ngày hiện tại: 24/03/2025
    let currentStart, currentEnd, previousStart, previousEnd;

    switch (datePeriod) {
      case DATE_PERIOD.WEEK:
        currentEnd = endOfDay(now); // 24/03/2025 23:59:59.999
        currentStart = startOfDay(subDays(now, 6)); // 18/03/2025 00:00:00.000
        previousEnd = endOfDay(subDays(currentStart, 1)); // 17/03/2025 23:59:59.999
        previousStart = startOfDay(subDays(currentStart, 7)); // 11/03/2025 00:00:00.000
        break;
      case DATE_PERIOD.MONTH:
        currentEnd = endOfMonth(now); // 31/03/2025 23:59:59.999
        currentStart = startOfMonth(now); // 01/03/2025 00:00:00.000
        previousEnd = endOfDay(subDays(currentStart, 1)); // 28/02/2025 23:59:59.999
        previousStart = startOfMonth(subMonths(now, 1)); // 01/02/2025 00:00:00.000
        break;
      case DATE_PERIOD.YEAR:
        currentEnd = endOfYear(now); // 31/12/2025 23:59:59.999
        currentStart = startOfYear(now); // 01/01/2025 00:00:00.000
        previousEnd = endOfDay(subDays(currentStart, 1)); // 31/12/2024 23:59:59.999
        previousStart = startOfYear(subYears(now, 1)); // 01/01/2024 00:00:00.000
        break;
    }

    // Query cho current period
    const [currentOrders, currentUsers] = await Promise.all([
      Order.aggregate([
        {
          $match: {
            createdAt: { $gte: currentStart, $lte: currentEnd },
            status: { $ne: 'CANCELED' }, // Không tính order bị hủy
          },
        },
        {
          $group: {
            _id: null,
            totalMoney: { $sum: '$totalPrice' },
            totalOrders: { $sum: 1 },
          },
        },
      ]),
      User.countDocuments({
        createdAt: { $gte: currentStart, $lte: currentEnd },
      }),
    ]);

    // Query cho previous period
    const [previousOrders, previousUsers] = await Promise.all([
      Order.aggregate([
        {
          $match: {
            createdAt: { $gte: previousStart, $lte: previousEnd },
            status: { $ne: 'CANCELED' },
          },
        },
        {
          $group: {
            _id: null,
            totalMoney: { $sum: '$totalPrice' },
            totalOrders: { $sum: 1 },
          },
        },
      ]),
      User.countDocuments({
        createdAt: { $gte: previousStart, $lte: previousEnd },
      }),
    ]);

    // Lấy dữ liệu từ kết quả aggregation
    const currentMoney =
      currentOrders.length > 0 ? currentOrders[0].totalMoney : 0;
    const currentOrderCount =
      currentOrders.length > 0 ? currentOrders[0].totalOrders : 0;
    const currentUserCount = currentUsers || 0;

    const previousMoney =
      previousOrders.length > 0 ? previousOrders[0].totalMoney : 0;
    const previousOrderCount =
      previousOrders.length > 0 ? previousOrders[0].totalOrders : 0;
    const previousUserCount = previousUsers || 0;

    // Tính % thay đổi
    const moneyChange = calculateChange(currentMoney, previousMoney);
    const orderChange = calculateChange(currentOrderCount, previousOrderCount);
    const userChange = calculateChange(currentUserCount, previousUserCount);

    // Response
    const response = {
      incomes: {
        Money: currentMoney,
        Change: parseFloat(moneyChange),
      },
      Orders: {
        Orders: currentOrderCount,
        Change: parseFloat(orderChange),
      },
      Users: {
        Users: currentUserCount,
        Change: parseFloat(userChange),
      },
    };

    return new ResponseDataSuccess(response);
  } catch (error) {
    return new InternalServerError(error);
  }
};

const getOrderMetricsByMonthsInYear = async () => {
  try {
    const now = new Date(); // Ngày hiện tại: 24/03/2025
    const yearStart = startOfYear(now); // 01/01/2025
    const yearEnd = endOfYear(now); // 31/12/2025

    // Aggregation để tính số lượng order và tổng tiền theo tháng
    const orderData = await Order.aggregate([
      {
        $match: {
          createdAt: { $gte: yearStart, $lte: yearEnd },
          status: { $ne: 'CANCELED' }, // Không tính order bị hủy
        },
      },
      {
        $group: {
          _id: { $month: '$createdAt' }, // Nhóm theo tháng (1-12)
          totalOrders: { $sum: 1 },
          totalMoney: { $sum: '$totalPrice' },
        },
      },
      {
        $sort: { _id: 1 }, // Sắp xếp theo tháng tăng dần
      },
    ]);

    // Danh sách tháng để ánh xạ
    const monthNames = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];

    // Tạo mảng đầy đủ 12 tháng, gán giá trị 0 nếu không có dữ liệu
    const orderArray = monthNames.map((name, index) => {
      const monthData = orderData.find((data) => data._id === index + 1);
      return {
        name,
        order: monthData ? monthData.totalOrders : 0,
      };
    });

    const moneyArray = monthNames.map((name, index) => {
      const monthData = orderData.find((data) => data._id === index + 1);
      return {
        name,
        money: monthData ? monthData.totalMoney : 0,
      };
    });

    // Response
    const response = {
      ordersByMonth: orderArray,
      moneyByMonth: moneyArray,
    };

    return new ResponseDataSuccess(response);
  } catch (error) {
    return new InternalServerError(error);
  }
};

export default {
  search,
  searchAdmin,
  createOrder,
  changeDeliveryAddress,
  updateOrderStatus,
  getOrderById,
  updateOrderStatusAdmin,
  getMetricsByDateRange,
  getOrderMetricsByMonthsInYear,
};
